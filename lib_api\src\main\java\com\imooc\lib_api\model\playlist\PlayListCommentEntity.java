package com.imooc.lib_api.model.playlist;

import com.chad.library.adapter.base.entity.SectionEntity;
import com.imooc.lib_api.model.song.MusicCommentBean;

public class PlayListCommentEntity implements SectionEntity {

    private boolean isHeader;
    private String header;
    private MusicCommentBean.CommentsBean data;
	private String count;

	public PlayListCommentEntity(boolean isHeader, String header, String count) {
		this.isHeader = isHeader;
		this.header = header;
		this.count = count;
	}

	public PlayListCommentEntity(MusicCommentBean.CommentsBean commentsBean) {
		this.isHeader = false;
		this.data = commentsBean;
	}

	@Override
	public boolean isHeader() {
		return isHeader;
	}

	public String getHeader() {
		return header;
	}

	public MusicCommentBean.CommentsBean getData() {
		return data;
	}

	public String getCount() {
		return count;
	}
}
