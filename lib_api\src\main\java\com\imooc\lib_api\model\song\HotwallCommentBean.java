package com.imooc.lib_api.model.song;

import java.util.List;

/**
 * {
 *     "code": 200,
 *     "message": "获取成功",
 *     "data": [
 *         {
 *             "id": 3363513212,
 *             "threadId": "R_SO_4_17339569",
 *             "content": "山径被黑夜染得彻底, 月光映着独行者身披得甲衣。山林中偶尔传来簌簌树枝的响动, 他警惕的单手紧握着佩剑, 快速行进的步伐将浓密的草丛踏出一条蹊径。夜莺突然说了话, 发出低沉的鸟鸣, 那双猩红的眼像夜色中的红星。",
 *             "time": 1593750297076,
 *             "simpleUserInfo": {
 *                 "userId": 89767287,
 *                 "nickname": "<PERSON><PERSON><PERSON>",
 *                 "avatar": "http://p2.music.126.net/za70IrITjqHihKKjJYw4YA==/3284241232743165.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 17,
 *             "replyCount": 6,
 *             "simpleResourceInfo": {
 *                 "songId": 17339569,
 *                 "threadId": "R_SO_4_17339569",
 *                 "name": "Jedi",
 *                 "artists": [
 *                     {
 *                         "id": 96275,
 *                         "name": "Melpo Mene"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/aZ8X_Eem5N4Ur8uRDjkRpQ==/109951163367958068.jpg",
 *                 "song": {
 *                     "name": "Jedi",
 *                     "id": 17339569,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 96275,
 *                             "name": "Melpo Mene",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 20,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 8,
 *                     "v": 32,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 1596767,
 *                         "name": "Bring the Lions Out",
 *                         "picUrl": "http://p4.music.126.net/aZ8X_Eem5N4Ur8uRDjkRpQ==/109951163367958068.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951163367958068",
 *                         "pic": 109951163367958060
 *                     },
 *                     "dt": 262986,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 10522166,
 *                         "vd": -2
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 6313317,
 *                         "vd": -2
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 4208893,
 *                         "vd": -2
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 10,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 1,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 679014,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1219622400000
 *                 },
 *                 "privilege": {
 *                     "id": 17339569,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 68,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 587672537,
 *             "threadId": "R_SO_4_494410758",
 *             "content": "你要明白 你跟這個人已經沒有以後了這輩子 都沒法有了 ​​​",
 *             "time": 1508162647354,
 *             "simpleUserInfo": {
 *                 "userId": 559995250,
 *                 "nickname": "Forever_we__",
 *                 "avatar": "http://p2.music.126.net/buEBLQEZcNnYrBc1n04A5A==/109951165493515774.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 9569,
 *             "replyCount": 90,
 *             "simpleResourceInfo": {
 *                 "songId": 494410758,
 *                 "threadId": "R_SO_4_494410758",
 *                 "name": "why do i love you",
 *                 "artists": [
 *                     {
 *                         "id": 12322199,
 *                         "name": "eel."
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/bxPaAgAc6OnPg-GHBHDl8A==/19207368625891367.jpg",
 *                 "song": {
 *                     "name": "why do i love you",
 *                     "id": 494410758,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 12322199,
 *                             "name": "eel.",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": null,
 *                     "fee": 0,
 *                     "v": 9,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 35865105,
 *                         "name": "why do i love you",
 *                         "picUrl": "http://p4.music.126.net/bxPaAgAc6OnPg-GHBHDl8A==/19207368625891367.jpg",
 *                         "tns": [],
 *                         "pic_str": "19207368625891367",
 *                         "pic": 19207368625891370
 *                     },
 *                     "dt": 77923,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 3119064,
 *                         "vd": 7596
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 1871456,
 *                         "vd": 10281
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 1247652,
 *                         "vd": 9143
 *                     },
 *                     "a": null,
 *                     "cd": "01",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 0,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1485964800007
 *                 },
 *                 "privilege": {
 *                     "id": 494410758,
 *                     "fee": 0,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 320000,
 *                     "dl": 320000,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 320000,
 *                     "fl": 320000,
 *                     "toast": false,
 *                     "flag": 128,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 3428877783,
 *             "threadId": "R_SO_4_1472925673",
 *             "content": "我们置身于同一片星夜之下, 纵使未曾谋面, 纵使从未交流, 却能在这一瞬间一同品味美丽, 一同因乐感动, 从心而动, 倚梦创幻, 或悦然或黯然或陌然, 大概都是因为这极致抽象的尤物——音乐的力量吧…",
 *             "time": 1598016306819,
 *             "simpleUserInfo": {
 *                 "userId": 413847637,
 *                 "nickname": "凌水浅风-Kanade-",
 *                 "avatar": "http://p2.music.126.net/YlZcqU0ZxcAmPzdL3y8z2Q==/109951164740150738.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 236,
 *             "replyCount": 23,
 *             "simpleResourceInfo": {
 *                 "songId": 1472925673,
 *                 "threadId": "R_SO_4_1472925673",
 *                 "name": "星夜之下",
 *                 "artists": [
 *                     {
 *                         "id": 12275337,
 *                         "name": "MoreanP"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/dn4oTX8GSvgfG63sxnfD7Q==/109951165256635190.jpg",
 *                 "song": {
 *                     "name": "星夜之下",
 *                     "id": 1472925673,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 12275337,
 *                             "name": "MoreanP",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 85,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 8,
 *                     "v": 6,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 94248084,
 *                         "name": "星夜之下，逆光唤醒",
 *                         "picUrl": "http://p4.music.126.net/dn4oTX8GSvgfG63sxnfD7Q==/109951165256635190.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951165256635190",
 *                         "pic": 109951165256635180
 *                     },
 *                     "dt": 222817,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 8915113,
 *                         "vd": -59916
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 5349085,
 *                         "vd": -57331
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 3566071,
 *                         "vd": -55633
 *                     },
 *                     "a": null,
 *                     "cd": "01",
 *                     "no": 2,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 0,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 0
 *                 },
 *                 "privilege": {
 *                     "id": 1472925673,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 64,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 1444372926,
 *             "threadId": "R_SO_4_1357210348",
 *             "content": "“世界上最幸福的事莫过于你喜欢的那个人正好也在喜欢着你”    “我想要追寻你的轨迹, 追逐了很久却发现找不到你, 有一天我回头一看, 发现原来你在我的身后, 追寻我”",
 *             "time": 1554618953652,
 *             "simpleUserInfo": {
 *                 "userId": 420707499,
 *                 "nickname": "贾静雅",
 *                 "avatar": "http://p2.music.126.net/5rCU1wkqLc1j7eR0gqrXVQ==/109951165477489649.jpg",
 *                 "followed": false,
 *                 "userType": 4
 *             },
 *             "likedCount": 2668,
 *             "replyCount": 337,
 *             "simpleResourceInfo": {
 *                 "songId": 1357210348,
 *                 "threadId": "R_SO_4_1357210348",
 *                 "name": "轨迹",
 *                 "artists": [
 *                     {
 *                         "id": 13445507,
 *                         "name": "贾静雅"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/SNiKNGo5NQm8ZVLO27A_mw==/109951163982371196.jpg",
 *                 "song": {
 *                     "name": "轨迹",
 *                     "id": 1357210348,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 13445507,
 *                             "name": "贾静雅",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 90,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 8,
 *                     "v": 8,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 78203033,
 *                         "name": "轨迹",
 *                         "picUrl": "http://p4.music.126.net/SNiKNGo5NQm8ZVLO27A_mw==/109951163982371196.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951163982371196",
 *                         "pic": 109951163982371200
 *                     },
 *                     "dt": 280365,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 11217023,
 *                         "vd": -25600
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 6730231,
 *                         "vd": -23100
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 4486835,
 *                         "vd": -21400
 *                     },
 *                     "a": null,
 *                     "cd": "01",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 0,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 0
 *                 },
 *                 "privilege": {
 *                     "id": 1357210348,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 2,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 5170933920,
 *             "threadId": "R_SO_4_1340677881",
 *             "content": "脖子上顶着高傲的头颅,   骨子里住着谦卑的灵魂,   我们是脚踩在地上的人。",
 *             "time": 1606719088455,
 *             "simpleUserInfo": {
 *                 "userId": 1305967769,
 *                 "nickname": "折扇君",
 *                 "avatar": "http://p2.music.126.net/HGeR1NbjZPEuhC5-ziT2Rw==/109951164435822885.jpg",
 *                 "followed": false,
 *                 "userType": 204
 *             },
 *             "likedCount": 21,
 *             "replyCount": 2,
 *             "simpleResourceInfo": {
 *                 "songId": 1340677881,
 *                 "threadId": "R_SO_4_1340677881",
 *                 "name": "我",
 *                 "artists": [
 *                     {
 *                         "id": 31010647,
 *                         "name": "无边界音乐"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/2bepi13lhHf8qVDoHGPJUg==/109951163734122907.jpg",
 *                 "song": {
 *                     "name": "我",
 *                     "id": 1340677881,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 31010647,
 *                             "name": "无边界音乐",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 90,
 *                     "st": 0,
 *                     "rt": null,
 *                     "fee": 8,
 *                     "v": 45,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 74752067,
 *                         "name": "雨 RAIN RAIN RAIN",
 *                         "picUrl": "http://p4.music.126.net/2bepi13lhHf8qVDoHGPJUg==/109951163734122907.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951163734122907",
 *                         "pic": 109951163734122910
 *                     },
 *                     "dt": 291157,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 11648566,
 *                         "vd": -7400
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 6989157,
 *                         "vd": -4900
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 4659453,
 *                         "vd": -3300
 *                     },
 *                     "a": null,
 *                     "cd": "01",
 *                     "no": 4,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 0,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1545148800000
 *                 },
 *                 "privilege": {
 *                     "id": 1340677881,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 256,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 5171236069,
 *             "threadId": "R_SO_4_1480414391",
 *             "content": "这世界没有事实, 只有认知。你和认知不同的人在一起就会想争吵, 和认知切合的人在一起就容易开心。你可以努力寻找在一起会开心的人, 也可以避开在一起容易争吵人。嗨, 千万别卯足力气去和认知不同人的争论什么, 我怕傻傻的你受伤, 也怕傻傻的你认为这世界不美好。",
 *             "time": 1606746650649,
 *             "simpleUserInfo": {
 *                 "userId": 123421991,
 *                 "nickname": "馒头ZouLiang",
 *                 "avatar": "http://p2.music.126.net/JvKz4ti_73JlhdwfiqB0cw==/109951165322570346.jpg",
 *                 "followed": false,
 *                 "userType": 4
 *             },
 *             "likedCount": 38,
 *             "replyCount": 7,
 *             "simpleResourceInfo": {
 *                 "songId": 1480414391,
 *                 "threadId": "R_SO_4_1480414391",
 *                 "name": "长灯怯风吹·星河不可追",
 *                 "artists": [
 *                     {
 *                         "id": 12275908,
 *                         "name": "馒头"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/5AaNR7Ix0qr178qmmOLbQg==/109951165506235344.jpg",
 *                 "song": {
 *                     "name": "长灯怯风吹·星河不可追",
 *                     "id": 1480414391,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 12275908,
 *                             "name": "馒头",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 75,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 8,
 *                     "v": 8,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 95515307,
 *                         "name": "长灯怯风吹·星河不可追",
 *                         "picUrl": "http://p4.music.126.net/5AaNR7Ix0qr178qmmOLbQg==/109951165506235344.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951165506235344",
 *                         "pic": 109951165506235340
 *                     },
 *                     "dt": 257500,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 10302738,
 *                         "vd": -29207
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 6181660,
 *                         "vd": -26612
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 4121121,
 *                         "vd": -24972
 *                     },
 *                     "a": null,
 *                     "cd": "01",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 0,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 0
 *                 },
 *                 "privilege": {
 *                     "id": 1480414391,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 66,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 5170562891,
 *             "threadId": "R_SO_4_1499076050",
 *             "content": "就算公交上空无一人 司机也会把车开到终点站 我的意思是 你不要因为任何人的离开 而停止原本的生活",
 *             "time": 1606665632950,
 *             "simpleUserInfo": {
 *                 "userId": 1731540193,
 *                 "nickname": "后来晚风吹",
 *                 "avatar": "http://p2.music.126.net/ZYXdZ-qZkY55vTxYgNY43A==/109951165093094322.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 837,
 *             "replyCount": 17,
 *             "simpleResourceInfo": {
 *                 "songId": 1499076050,
 *                 "threadId": "R_SO_4_1499076050",
 *                 "name": "不容",
 *                 "artists": [
 *                     {
 *                         "id": 33694141,
 *                         "name": "一支榴莲"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/mYy8wQteoX12XhYFI_g4tA==/109951165507242980.jpg",
 *                 "song": {
 *                     "name": "不容",
 *                     "id": 1499076050,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 33694141,
 *                             "name": "一支榴莲",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 8,
 *                     "v": 4,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 98891992,
 *                         "name": "不容",
 *                         "picUrl": "http://p3.music.126.net/mYy8wQteoX12XhYFI_g4tA==/109951165507242980.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951165507242980",
 *                         "pic": 109951165507242980
 *                     },
 *                     "dt": 136277,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 5453805,
 *                         "vd": -40845
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 3272301,
 *                         "vd": -38217
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 2181549,
 *                         "vd": -36460
 *                     },
 *                     "a": null,
 *                     "cd": "01",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 0,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1606665600000
 *                 },
 *                 "privilege": {
 *                     "id": 1499076050,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 64,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 3597947383,
 *             "threadId": "R_SO_4_1493108703",
 *             "content": "这首太浪漫了 有种在disco舞厅蹦完迪拉你去山顶看星星一样的浪漫",
 *             "time": 1604643949886,
 *             "simpleUserInfo": {
 *                 "userId": 102525917,
 *                 "nickname": "_NIALL",
 *                 "avatar": "http://p2.music.126.net/QpkFzDkQS0XOcfLp0fU5KA==/109951165325627439.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 213,
 *             "replyCount": 10,
 *             "simpleResourceInfo": {
 *                 "songId": 1493108703,
 *                 "threadId": "R_SO_4_1493108703",
 *                 "name": "Celebrate You",
 *                 "artists": [
 *                     {
 *                         "id": 62896,
 *                         "name": "Kylie Minogue"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/vO6nk-is9RqfF5NIgrDChQ==/109951165454991042.jpg",
 *                 "song": {
 *                     "name": "Celebrate You",
 *                     "id": 1493108703,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 62896,
 *                             "name": "Kylie Minogue",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 85,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 8,
 *                     "v": 5,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 97934609,
 *                         "name": "DISCO (Deluxe)",
 *                         "picUrl": "http://p3.music.126.net/vO6nk-is9RqfF5NIgrDChQ==/109951165454991042.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951165454991042",
 *                         "pic": 109951165454991040
 *                     },
 *                     "dt": 223000,
 *                     "h": {
 *                         "br": 320002,
 *                         "fid": 0,
 *                         "size": 8922427,
 *                         "vd": -68443
 *                     },
 *                     "m": {
 *                         "br": 192002,
 *                         "fid": 0,
 *                         "size": 5353474,
 *                         "vd": -65984
 *                     },
 *                     "l": {
 *                         "br": 128002,
 *                         "fid": 0,
 *                         "size": 3568997,
 *                         "vd": -64647
 *                     },
 *                     "a": null,
 *                     "cd": "01",
 *                     "no": 12,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 1416763,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 0
 *                 },
 *                 "privilege": {
 *                     "id": 1493108703,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 68,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 210770612,
 *             "threadId": "R_SO_4_2587456",
 *             "content": "人这一辈子呢, 最重要的三点其实是: 岁月静好, 懂得感恩, 与你相随。其实我们小学就学过了, 按照国际的说法: fine, thank you , and you",
 *             "time": 1473157134607,
 *             "simpleUserInfo": {
 *                 "userId": 303607884,
 *                 "nickname": "我曾经看到彩虹",
 *                 "avatar": "http://p2.music.126.net/TJMfVeulWDJJ0wlQpR4pSA==/18679603044685980.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 39559,
 *             "replyCount": 79,
 *             "simpleResourceInfo": {
 *                 "songId": 2587456,
 *                 "threadId": "R_SO_4_2587456",
 *                 "name": "I’m Fine",
 *                 "artists": [
 *                     {
 *                         "id": 56738,
 *                         "name": "Emily Hearn"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/afTgyKxcNLab02t8ZQbzng==/830131279023596.jpg",
 *                 "song": {
 *                     "name": "I’m Fine",
 *                     "id": 2587456,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 56738,
 *                             "name": "Emily Hearn",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 0,
 *                     "v": 7,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 260625,
 *                         "name": "Red Balloon",
 *                         "picUrl": "http://p3.music.126.net/afTgyKxcNLab02t8ZQbzng==/830131279023596.jpg",
 *                         "tns": [],
 *                         "pic": 830131279023596
 *                     },
 *                     "dt": 217000,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 8681074,
 *                         "vd": -12700
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 5208669,
 *                         "vd": -10100
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 3472466,
 *                         "vd": -9000
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 9,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 2,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 0,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1327939200007
 *                 },
 *                 "privilege": {
 *                     "id": 2587456,
 *                     "fee": 0,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 320000,
 *                     "dl": 320000,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 320000,
 *                     "fl": 320000,
 *                     "toast": false,
 *                     "flag": 128,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 55516365,
 *             "threadId": "R_SO_4_444312",
 *             "content": "在一次去家旁边的水库去洗澡, 一个小伙伴抓到一只黄鳝, 我们花了一毛钱买了盒火柴, 用一堆废柴把黄鳝给烤了, 突然有人说到, 烤鱼没盐会不会很难吃, 于是我飞奔回家, 捧了一手盐, 就好像捧住了整个童年",
 *             "time": 1450716277345,
 *             "simpleUserInfo": {
 *                 "userId": 73152484,
 *                 "nickname": "隽意阳光",
 *                 "avatar": "http://p2.music.126.net/mA9qNJ20QaQqkaae94tBPQ==/18796151278566138.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 23609,
 *             "replyCount": 169,
 *             "simpleResourceInfo": {
 *                 "songId": 444312,
 *                 "threadId": "R_SO_4_444312",
 *                 "name": "The Rain",
 *                 "artists": [
 *                     {
 *                         "id": 14408,
 *                         "name": "久石譲"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/w0X3JcUjELmDWYjPZL0YSg==/109951164728423040.jpg",
 *                 "song": {
 *                     "name": "The Rain",
 *                     "id": 444312,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 14408,
 *                             "name": "久石譲",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": "600902000000619124",
 *                     "fee": 1,
 *                     "v": 416,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 43083,
 *                         "name": "菊次郎の夏",
 *                         "picUrl": "http://p4.music.126.net/w0X3JcUjELmDWYjPZL0YSg==/109951164728423040.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951164728423040",
 *                         "pic": 109951164728423040
 *                     },
 *                     "dt": 338128,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 13526248,
 *                         "vd": 53001
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 8115766,
 *                         "vd": 55639
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 5410525,
 *                         "vd": 57426
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 6,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 1,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 7003,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1128441600000
 *                 },
 *                 "privilege": {
 *                     "id": 444312,
 *                     "fee": 1,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 320000,
 *                     "fl": 0,
 *                     "toast": false,
 *                     "flag": 4,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 259385588,
 *             "threadId": "R_SO_4_421563711",
 *             "content": "陈南西说, 长大后晓得了, 身无长技, 不配爱人。这是真理",
 *             "time": 1480870198818,
 *             "simpleUserInfo": {
 *                 "userId": 111794535,
 *                 "nickname": "陈粒的神秘圈外男友",
 *                 "avatar": "http://p2.music.126.net/UVDFXOOSHZfOjrEDbMqODQ==/109951164337868148.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 12909,
 *             "replyCount": 104,
 *             "simpleResourceInfo": {
 *                 "songId": 421563711,
 *                 "threadId": "R_SO_4_421563711",
 *                 "name": "芳草地",
 *                 "artists": [
 *                     {
 *                         "id": 1007170,
 *                         "name": "陈粒"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/HQxTggMCB7AHUXN-ZFEtmA==/1371091013186741.jpg",
 *                 "song": {
 *                     "name": "芳草地",
 *                     "id": 421563711,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 1007170,
 *                             "name": "陈粒",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": null,
 *                     "fee": 8,
 *                     "v": 17,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 34780579,
 *                         "name": "小梦大半",
 *                         "picUrl": "http://p3.music.126.net/HQxTggMCB7AHUXN-ZFEtmA==/1371091013186741.jpg",
 *                         "tns": [],
 *                         "pic": 1371091013186741
 *                     },
 *                     "dt": 237146,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 9488762,
 *                         "vd": 521
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 5693275,
 *                         "vd": 196
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 3795531,
 *                         "vd": -2
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 2,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 1416476,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1469462400007
 *                 },
 *                 "privilege": {
 *                     "id": 421563711,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 0,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 1241519670,
 *             "threadId": "R_SO_4_41659255",
 *             "content": "我姥爷是一个参加过对越自卫反击战的人, 他曾经对我说:他不怕在战争中死去, 也不怕在毒蛇和虫子, 他就怕这一生最后因疾病躺在床上不能动弹而死！15年他一个人不顾我们的想法一个人强行停止化疗一个回家, 拿个板凳坐在家门口看着夕阳落下。16年初四一个人坐在大厅里看着姥太的照片, 回来后他睡了",
 *             "time": 1536485103011,
 *             "simpleUserInfo": {
 *                 "userId": 350601413,
 *                 "nickname": "伯庸仲舒",
 *                 "avatar": "http://p2.music.126.net/IzuiddQuORaxl_Fnxb8LRw==/109951164689615558.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 1116,
 *             "replyCount": 37,
 *             "simpleResourceInfo": {
 *                 "songId": 41659255,
 *                 "threadId": "R_SO_4_41659255",
 *                 "name": "老头",
 *                 "artists": [
 *                     {
 *                         "id": 11481,
 *                         "name": "岛屿心情"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/KHqGDy5zFlQb5WOQTBNhjg==/18150737951738895.jpg",
 *                 "song": {
 *                     "name": "老头",
 *                     "id": 41659255,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 11481,
 *                             "name": "岛屿心情",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 90,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 0,
 *                     "v": 10,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 3455346,
 *                         "name": "纷纭",
 *                         "picUrl": "http://p4.music.126.net/KHqGDy5zFlQb5WOQTBNhjg==/18150737951738895.jpg",
 *                         "tns": [],
 *                         "pic_str": "18150737951738895",
 *                         "pic": 18150737951738896
 *                     },
 *                     "dt": 256130,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 10247357,
 *                         "vd": -4.36
 *                     },
 *                     "m": {
 *                         "br": 160000,
 *                         "fid": 0,
 *                         "size": 5123701,
 *                         "vd": -3.95
 *                     },
 *                     "l": {
 *                         "br": 96000,
 *                         "fid": 0,
 *                         "size": 3074238,
 *                         "vd": -3.99
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 8,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 2,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 0,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1439308800007
 *                 },
 *                 "privilege": {
 *                     "id": 41659255,
 *                     "fee": 0,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 320000,
 *                     "dl": 320000,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 320000,
 *                     "fl": 320000,
 *                     "toast": false,
 *                     "flag": 128,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 1120333752,
 *             "threadId": "R_SO_4_420125422",
 *             "content": "淅沥的雨声, 琴键的柔和,   响指的敲打, 钢琴的弹奏,   吉他的和鸣, 仙花的绽放,   鸟语和花香, 温馨又浪漫。  🧚🏻‍♀️🧚🏻‍♀️🌹🌹🧚🏻‍♀️🧚🏻‍♀️🌹🌹",
 *             "time": 1526596429099,
 *             "simpleUserInfo": {
 *                 "userId": 1363603765,
 *                 "nickname": "花仙语露",
 *                 "avatar": "http://p2.music.126.net/o4FHKwWquWBeSZDSHAnmfg==/109951163265891169.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 418,
 *             "replyCount": 20,
 *             "simpleResourceInfo": {
 *                 "songId": 420125422,
 *                 "threadId": "R_SO_4_420125422",
 *                 "name": "Walking Trough The Rain",
 *                 "artists": [
 *                     {
 *                         "id": 11978028,
 *                         "name": "Jay Hifive"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/NGg9PWvDKRwZR8zrvCQHSg==/3439272376857845.jpg",
 *                 "song": {
 *                     "name": "Walking Trough The Rain",
 *                     "id": 420125422,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 11978028,
 *                             "name": "Jay Hifive",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 85,
 *                     "st": 0,
 *                     "rt": null,
 *                     "fee": 0,
 *                     "v": 3,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 34767434,
 *                         "name": "Walking Trough The Rain",
 *                         "picUrl": "http://p3.music.126.net/NGg9PWvDKRwZR8zrvCQHSg==/3439272376857845.jpg",
 *                         "tns": [],
 *                         "pic": 3439272376857845
 *                     },
 *                     "dt": 432065,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 17284746,
 *                         "vd": -2
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 10370865,
 *                         "vd": -2
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 6913924,
 *                         "vd": -2
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 0,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1456588800007
 *                 },
 *                 "privilege": {
 *                     "id": 420125422,
 *                     "fee": 0,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 320000,
 *                     "dl": 320000,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 320000,
 *                     "fl": 320000,
 *                     "toast": false,
 *                     "flag": 128,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 70832787,
 *             "threadId": "R_SO_4_255973",
 *             "content": "以前一直都是留的大波浪长发, 过腰, 突然有一天剪了短头发, 大家都问我是不是受刺激了, 我只是乐呵呵地说没有啊, 我就想试试。其实只有心里知道, 那段时间跟初恋走得很近, 他说喜欢短发女生, 而一直视长发如命的我狠下心剪了。结果, 他跟一个长发女生一起了。而我却爱上了短发的自己。[大笑]",
 *             "time": 1455543763941,
 *             "simpleUserInfo": {
 *                 "userId": 68872932,
 *                 "nickname": "BBBBonnie林佩珊",
 *                 "avatar": "http://p2.music.126.net/xEl-FPkRxFKQu5ltiYn2TQ==/3261151500238831.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 12808,
 *             "replyCount": 86,
 *             "simpleResourceInfo": {
 *                 "songId": 255973,
 *                 "threadId": "R_SO_4_255973",
 *                 "name": "短发",
 *                 "artists": [
 *                     {
 *                         "id": 8329,
 *                         "name": "梁咏琪"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/k7VJQhhKGRBCKcXKwPTT5Q==/109951163104939754.jpg",
 *                 "song": {
 *                     "name": "短发",
 *                     "id": 255973,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 8329,
 *                             "name": "梁咏琪",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [
 *                         "电影《爱情无限Touch》主题曲"
 *                     ],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 1,
 *                     "v": 22,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 25564,
 *                         "name": "短发",
 *                         "picUrl": "http://p4.music.126.net/k7VJQhhKGRBCKcXKwPTT5Q==/109951163104939754.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951163104939754",
 *                         "pic": 109951163104939760
 *                     },
 *                     "dt": 280960,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 11241056,
 *                         "vd": 7446
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 6744651,
 *                         "vd": 9805
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 4496448,
 *                         "vd": 9273
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 2,
 *                     "s_id": 0,
 *                     "mv": 5308806,
 *                     "mst": 9,
 *                     "cp": 7002,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 858528000000
 *                 },
 *                 "privilege": {
 *                     "id": 255973,
 *                     "fee": 1,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 0,
 *                     "toast": false,
 *                     "flag": 4,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 390629164,
 *             "threadId": "R_SO_4_479382670",
 *             "content": "据说人悲伤的极限是5天 很多烦恼 其实都没什么大不了 只是你在那个情境下 在那种心情里 庸人自扰罢了 所以 无论发生什么 先善待自己 时间一过 世界自然会好",
 *             "time": 1495531668924,
 *             "simpleUserInfo": {
 *                 "userId": 366899655,
 *                 "nickname": "王文钰1999",
 *                 "avatar": "http://p2.music.126.net/PF6xWS3pe5uCFWeiCu5Z5Q==/109951162935510025.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 12208,
 *             "replyCount": 208,
 *             "simpleResourceInfo": {
 *                 "songId": 479382670,
 *                 "threadId": "R_SO_4_479382670",
 *                 "name": "给一个焦虑症的朋友",
 *                 "artists": [
 *                     {
 *                         "id": 844646,
 *                         "name": "理想后花园"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/iLiByNh8Fzpw6FRdSwapZA==/109951162931191887.jpg",
 *                 "song": {
 *                     "name": "给一个焦虑症的朋友",
 *                     "id": 479382670,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 844646,
 *                             "name": "理想后花园",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": null,
 *                     "fee": 8,
 *                     "v": 167,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 35532254,
 *                         "name": "大气压",
 *                         "picUrl": "http://p3.music.126.net/iLiByNh8Fzpw6FRdSwapZA==/109951162931191887.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951162931191887",
 *                         "pic": 109951162931191890
 *                     },
 *                     "dt": 229658,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 9188876,
 *                         "vd": -2
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 5513343,
 *                         "vd": -2
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 3675577,
 *                         "vd": -2
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 7,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 2,
 *                     "s_id": 0,
 *                     "mv": 5611847,
 *                     "mst": 9,
 *                     "cp": 0,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1495211559252
 *                 },
 *                 "privilege": {
 *                     "id": 479382670,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 256,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 13693774,
 *             "threadId": "R_SO_4_4238109",
 *             "content": "所以你跑啊跑, 想追上太阳, 可惜它已经开始下落。它在和你竞赛, 从你后面又升起来了。太阳还是那个太阳, 但你却已经变老。呼吸变急了, 离死亡又近了一天。年复一年, 变得越来越短, 总觉得找不到时间了, 计划最后总是要么变成零, 要么变成草草的半页纸。悬于安静的绝望中, 这是英国人的方式, 时光流逝了",
 *             "time": 1427518215342,
 *             "simpleUserInfo": {
 *                 "userId": 20582515,
 *                 "nickname": "Watsonwang",
 *                 "avatar": "http://p2.music.126.net/Q1uFNg9PcrTYLUjNoxMaRg==/7719671139154061.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 4111,
 *             "replyCount": 25,
 *             "simpleResourceInfo": {
 *                 "songId": 4238109,
 *                 "threadId": "R_SO_4_4238109",
 *                 "name": "Time",
 *                 "artists": [
 *                     {
 *                         "id": 98541,
 *                         "name": "Pink Floyd"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/3SYIXJ3qplnhW6vKv6a7bw==/2537672838060410.jpg",
 *                 "song": {
 *                     "name": "Time",
 *                     "id": 4238109,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 98541,
 *                             "name": "Pink Floyd",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 90,
 *                     "st": 0,
 *                     "rt": "600902000003716695",
 *                     "fee": 1,
 *                     "v": 34,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 428503,
 *                         "name": "The Dark Side of the Moon",
 *                         "picUrl": "http://p4.music.126.net/3SYIXJ3qplnhW6vKv6a7bw==/2537672838060410.jpg",
 *                         "tns": [],
 *                         "pic": 2537672838060410
 *                     },
 *                     "dt": 413733,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 16552272,
 *                         "vd": -10124
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 9931381,
 *                         "vd": -7547
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 6620935,
 *                         "vd": -5826
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 4,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 1,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 7002,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 99763200007
 *                 },
 *                 "privilege": {
 *                     "id": 4238109,
 *                     "fee": 1,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 0,
 *                     "toast": false,
 *                     "flag": 4,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 17885411,
 *             "threadId": "R_SO_4_27540403",
 *             "content": "La cumparsita 假面舞会  由Rodriguez 作于1917年, 当时Rodriguez只有17岁 是乌拉圭首都蒙特维多的一个建筑系学生！\r\r  1924年  假面舞会  传到巴黎, 此后该曲\r就在世界各地传播开来, 成了探戈的代名词\r虽然一步之遥在心目地位也很高\r相比而言还是更喜欢La Cumparsita",
 *             "time": 1431268328146,
 *             "simpleUserInfo": {
 *                 "userId": 32399294,
 *                 "nickname": "江河故人",
 *                 "avatar": "http://p2.music.126.net/RfScBqayxylkbRJOEvNQsA==/109951164735695451.jpg",
 *                 "followed": false,
 *                 "userType": 200
 *             },
 *             "likedCount": 12962,
 *             "replyCount": 42,
 *             "simpleResourceInfo": {
 *                 "songId": 27540403,
 *                 "threadId": "R_SO_4_27540403",
 *                 "name": "Habanera from Carmen / La Cumparsita",
 *                 "artists": [
 *                     {
 *                         "id": 810011,
 *                         "name": "Martynas"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/4WY-C5gId6I-BDdGuzDqWg==/2536573325565968.jpg",
 *                 "song": {
 *                     "name": "Habanera from Carmen / La Cumparsita",
 *                     "id": 27540403,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 810011,
 *                             "name": "Martynas",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [
 *                         "比才：卡门 - 哈巴涅拉/假面舞会"
 *                     ],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 1,
 *                     "v": 16,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 2627983,
 *                         "name": "Martynas",
 *                         "picUrl": "http://p3.music.126.net/4WY-C5gId6I-BDdGuzDqWg==/2536573325565968.jpg",
 *                         "tns": [],
 *                         "pic": 2536573325565968
 *                     },
 *                     "dt": 202000,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 8096088,
 *                         "vd": -2
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 4857740,
 *                         "vd": -2
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 3238566,
 *                         "vd": -2
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 3,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 1,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 7003,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1381680000007
 *                 },
 *                 "privilege": {
 *                     "id": 27540403,
 *                     "fee": 1,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 0,
 *                     "toast": false,
 *                     "flag": 4,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 1524133572,
 *             "threadId": "R_SO_4_1371503842",
 *             "content": "“她咬着指尖自下而上看我, 眼中满满都是装出来的蜜糖纯真, 粘黏着我的皮肤。再贴近一点, 哪怕一厘, 我就能看见那隐藏着的如狐狸一般的狡诈神色, 决不输于任何一个最恶毒的成年女人。可我的小宝贝是那样娇嗔的女孩, 我忍受不了她如幼猫一样的撒娇。更何况她不过想要我的命, 那送她便是了。”",
 *             "time": 1560575410005,
 *             "simpleUserInfo": {
 *                 "userId": 467613384,
 *                 "nickname": "本小姐才是你在废墟中的唯一信仰",
 *                 "avatar": "http://p2.music.126.net/z5XDSy5umFc7e6_raLaXaA==/109951165484331107.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 7707,
 *             "replyCount": 117,
 *             "simpleResourceInfo": {
 *                 "songId": 1371503842,
 *                 "threadId": "R_SO_4_1371503842",
 *                 "name": "ฅ’ω’ฅ",
 *                 "artists": [
 *                     {
 *                         "id": 12037196,
 *                         "name": "WJCTION"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/DsLoVcPomQZVuRcPHDVm7A==/109951164143387320.jpg",
 *                 "song": {
 *                     "name": "ฅ’ω’ฅ",
 *                     "id": 1371503842,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 12037196,
 *                             "name": "WJCTION",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 95,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 8,
 *                     "v": 8,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 79726870,
 *                         "name": "ฅ’ω’ฅ",
 *                         "picUrl": "http://p3.music.126.net/DsLoVcPomQZVuRcPHDVm7A==/109951164143387320.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951164143387320",
 *                         "pic": 109951164143387330
 *                     },
 *                     "dt": 206367,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 8256827,
 *                         "vd": -23935
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 4954114,
 *                         "vd": -21338
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 3302757,
 *                         "vd": -19630
 *                     },
 *                     "a": null,
 *                     "cd": "01",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 0,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 0
 *                 },
 *                 "privilege": {
 *                     "id": 1371503842,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 320000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 66,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 695360212,
 *             "threadId": "R_SO_4_416554193",
 *             "content": "世界就像一个巨大的娃娃机 我隔着玻璃只想得到你",
 *             "time": 1517804523074,
 *             "simpleUserInfo": {
 *                 "userId": 321004050,
 *                 "nickname": "Pa9k",
 *                 "avatar": "http://p2.music.126.net/gK9qBv6oJKsR7COkVPy9Pg==/109951164285200936.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 12333,
 *             "replyCount": 182,
 *             "simpleResourceInfo": {
 *                 "songId": 416554193,
 *                 "threadId": "R_SO_4_416554193",
 *                 "name": "South Wind",
 *                 "artists": [
 *                     {
 *                         "id": 17400,
 *                         "name": "NIKIIE"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/zDxL78H-clXh5Z8j9Cyi8g==/1421668546070495.jpg",
 *                 "song": {
 *                     "name": "South Wind",
 *                     "id": 416554193,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 17400,
 *                             "name": "NIKIIE",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": null,
 *                     "fee": 0,
 *                     "v": 8,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 34726537,
 *                         "name": "MOMENTARY: Memories of the Beginning",
 *                         "picUrl": "http://p3.music.126.net/zDxL78H-clXh5Z8j9Cyi8g==/1421668546070495.jpg",
 *                         "tns": [],
 *                         "pic": 1421668546070495
 *                     },
 *                     "dt": 216548,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 8664338,
 *                         "vd": 0
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 5198620,
 *                         "vd": 0
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 3465761,
 *                         "vd": 0
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 4,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 5360409,
 *                     "mst": 9,
 *                     "cp": 663018,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1465287520170
 *                 },
 *                 "privilege": {
 *                     "id": 416554193,
 *                     "fee": 0,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 320000,
 *                     "dl": 999000,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 320000,
 *                     "toast": false,
 *                     "flag": 256,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 1101153794,
 *             "threadId": "R_SO_4_2542100",
 *             "content": "《儿时情景》埃丝特•奥法里姆, 以色列歌手, 旅居瑞士。1963年代表瑞士参加Eurovision (欧洲电视网络歌唱) 比赛, 本应得第一, 但因北欧评判作弊, Esther被拉第二。此事件成为Eurovision史上最大丑闻, 但她从此便红遍欧洲。音色亲切优美, 凡男性听了她歌声, 就想向她求婚。[可爱]",
 *             "time": 1524988981768,
 *             "simpleUserInfo": {
 *                 "userId": 297204739,
 *                 "nickname": "苏黎世化学",
 *                 "avatar": "http://p2.music.126.net/eY_3NBbaRmQ4kMEns_usfQ==/1382086132898521.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 59,
 *             "replyCount": 8,
 *             "simpleResourceInfo": {
 *                 "songId": 2542100,
 *                 "threadId": "R_SO_4_2542100",
 *                 "name": "Kinderspiele",
 *                 "artists": [
 *                     {
 *                         "id": 55623,
 *                         "name": "Esther Ofarim"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/-QwrocF88ZjJ2q9utlrBRw==/814738116226801.jpg",
 *                 "song": {
 *                     "name": "Kinderspiele",
 *                     "id": 2542100,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 55623,
 *                             "name": "Esther Ofarim",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 25,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 0,
 *                     "v": 4,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 255665,
 *                         "name": "Esther",
 *                         "picUrl": "http://p4.music.126.net/-QwrocF88ZjJ2q9utlrBRw==/814738116226801.jpg",
 *                         "tns": [],
 *                         "pic": 814738116226801
 *                     },
 *                     "dt": 193000,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 7740774,
 *                         "vd": 10598
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 4644532,
 *                         "vd": 13386
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 3096411,
 *                         "vd": 15145
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 2,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 0,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 620924400007
 *                 },
 *                 "privilege": {
 *                     "id": 2542100,
 *                     "fee": 0,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 320000,
 *                     "dl": 320000,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 320000,
 *                     "fl": 320000,
 *                     "toast": false,
 *                     "flag": 128,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 1501703275,
 *             "threadId": "R_SO_4_1366564091",
 *             "content": "“breathe slow”意思是: 呼吸缓慢。  呼吸困难的时刻人们会需要什么, 两支烟, 或者一场无需开口听着就行的对话",
 *             "time": 1558871574988,
 *             "simpleUserInfo": {
 *                 "userId": 1427419091,
 *                 "nickname": "江本阪川",
 *                 "avatar": "http://p2.music.126.net/L-8elTRMpSI_Ft7x1Zn46w==/109951163555707395.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 6053,
 *             "replyCount": 33,
 *             "simpleResourceInfo": {
 *                 "songId": 1366564091,
 *                 "threadId": "R_SO_4_1366564091",
 *                 "name": "breathe slow",
 *                 "artists": [
 *                     {
 *                         "id": 12175279,
 *                         "name": "ROOK1E"
 *                     },
 *                     {
 *                         "id": 12043020,
 *                         "name": "Meltycanon"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/gZ3tdfKetM1aZ1irxTKO7Q==/109951164087919745.jpg",
 *                 "song": {
 *                     "name": "breathe slow",
 *                     "id": 1366564091,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 12175279,
 *                             "name": "ROOK1E",
 *                             "tns": [],
 *                             "alias": []
 *                         },
 *                         {
 *                             "id": 12043020,
 *                             "name": "Meltycanon",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 8,
 *                     "v": 5,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 79263936,
 *                         "name": "breathe slow",
 *                         "picUrl": "http://p4.music.126.net/gZ3tdfKetM1aZ1irxTKO7Q==/109951164087919745.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951164087919745",
 *                         "pic": 109951164087919740
 *                     },
 *                     "dt": 139456,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 5580525,
 *                         "vd": 22891
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 3348333,
 *                         "vd": 25500
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 2232237,
 *                         "vd": 27074
 *                     },
 *                     "a": null,
 *                     "cd": "01",
 *                     "no": 0,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 1416692,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 0
 *                 },
 *                 "privilege": {
 *                     "id": 1366564091,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 132,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 245586366,
 *             "threadId": "R_SO_4_27001090",
 *             "content": "西方古典总使人联想到生活的情趣, 要么高雅浪漫, 要么品味生活, 总之热衷当下“活着”的一种态度。反观东方古典, 予人的是一种意境, 使人心旷神怡, 化身物外, 追求的是生活之外精神的归宿, 热衷的是世界、宇宙及哲学的思索。假若西方古典是小乘佛法, 与浓咖相配, 那么东方古典自然是大乘佛法与清茶对应",
 *             "time": 1478941539075,
 *             "simpleUserInfo": {
 *                 "userId": 267155165,
 *                 "nickname": "-小豚豚",
 *                 "avatar": "http://p2.music.126.net/sIHSxIhPGMug2y4v9vzOMw==/18790653720741718.jpg",
 *                 "followed": false,
 *                 "userType": 204
 *             },
 *             "likedCount": 734,
 *             "replyCount": 34,
 *             "simpleResourceInfo": {
 *                 "songId": 27001090,
 *                 "threadId": "R_SO_4_27001090",
 *                 "name": "Serenade from String Quartet in F Major, Op. 3, No. 5",
 *                 "artists": [
 *                     {
 *                         "id": 645938,
 *                         "name": "Lazar Gosman"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/4KlGD6UiJ0XIALAcGQl4IQ==/5747147278469623.jpg",
 *                 "song": {
 *                     "name": "Serenade from String Quartet in F Major, Op. 3, No. 5",
 *                     "id": 27001090,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 645938,
 *                             "name": "Lazar Gosman",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 55,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 8,
 *                     "v": 6,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 2580133,
 *                         "name": "The Knot Collection of Ceremony and Wedding Music",
 *                         "picUrl": "http://p4.music.126.net/4KlGD6UiJ0XIALAcGQl4IQ==/5747147278469623.jpg",
 *                         "tns": [],
 *                         "pic": 5747147278469623
 *                     },
 *                     "dt": 343000,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 13749015,
 *                         "vd": 77854
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 8249508,
 *                         "vd": 79965
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 5499755,
 *                         "vd": 80211
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 5,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 1,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 7001,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1146499200007
 *                 },
 *                 "privilege": {
 *                     "id": 27001090,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 320000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 256,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 1516781128,
 *             "threadId": "R_SO_4_1354016079",
 *             "content": "一开始轻快的前奏让人沦陷, 就像是一个人在清晨的森林里跑着, 想细细品味时鼓点加入。于是  节奏起, 心跳急。  却又闻那沁人心脾的歌声忽然升调, 把整个人往上提了一刹  鼓点停一拍, 大脑静一呆。  于是那轻快的歌声, 鼓点续上, 不久音调上提略一顿, 进入高潮  依旧是轻快的语调, 但不同的是多了你喜欢的人",
 *             "time": 1560052797447,
 *             "simpleUserInfo": {
 *                 "userId": 1300060153,
 *                 "nickname": "奥克Aouk",
 *                 "avatar": "http://p2.music.126.net/ujpJVsaVxEUzt7B3Dgqs5Q==/18972073137723218.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 1706,
 *             "replyCount": 28,
 *             "simpleResourceInfo": {
 *                 "songId": 1354016079,
 *                 "threadId": "R_SO_4_1354016079",
 *                 "name": "IA-アスノヨゾラ哨戒班/明日的夜空哨戒班（REGG$HOTS / Akie秋绘 / 夏璃夜 remix）",
 *                 "artists": [
 *                     {
 *                         "id": 28071541,
 *                         "name": "RADI8"
 *                     },
 *                     {
 *                         "id": 12184145,
 *                         "name": "Akie秋绘"
 *                     },
 *                     {
 *                         "id": 12236114,
 *                         "name": "夏璃夜"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/nDgeSP4ueSao7HDq40H_AQ==/109951163948483970.jpg",
 *                 "song": {
 *                     "name": "IA-アスノヨゾラ哨戒班/明日的夜空哨戒班（REGG$HOTS / Akie秋绘 / 夏璃夜 remix）",
 *                     "id": 1354016079,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 28071541,
 *                             "name": "RADI8",
 *                             "tns": [],
 *                             "alias": []
 *                         },
 *                         {
 *                             "id": 12184145,
 *                             "name": "Akie秋绘",
 *                             "tns": [],
 *                             "alias": []
 *                         },
 *                         {
 *                             "id": 12236114,
 *                             "name": "夏璃夜",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 0,
 *                     "v": 8,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 75616521,
 *                         "name": "アスノヨゾラ哨戒班/明日的夜空哨戒班（REGG$HOTS REMIX）",
 *                         "picUrl": "http://p4.music.126.net/nDgeSP4ueSao7HDq40H_AQ==/109951163948483970.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951163948483970",
 *                         "pic": 109951163948483970
 *                     },
 *                     "dt": 206000,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 8242199,
 *                         "vd": -7799
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 4945337,
 *                         "vd": -5299
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 3296906,
 *                         "vd": -3700
 *                     },
 *                     "a": null,
 *                     "cd": "01",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 0,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 0
 *                 },
 *                 "privilege": {
 *                     "id": 1354016079,
 *                     "fee": 0,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 320000,
 *                     "dl": 999000,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 320000,
 *                     "toast": false,
 *                     "flag": 128,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 450555285,
 *             "threadId": "R_SO_4_486188245",
 *             "content": "“相片和音乐, 是有记忆功能的。”前两天和最好的朋友去小卖铺买了一根棒冰。走过操场和篮球架, 走过画室, 也去食堂打了一次饭。就和一年前平淡无奇的每一天一样。所有细碎的过往和经历就这么纷至沓来了。 ​​​学生时代, 大概是最好的时代💙",
 *             "time": 1498360140688,
 *             "simpleUserInfo": {
 *                 "userId": 51912528,
 *                 "nickname": "草木枝枝",
 *                 "avatar": "http://p2.music.126.net/8U7wUrlbozYX2nyZo88o_g==/109951163184541761.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 12853,
 *             "replyCount": 24,
 *             "simpleResourceInfo": {
 *                 "songId": 486188245,
 *                 "threadId": "R_SO_4_486188245",
 *                 "name": "下一站茶山刘",
 *                 "artists": [
 *                     {
 *                         "id": 1050282,
 *                         "name": "房东的猫"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/eyY36vwg6DWAJpBy9FZngA==/19176582300236529.jpg",
 *                 "song": {
 *                     "name": "下一站茶山刘",
 *                     "id": 486188245,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 1050282,
 *                             "name": "房东的猫",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": null,
 *                     "fee": 8,
 *                     "v": 62,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 35663180,
 *                         "name": "拾贰",
 *                         "picUrl": "http://p3.music.126.net/eyY36vwg6DWAJpBy9FZngA==/19176582300236529.jpg",
 *                         "tns": [],
 *                         "pic_str": "19176582300236529",
 *                         "pic": 19176582300236530
 *                     },
 *                     "dt": 205573,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 8225480,
 *                         "vd": -4200
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 4935306,
 *                         "vd": -1500
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 3290218,
 *                         "vd": -2
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 2,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 386011,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1498320000007
 *                 },
 *                 "privilege": {
 *                     "id": 486188245,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 2,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 1396127602,
 *             "threadId": "R_SO_4_399354289",
 *             "content": "我的青春里啊, 没有刻骨铭心的初恋, 没有让人羡慕的外貌, 没有让人崇拜的成绩, 没有传说中的美好回忆, 印象最深的是只有日复一日坐在教室窗边的我看着夕阳落下升起落下升起落下升起, 很多时候, 沉浸在自己的世界里, 任他人吵闹",
 *             "time": 1550499268896,
 *             "simpleUserInfo": {
 *                 "userId": 372658030,
 *                 "nickname": "-X-729",
 *                 "avatar": "http://p2.music.126.net/NhSQw9cNROavta4QHj_6pw==/109951165406547088.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 671,
 *             "replyCount": 58,
 *             "simpleResourceInfo": {
 *                 "songId": 399354289,
 *                 "threadId": "R_SO_4_399354289",
 *                 "name": "不说再见",
 *                 "artists": [
 *                     {
 *                         "id": 711683,
 *                         "name": "好妹妹"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/GHspaji-l6W7rBT0e4PFrg==/2529976259373492.jpg",
 *                 "song": {
 *                     "name": "不说再见",
 *                     "id": 399354289,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 711683,
 *                             "name": "好妹妹",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [
 *                         "电影《谁的青春不迷茫》主题曲"
 *                     ],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": null,
 *                     "fee": 8,
 *                     "v": 89,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 34407108,
 *                         "name": "不说再见",
 *                         "picUrl": "http://p4.music.126.net/GHspaji-l6W7rBT0e4PFrg==/2529976259373492.jpg",
 *                         "tns": [],
 *                         "pic": 2529976259373492
 *                     },
 *                     "dt": 296150,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 11848141,
 *                         "vd": -1400
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 7108901,
 *                         "vd": -2
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 4739282,
 *                         "vd": -2
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 5267079,
 *                     "mst": 9,
 *                     "cp": 1416476,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1451545716191
 *                 },
 *                 "privilege": {
 *                     "id": 399354289,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 320000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 0,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 1507673663,
 *             "threadId": "R_SO_4_542001011",
 *             "content": "trance的灵魂在于,  前奏用持久的循环实现的气氛的营造,  中间的变换中作者用舒缓亦或者壮美的旋律诉说 (很多时候像是诉说一段美好的故事) 。 再后来, 鼓点和快速节奏再次上线, 伴随着并未中断的壮美旋律, 让这个迷幻的世界到达风景最好的巅峰。 最后旋律回归至最初, 配合原始节奏模式, 完美收官[爱心]",
 *             "time": 1559351305497,
 *             "simpleUserInfo": {
 *                 "userId": 414580711,
 *                 "nickname": "Snovia_",
 *                 "avatar": "http://p2.music.126.net/3CgcXbTDcUuKAeqbdC_-lg==/109951165334650599.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 100,
 *             "replyCount": 17,
 *             "simpleResourceInfo": {
 *                 "songId": 542001011,
 *                 "threadId": "R_SO_4_542001011",
 *                 "name": "A Love Story (Original Mix)",
 *                 "artists": [
 *                     {
 *                         "id": 794061,
 *                         "name": "Afternova"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/5aazUOakV_F6JJ1qsM9V6Q==/109951163167096964.jpg",
 *                 "song": {
 *                     "name": "A Love Story (Original Mix)",
 *                     "id": 542001011,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 794061,
 *                             "name": "Afternova",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 10,
 *                     "st": 0,
 *                     "rt": null,
 *                     "fee": 8,
 *                     "v": 4,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 37782019,
 *                         "name": "A Love Story",
 *                         "picUrl": "http://p3.music.126.net/5aazUOakV_F6JJ1qsM9V6Q==/109951163167096964.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951163167096964",
 *                         "pic": 109951163167096960
 *                     },
 *                     "dt": 464567,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 18585644,
 *                         "vd": -1000
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 11151404,
 *                         "vd": -2
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 7434284,
 *                         "vd": -2
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 1,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 526012,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1519747200007
 *                 },
 *                 "privilege": {
 *                     "id": 542001011,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 64,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 649834240,
 *             "threadId": "R_SO_4_523035658",
 *             "content": "艺术本就是删去功利的产物, 是对美的纯粹追求。东方顺应了这一点, 所以显得超然于世。当然, 只有诗意是不够的, 还要有一种醉意, 就是尼采提出的酒神精神。只有有了奔放, 有了酣畅, 有了飞动, 有了癫狂, 有了醉步如舞, 有了云烟迷茫, 才能现出温度。这种众人皆醒我独醉的气质, 我认为是东方最迷人的地方",
 *             "time": 1514040473994,
 *             "simpleUserInfo": {
 *                 "userId": 330576857,
 *                 "nickname": "绯红梧桐",
 *                 "avatar": "http://p2.music.126.net/CbSJFDovGKKxEASYGp3eJw==/109951163432768184.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 951,
 *             "replyCount": 15,
 *             "simpleResourceInfo": {
 *                 "songId": 523035658,
 *                 "threadId": "R_SO_4_523035658",
 *                 "name": "風の唄(long ver.)",
 *                 "artists": [
 *                     {
 *                         "id": 17207,
 *                         "name": "めらみぽっぷ"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/c2AaIdHRZ1rWvKnlZnmlFA==/109951163081137434.jpg",
 *                 "song": {
 *                     "name": "風の唄(long ver.)",
 *                     "id": 523035658,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 17207,
 *                             "name": "めらみぽっぷ",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [
 *                         "原曲:信仰は儚き人間の為に"
 *                     ],
 *                     "pop": 85,
 *                     "st": 0,
 *                     "rt": null,
 *                     "fee": 0,
 *                     "v": 10,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 36875647,
 *                         "name": "秘封活動記録-祝-Original Soundtrack",
 *                         "picUrl": "http://p4.music.126.net/c2AaIdHRZ1rWvKnlZnmlFA==/109951163081137434.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951163081137434",
 *                         "pic": 109951163081137440
 *                     },
 *                     "dt": 341333,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 13655815,
 *                         "vd": -20900
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 8193506,
 *                         "vd": -18400
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 5462352,
 *                         "vd": -16800
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 23,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 663018,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1507996800000
 *                 },
 *                 "privilege": {
 *                     "id": 523035658,
 *                     "fee": 0,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 320000,
 *                     "dl": 999000,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 320000,
 *                     "toast": false,
 *                     "flag": 256,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 420237221,
 *             "threadId": "R_SO_4_402073304",
 *             "content": "每次听这个, 宏达的音乐里有那么一两声清脆的声音, 感觉人类就像一个顽皮的孩子, 在宇宙母亲的怀抱里嬉戏, 又会想起玩儿坎巴拉的时候小绿人再入大气层搞怪的表情, 人类历史上最伟大的一次冒险, 第一次冲出地球探索无垠的宇宙, 真是让人感动的要哭！",
 *             "time": 1495862558984,
 *             "simpleUserInfo": {
 *                 "userId": 80527771,
 *                 "nickname": "Saturn__V",
 *                 "avatar": "http://p2.music.126.net/BlmyXpOdEmMDDb_w4rZLfQ==/19116109160975661.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 106,
 *             "replyCount": 14,
 *             "simpleResourceInfo": {
 *                 "songId": 402073304,
 *                 "threadId": "R_SO_4_402073304",
 *                 "name": "Re-Entry",
 *                 "artists": [
 *                     {
 *                         "id": 191571,
 *                         "name": "Adam Young"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/IjaoZ2oTINibrbdwx1LmSQ==/109951164857225332.jpg",
 *                 "song": {
 *                     "name": "Re-Entry",
 *                     "id": 402073304,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 191571,
 *                             "name": "Adam Young",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 10,
 *                     "st": 0,
 *                     "rt": null,
 *                     "fee": 8,
 *                     "v": 12,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 34478104,
 *                         "name": "Apollo 11",
 *                         "picUrl": "http://p4.music.126.net/IjaoZ2oTINibrbdwx1LmSQ==/109951164857225332.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951164857225332",
 *                         "pic": 109951164857225330
 *                     },
 *                     "dt": 152426,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 6100158,
 *                         "vd": -13216
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 3660112,
 *                         "vd": -10598
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 2440089,
 *                         "vd": -8924
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 11,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 0,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 1416729,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1454422059630
 *                 },
 *                 "privilege": {
 *                     "id": 402073304,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 6,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 148934394,
 *             "threadId": "R_SO_4_381821",
 *             "content": "看完痛仰, 坐大巴回到苏州站, 到地下一层买肯德基。跟随我进来的是三个女孩, 我看她们的鞋子很脏都是泥, 这时候她们也在看我的鞋子, 我的鞋子也都是泥。当我看到其中一个女孩手上缠着痛仰红色方巾时, 有种特别的亲切感。我冲她们笑笑, 她们也回我笑容。摇滚真的拉进了人与人的距离。",
 *             "time": 1462187459468,
 *             "simpleUserInfo": {
 *                 "userId": 46106995,
 *                 "nickname": "52hertz_Antonio",
 *                 "avatar": "http://p2.music.126.net/SZG1gbUUu8ol_bEYM9k3Jw==/109951163556285811.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 1631,
 *             "replyCount": 36,
 *             "simpleResourceInfo": {
 *                 "songId": 381821,
 *                 "threadId": "R_SO_4_381821",
 *                 "name": "再见杰克",
 *                 "artists": [
 *                     {
 *                         "id": 12971,
 *                         "name": "痛仰乐队"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/1f7NwezJUZ_Ewpd070oYaQ==/30786325592070.jpg",
 *                 "song": {
 *                     "name": "再见杰克",
 *                     "id": 381821,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 12971,
 *                             "name": "痛仰乐队",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": "",
 *                     "fee": 8,
 *                     "v": 17,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 37797,
 *                         "name": "不要停止我的音乐",
 *                         "picUrl": "http://p3.music.126.net/1f7NwezJUZ_Ewpd070oYaQ==/30786325592070.jpg",
 *                         "tns": [],
 *                         "pic": 30786325592070
 *                     },
 *                     "dt": 273787,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 10953709,
 *                         "vd": -57093
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 6572243,
 *                         "vd": -54500
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 4381510,
 *                         "vd": -52842
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 1,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 2,
 *                     "s_id": 0,
 *                     "mv": 0,
 *                     "mst": 9,
 *                     "cp": 414014,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 1222704000000
 *                 },
 *                 "privilege": {
 *                     "id": 381821,
 *                     "fee": 8,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 7,
 *                     "cp": 1,
 *                     "subp": 1,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 128000,
 *                     "toast": false,
 *                     "flag": 0,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         },
 *         {
 *             "id": 16295831,
 *             "threadId": "R_SO_4_3029895",
 *             "content": "电影《贝隆夫人》主题曲《阿根廷别为我哭泣》唱出了阿根廷前“第一夫人”艾薇塔·贝隆辛酸曲折、辉煌传奇的一生。从穷裁缝的私生女到15岁的舞女, 从高级交际花到总统夫人, 艾薇塔33载的短暂一生记录了太多的悲欢离合、大起大落。",
 *             "time": 1429976908069,
 *             "simpleUserInfo": {
 *                 "userId": 30105054,
 *                 "nickname": "bluebac",
 *                 "avatar": "http://p2.music.126.net/1GOmuaJnSOdvSRimKA_MpA==/109951163051071529.jpg",
 *                 "followed": false,
 *                 "userType": 0
 *             },
 *             "likedCount": 6317,
 *             "replyCount": 17,
 *             "simpleResourceInfo": {
 *                 "songId": 3029895,
 *                 "threadId": "R_SO_4_3029895",
 *                 "name": "Don't Cry for Me Argentina",
 *                 "artists": [
 *                     {
 *                         "id": 66364,
 *                         "name": "Madonna"
 *                     }
 *                 ],
 *                 "songCoverUrl": "http://p1.music.126.net/_Jg6Iyzjvy5R5H2KPE2tPw==/109951163846698389.jpg",
 *                 "song": {
 *                     "name": "Don't Cry for Me Argentina",
 *                     "id": 3029895,
 *                     "pst": 0,
 *                     "t": 0,
 *                     "ar": [
 *                         {
 *                             "id": 66364,
 *                             "name": "Madonna",
 *                             "tns": [],
 *                             "alias": []
 *                         }
 *                     ],
 *                     "alia": [
 *                         "电影《贝隆夫人》主题曲"
 *                     ],
 *                     "pop": 100,
 *                     "st": 0,
 *                     "rt": "600902000001698006",
 *                     "fee": 1,
 *                     "v": 296,
 *                     "crbt": null,
 *                     "cf": "",
 *                     "al": {
 *                         "id": 306533,
 *                         "name": "Music from the Motion Picture \"Evita\"",
 *                         "picUrl": "http://p3.music.126.net/_Jg6Iyzjvy5R5H2KPE2tPw==/109951163846698389.jpg",
 *                         "tns": [],
 *                         "pic_str": "109951163846698389",
 *                         "pic": 109951163846698380
 *                     },
 *                     "dt": 335440,
 *                     "h": {
 *                         "br": 320000,
 *                         "fid": 0,
 *                         "size": 13420713,
 *                         "vd": -2
 *                     },
 *                     "m": {
 *                         "br": 192000,
 *                         "fid": 0,
 *                         "size": 8052445,
 *                         "vd": -2
 *                     },
 *                     "l": {
 *                         "br": 128000,
 *                         "fid": 0,
 *                         "size": 5368311,
 *                         "vd": -2
 *                     },
 *                     "a": null,
 *                     "cd": "1",
 *                     "no": 11,
 *                     "rtUrl": null,
 *                     "ftype": 0,
 *                     "rtUrls": [],
 *                     "djId": 0,
 *                     "copyright": 1,
 *                     "s_id": 0,
 *                     "mv": 5361816,
 *                     "mst": 9,
 *                     "cp": 7002,
 *                     "rurl": null,
 *                     "rtype": 0,
 *                     "publishTime": 870105600000,
 *                     "tns": [
 *                         "阿根廷别为我哭泣"
 *                     ]
 *                 },
 *                 "privilege": {
 *                     "id": 3029895,
 *                     "fee": 1,
 *                     "payed": 0,
 *                     "st": 0,
 *                     "pl": 128000,
 *                     "dl": 0,
 *                     "sp": 0,
 *                     "cp": 0,
 *                     "subp": 0,
 *                     "cs": false,
 *                     "maxbr": 999000,
 *                     "fl": 0,
 *                     "toast": false,
 *                     "flag": 4,
 *                     "preSell": false
 *                 }
 *             },
 *             "liked": false
 *         }
 *     ]
 * }
 */
public class HotwallCommentBean {

    private int code;
    private String message;
    private List<Data> data;

    public void setCode(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public void setData(List<Data> data) {
        this.data = data;
    }

    public List<Data> getData() {
        return data;
    }

    public static class Data {

        private long id;
        private String threadId;
        private String content;
        private long time;
        private SimpleUserInfo simpleUserInfo;
        private int likedCount;
        private int replyCount;
        private SimpleResourceInfo simpleResourceInfo;
        private boolean liked;

        public void setId(long id) {
            this.id = id;
        }

        public long getId() {
            return id;
        }

        public void setThreadId(String threadId) {
            this.threadId = threadId;
        }

        public String getThreadId() {
            return threadId;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getContent() {
            return content;
        }

        public void setTime(long time) {
            this.time = time;
        }

        public long getTime() {
            return time;
        }

        public void setSimpleUserInfo(SimpleUserInfo simpleUserInfo) {
            this.simpleUserInfo = simpleUserInfo;
        }

        public SimpleUserInfo getSimpleUserInfo() {
            return simpleUserInfo;
        }

        public void setLikedCount(int likedCount) {
            this.likedCount = likedCount;
        }

        public int getLikedCount() {
            return likedCount;
        }

        public void setReplyCount(int replyCount) {
            this.replyCount = replyCount;
        }

        public int getReplyCount() {
            return replyCount;
        }

        public void setSimpleResourceInfo(SimpleResourceInfo simpleResourceInfo) {
            this.simpleResourceInfo = simpleResourceInfo;
        }

        public SimpleResourceInfo getSimpleResourceInfo() {
            return simpleResourceInfo;
        }

        public void setLiked(boolean liked) {
            this.liked = liked;
        }

        public boolean getLiked() {
            return liked;
        }

    }

    public static class SimpleUserInfo {

        private long userId;
        private String nickname;
        private String avatar;
        private boolean followed;
        private int userType;

        public void setUserId(long userId) {
            this.userId = userId;
        }

        public long getUserId() {
            return userId;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public String getNickname() {
            return nickname;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setFollowed(boolean followed) {
            this.followed = followed;
        }

        public boolean getFollowed() {
            return followed;
        }

        public void setUserType(int userType) {
            this.userType = userType;
        }

        public int getUserType() {
            return userType;
        }

    }

    public static class SimpleResourceInfo {

        private long songId;
        private String threadId;
        private String name;
        private List<Artists> artists;
        private String songCoverUrl;
        private SongDetailBean.SongsBean song;
        private Privilege privilege;

        public void setSongId(long songId) {
            this.songId = songId;
        }

        public long getSongId() {
            return songId;
        }

        public void setThreadId(String threadId) {
            this.threadId = threadId;
        }

        public String getThreadId() {
            return threadId;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public void setArtists(List<Artists> artists) {
            this.artists = artists;
        }

        public List<Artists> getArtists() {
            return artists;
        }

        public void setSongCoverUrl(String songCoverUrl) {
            this.songCoverUrl = songCoverUrl;
        }

        public String getSongCoverUrl() {
            return songCoverUrl;
        }

        public void setSong(SongDetailBean.SongsBean song) {
            this.song = song;
        }

        public SongDetailBean.SongsBean getSong() {
            return song;
        }

        public void setPrivilege(Privilege privilege) {
            this.privilege = privilege;
        }

        public Privilege getPrivilege() {
            return privilege;
        }

    }

    public static class Privilege {

        private long id;
        private int fee;
        private int payed;
        private int st;
        private long pl;
        private int dl;
        private int sp;
        private int cp;
        private int subp;
        private boolean cs;
        private long maxbr;
        private long fl;
        private boolean toast;
        private int flag;
        private boolean preSell;

        public void setId(long id) {
            this.id = id;
        }

        public long getId() {
            return id;
        }

        public void setFee(int fee) {
            this.fee = fee;
        }

        public int getFee() {
            return fee;
        }

        public void setPayed(int payed) {
            this.payed = payed;
        }

        public int getPayed() {
            return payed;
        }

        public void setSt(int st) {
            this.st = st;
        }

        public int getSt() {
            return st;
        }

        public void setPl(long pl) {
            this.pl = pl;
        }

        public long getPl() {
            return pl;
        }

        public void setDl(int dl) {
            this.dl = dl;
        }

        public int getDl() {
            return dl;
        }

        public void setSp(int sp) {
            this.sp = sp;
        }

        public int getSp() {
            return sp;
        }

        public void setCp(int cp) {
            this.cp = cp;
        }

        public int getCp() {
            return cp;
        }

        public void setSubp(int subp) {
            this.subp = subp;
        }

        public int getSubp() {
            return subp;
        }

        public void setCs(boolean cs) {
            this.cs = cs;
        }

        public boolean getCs() {
            return cs;
        }

        public void setMaxbr(long maxbr) {
            this.maxbr = maxbr;
        }

        public long getMaxbr() {
            return maxbr;
        }

        public void setFl(long fl) {
            this.fl = fl;
        }

        public long getFl() {
            return fl;
        }

        public void setToast(boolean toast) {
            this.toast = toast;
        }

        public boolean getToast() {
            return toast;
        }

        public void setFlag(int flag) {
            this.flag = flag;
        }

        public int getFlag() {
            return flag;
        }

        public void setPreSell(boolean preSell) {
            this.preSell = preSell;
        }

        public boolean getPreSell() {
            return preSell;
        }

    }

    public static class Artists {

        private long id;
        private String name;

        public void setId(long id) {
            this.id = id;
        }

        public long getId() {
            return id;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }

    }

}




