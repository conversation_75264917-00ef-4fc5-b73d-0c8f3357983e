<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectDictionaryState">
    <dictionary name="zhang" />
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State />
          <State>
            <id>Abstraction issuesJava</id>
          </State>
          <State>
            <id>Assignment issuesJava</id>
          </State>
          <State>
            <id>Class structureJava</id>
          </State>
          <State>
            <id>Code maturityJava</id>
          </State>
          <State>
            <id>Code style issuesJava</id>
          </State>
          <State>
            <id>Control flow issuesJava</id>
          </State>
          <State>
            <id>Data flowJava</id>
          </State>
          <State>
            <id>Declaration redundancyJava</id>
          </State>
          <State>
            <id>EncapsulationJava</id>
          </State>
          <State>
            <id>GeneralObjective-C</id>
          </State>
          <State>
            <id>InitializationJava</id>
          </State>
          <State>
            <id>JSON and JSON5</id>
          </State>
          <State>
            <id>Java</id>
          </State>
          <State>
            <id>Kotlin</id>
          </State>
          <State>
            <id>Objective-C</id>
          </State>
          <State>
            <id>PerformanceJava</id>
          </State>
          <State>
            <id>Probable bugsJava</id>
          </State>
          <State>
            <id>Probable bugsKotlin</id>
          </State>
          <State>
            <id>PropertiesObjective-C</id>
          </State>
          <State>
            <id>Redundant constructsKotlin</id>
          </State>
          <State>
            <id>Style issuesKotlin</id>
          </State>
          <State>
            <id>Threading issuesJava</id>
          </State>
          <State>
            <id>XML</id>
          </State>
        </expanded-state>
        <selected-state>
          <State>
            <id>Groovy</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
</project>