<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/holo_red_dark">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="300dp">

        <com.imooc.lib_common_ui.SpreadView
            android:id="@+id/spreadView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:spread_delay_milliseconds="150"
            app:spread_distance="5"
            app:spread_max_radius="90"
            app:spread_radius="100" />

        <com.imooc.lib_common_ui.circle_image_view.CircleImageView
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:src="@mipmap/ic_launcher" />
    </RelativeLayout>

    <TextView
        android:id="@+id/login_phone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="50dp"
        android:layout_marginBottom="165dp"
        android:background="@drawable/bg_mobile_loggin"
        android:gravity="center"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="手机号登陆"
        android:textColor="@android:color/holo_red_light"
        android:textSize="15sp" />

    <TextView
        android:id="@+id/login_experience"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="50dp"
        android:layout_marginBottom="110dp"
        android:background="@drawable/bg_home_experience"
        android:gravity="center"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="立即体验"
        android:textColor="@android:color/white"
        android:textSize="15sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:text="同意《用户协议》和《隐私政策》《儿童隐私政策》"
        android:textColor="@android:color/white"
        android:textSize="10sp" />
</RelativeLayout>