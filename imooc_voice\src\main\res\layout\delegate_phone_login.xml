<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/img_tab_back"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_margin="10dp"
            android:src="@drawable/ic_left_arrow_black" />

        <TextView
            android:id="@+id/tv_login_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="7"
            android:gravity="center_vertical"
            android:text="手机号登录"
            android:textColor="@color/black"
            android:textSize="19sp" />

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rv_login_phone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible">

        <TextView
            android:id="@+id/tv_show_auto_signin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="40dp"
            android:text="未注册手机号登录后将自动创建账号"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/tv_86"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_show_auto_signin"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="20dp"
            android:text="+86"
            android:textSize="19sp" />

        <android.support.design.widget.TextInputEditText
            android:id="@+id/et_login_phone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_show_auto_signin"
            android:layout_alignBottom="@+id/tv_86"
            android:layout_marginTop="20dp"
            android:layout_toRightOf="@+id/tv_86"
            android:background="@null"
            android:paddingLeft="10dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_below="@+id/tv_86"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp"
            android:background="@color/app_background" />

        <TextView
            android:id="@+id/tv_login_next"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="40dp"
            android:layout_marginTop="150dp"
            android:layout_marginEnd="50dp"
            android:layout_marginBottom="110dp"
            android:background="@drawable/bg_home_experience"
            android:gravity="center"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:text="下一步"
            android:textColor="@android:color/white"
            android:textSize="15sp" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/rv_login_password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/et_login_password"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_weight="8"
                android:background="@null"
                android:inputType="textPassword"
                android:paddingLeft="10dp" />

            <TextView
                android:id="@+id/tv_login_forget"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:text="忘记密码?"
                android:textColor="@color/blue"
                android:textSize="11sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_below="@+id/et_login_password"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp"
            android:background="@color/app_background" />

        <TextView
            android:id="@+id/tv_login_login"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="40dp"
            android:layout_marginTop="80dp"
            android:layout_marginEnd="50dp"
            android:layout_marginBottom="110dp"
            android:background="@drawable/bg_home_experience"
            android:gravity="center"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:text="登录"
            android:textColor="@android:color/white"
            android:textSize="15sp" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rl_captcha"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_capcha_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:text="验证码已发送至"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_captcha_timer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="5dp"
            android:layout_marginRight="20dp"
            android:text="58秒" />

        <TextView
            android:id="@+id/tv_capcha_phone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_capcha_title"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="20dp"
            android:text="150****1555" />

        <com.imooc.lib_common_ui.widget.CaptchaView
            android:id="@+id/captcha_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_capcha_phone"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp" />


    </RelativeLayout>
</LinearLayout>