<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Android Automotive 支持 -->
    <uses-feature
        android:name="android.hardware.type.automotive"
        android:required="false" />

    <!-- 音频相关权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" />

    <!-- 网络和存储权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <!-- 移除过时的权限 -->
    <!-- <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" /> -->
    <!-- <uses-permission android:name="android.permission.BROADCAST_STICKY" /> -->
    <!-- <uses-permission android:name="android.permission.WRITE_SETTINGS" /> -->
    <!-- <uses-permission android:name="android.permission.DISABLE_KEYGUARD" /> -->
    <!-- <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> -->
    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:name=".application.ImoocVoiceApplication"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:largeHeap="true"
        android:hardwareAccelerated="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        tools:targetApi="31">

        <!-- Android Automotive 应用元数据 -->
        <meta-data
            android:name="com.google.android.gms.car.application"
            android:resource="@xml/automotive_app_desc" />

        <activity android:name=".ImoocActivity"
            android:windowSoftInputMode="adjustPan|stateHidden"
            android:configChanges="orientation|screenSize|keyboardHidden|screenLayout"
            android:screenOrientation="landscape"
            android:theme="@style/AppTheme.FullScreen"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <!-- Android Automotive 启动器支持 -->
                <category android:name="android.intent.category.CAR_LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

</manifest>
