<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gray">

    <ImageView
        android:id="@+id/needle"
        android:layout_width="100dp"
        android:layout_height="170dp"
        android:layout_below="@+id/title_layout"
        android:layout_alignParentRight="true"
        android:layout_marginTop="-16dp"
        android:layout_marginRight="100dp"
        android:rotation="-30"
        android:src="@mipmap/ic_play_neddle"
        android:transformPivotX="15.1dp"
        android:transformPivotY="15.1dp" />
    <!--标题布局-->
    <RelativeLayout
        android:id="@+id/title_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="12dp"
        android:paddingTop="8dp"
        android:paddingEnd="12dp">

        <ImageView
            android:id="@+id/back_view"
            android:layout_width="30dp"
            android:layout_height="25dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/audio_af9" />

        <ImageView
            android:id="@+id/share_view"
            android:layout_width="30dp"
            android:layout_height="25dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:src="@mipmap/audio_share" />

        <LinearLayout
            android:id="@+id/title_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:layout_toStartOf="@id/share_view"
            android:layout_toEndOf="@id/back_view"
            android:orientation="vertical">

            <TextView
                android:id="@+id/album_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="marquee"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:marqueeRepeatLimit="-1"
                android:singleLine="true"
                android:textColor="@android:color/white"
                android:textSize="17sp" />

            <TextView
                android:id="@+id/author_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#dddddd"
                android:textSize="13sp" />
        </LinearLayout>

    </RelativeLayout>

    <com.imooc.lib_audio.mediaplayer.view.IndictorView
        android:id="@+id/indictor_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/title_layout"
        android:visibility="visible" />

    <com.imooc.lib_common_ui.lrc.LrcView
        android:id="@+id/lrc_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/bottom_layout"
        android:layout_below="@+id/title_layout"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/operation_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/bottom_layout"
        android:orientation="horizontal"
        android:paddingStart="30dp"
        android:paddingEnd="30dp"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center">

            <ImageView
                android:id="@+id/favourite_view"
                android:layout_width="43dp"
                android:layout_height="43dp"
                android:scaleType="fitXY"
                android:src="@drawable/ic_like_white" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center">

            <ImageView
                android:layout_width="43dp"
                android:layout_height="43dp"
                android:src="@drawable/ic_download_white" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center">

            <ImageView
                android:layout_width="43dp"
                android:layout_height="43dp"
                android:scaleType="fitXY"
                android:src="@drawable/ic_yinxiao" />
        </LinearLayout>

        <LinearLayout

            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:id="@+id/tv_comment_num"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignLeft="@+id/iv_comment"
                    android:layout_marginTop="7dp"
                    android:layout_marginLeft="28dp"
                    android:textSize="10sp"
                    android:textColor="@color/white"/>
                <ImageView
                    android:id="@+id/iv_comment"
                    android:layout_width="43dp"
                    android:layout_height="43dp"
                    android:scaleType="fitXY"
                    android:src="@drawable/ic_commect_white" />
            </RelativeLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center">

            <ImageView
                android:layout_width="43dp"
                android:layout_height="43dp"
                android:scaleType="fitXY"
                android:src="@drawable/ic_song_more_audio" />
        </LinearLayout>

    </LinearLayout>
    <!--底部布局-->
    <RelativeLayout
        android:id="@+id/bottom_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:paddingStart="12sp"
        android:paddingTop="10dp"
        android:paddingEnd="12dp"
        android:paddingBottom="25dp">


        <RelativeLayout
            android:id="@+id/time_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="20dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp">

            <TextView
                android:id="@+id/start_time_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:text="00:00"
                android:textColor="@android:color/white"
                android:textSize="9sp" />

            <TextView
                android:id="@+id/total_time_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:text="00:00"
                android:textColor="#dddddd"
                android:textSize="9sp" />

            <SeekBar
                android:id="@+id/progress_view"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@id/total_time_view"
                android:layout_toEndOf="@id/start_time_view"
                android:indeterminate="false"
                android:maxHeight="1dp"
                android:minHeight="1dp"
                android:progressDrawable="@drawable/bg_seekbar"
                android:thumb="@drawable/bg_circle_white" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/music_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/time_layout"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="30dp"
            android:paddingEnd="30dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center">

                <ImageView
                    android:id="@+id/play_mode_view"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:scaleType="fitXY" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center">

                <ImageView
                    android:id="@+id/previous_view"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:scaleType="fitXY"
                    android:src="@mipmap/audio_ajb" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center">

                <ImageView
                    android:id="@+id/play_view"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:scaleType="fitXY"
                    android:src="@mipmap/audio_aj6" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center">

                <ImageView
                    android:id="@+id/next_view"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:scaleType="fitXY"
                    android:src="@mipmap/audio_aja" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center">

                <ImageView
                    android:id="@+id/show_list_view"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:src="@mipmap/audio_cat" />
            </LinearLayout>

        </LinearLayout>

    </RelativeLayout>
</RelativeLayout>