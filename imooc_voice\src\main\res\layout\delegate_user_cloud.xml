<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/img_cloud_back"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_margin="10dp"
            android:src="@drawable/ic_left_arrow_black" />

        <TextView
            android:id="@+id/tv_user_cloud_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:layout_toRightOf="@+id/img_cloud_back"
            android:text="音乐云盘"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:textSize="17sp" />

        <TextView
            android:id="@+id/tv_user_cloud_size"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_user_cloud_title"
            android:layout_alignLeft="@+id/tv_user_cloud_title"
            android:text="0.1G/60G"
            android:textSize="14sp" />

        <ImageView
            android:id="@+id/img_tab_search"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_centerVertical="true"
            android:layout_marginRight="15dp"
            android:layout_toLeftOf="@+id/img_tab_more"
            android:src="@drawable/ic_search_gray" />

        <ImageView
            android:id="@+id/img_tab_more"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"
            android:src="@drawable/ic_upload_gray" />
    </RelativeLayout>

    <android.support.v7.widget.RecyclerView
        android:id="@+id/rv_cloud_music"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>