<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_dialog_border_white">

    <TextView
        android:id="@+id/tv_collect_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        android:text="收藏到歌单"
        android:textColor="@color/color_333333"
        android:textSize="16sp"
        android:textStyle="bold" />

    <ImageView
        android:layout_width="20dp"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/tv_multiple_choose"
        android:layout_alignBottom="@+id/tv_multiple_choose"
        android:layout_marginRight="10dp"
        android:layout_toLeftOf="@+id/tv_multiple_choose"
        android:src="@drawable/ic_menu_black" />

    <TextView
        android:id="@+id/tv_multiple_choose"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/tv_collect_title"
        android:layout_alignParentRight="true"
        android:layout_marginRight="20dp"
        android:text="多选"
        android:textColor="@color/black"
        android:textSize="13sp" />

    <android.support.v7.widget.RecyclerView
        android:id="@+id/recycler_playlist"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_collect_title" />

</RelativeLayout>