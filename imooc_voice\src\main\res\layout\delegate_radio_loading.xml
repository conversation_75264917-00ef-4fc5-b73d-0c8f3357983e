<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:id="@+id/ll_radio_recommend_header"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="50dp">
        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="15dp"
            android:gravity="center_vertical"
            android:layout_weight="8"
            android:padding="3dp"
            android:text="推荐电台"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_radio_recommend_header_change"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="15dp"
            android:padding="5dp"
            android:background="@drawable/bg_discover_gedan"
            android:text="换一换"
            android:textColor="@color/black"
            android:textSize="10sp" />
    </LinearLayout>
    <android.support.v7.widget.RecyclerView
        android:id="@+id/rv_radio_loading"
        android:layout_below="@+id/ll_radio_recommend_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
</RelativeLayout>