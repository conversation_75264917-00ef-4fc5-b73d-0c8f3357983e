package com.imooc.lib_api.model.banner;

import java.util.List;

/**
 * 从api获取的Banner
 */
public class BannerBean {

    /**
     * banners : [{"pic":"http://p1.music.126.net/FO8UwGJgs2o0v792EX7DfA==/109951164209688558.jpg","targetId":0,"adid":null,"targetType":3000,"titleColor":"blue","typeTitle":"数字专辑","url":"https://music.163.com/store/newalbum/detail?id=80316851","adurlV2":null,"exclusive":false,"monitorImpress":null,"monitorClick":null,"monitorType":null,"monitorImpressList":[],"monitorClickList":[],"monitorBlackList":null,"extMonitor":null,"extMonitorInfo":null,"adSource":null,"adLocation":null,"encodeId":"0","program":null,"event":null,"video":null,"song":null,"bannerId":"1562938875548669","alg":null,"scm":"1.music-homepage.homepage_banner_force.banner.488515.1288604071.null","requestId":"","showAdTag":true,"pid":null},{"pic":"http://p1.music.126.net/0uIZ2Dn4YkBGnHmbhgE1DQ==/109951164211876842.jpg","targetId":1349892968,"adid":null,"targetType":1,"titleColor":"red","typeTitle":"VIP专享","url":null,"adurlV2":null,"exclusive":false,"monitorImpress":null,"monitorClick":null,"monitorType":null,"monitorImpressList":[],"monitorClickList":[],"monitorBlackList":null,"extMonitor":null,"extMonitorInfo":null,"adSource":null,"adLocation":null,"encodeId":"1349892968","program":null,"event":null,"video":null,"song":{"name":"Lovebird","id":1349892968,"pst":0,"t":0,"ar":[{"id":92526,"name":"Far East Movement","tns":[],"alias":[]},{"id":1047015,"name":"张艺兴","tns":[],"alias":[]}],"alia":[],"pop":100,"st":0,"rt":"","fee":1,"v":3,"crbt":null,"cf":"","al":{"id":75761380,"name":"Lovebird","picUrl":"http://p1.music.126.net/zRzQpd8ZMWe-OwZTfSpUFg==/109951163918544809.jpg","tns":[],"pic_str":"109951163918544809","pic":109951163918544820},"dt":207986,"h":{"br":320000,"fid":0,"size":8321611,"vd":-2},"m":{"br":192000,"fid":0,"size":4992984,"vd":-2},"l":{"br":128000,"fid":0,"size":3328671,"vd":-2},"a":null,"cd":"01","no":1,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":0,"s_id":0,"mark":8192,"mst":9,"cp":1405820,"mv":10858575,"rtype":0,"rurl":null,"publishTime":0,"privilege":{"id":1349892968,"fee":1,"payed":0,"st":0,"pl":0,"dl":0,"sp":0,"cp":0,"subp":0,"cs":false,"maxbr":999000,"fl":0,"toast":false,"flag":1092,"preSell":false}},"bannerId":"1563028606666919","alg":null,"scm":"1.music-homepage.homepage_banner_force.banner.481817.1288637741.null","requestId":"","showAdTag":true,"pid":null},{"pic":"http://p1.music.126.net/-PWM4jTx24usZvzUW8JRwg==/109951164212693518.jpg","targetId":1325898847,"adid":null,"targetType":1,"titleColor":"red","typeTitle":"VIP专享","url":null,"adurlV2":null,"exclusive":false,"monitorImpress":null,"monitorClick":null,"monitorType":null,"monitorImpressList":[],"monitorClickList":[],"monitorBlackList":null,"extMonitor":null,"extMonitorInfo":null,"adSource":null,"adLocation":null,"encodeId":"1325898847","program":null,"event":null,"video":null,"song":{"name":"Bad Habit","id":1325898847,"pst":0,"t":0,"ar":[{"id":92526,"name":"Far East Movement","tns":[],"alias":[]},{"id":1203045,"name":"艾热","tns":[],"alias":[]},{"id":819003,"name":"Justine Skye","tns":[],"alias":[]}],"alia":[],"pop":100,"st":0,"rt":null,"fee":1,"v":4,"crbt":null,"cf":"","al":{"id":74267322,"name":"Bad Habit","picUrl":"http://p1.music.126.net/zxEiS1F2_xIFsS-TDV5Yog==/109951163691378676.jpg","tns":[],"pic_str":"109951163691378676","pic":109951163691378670},"dt":254457,"h":null,"m":null,"l":{"br":1411000,"fid":0,"size":10180485,"vd":-2},"a":null,"cd":"01","no":0,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":0,"s_id":0,"mark":8192,"mst":9,"cp":1405820,"mv":10839018,"rtype":0,"rurl":null,"publishTime":0,"privilege":{"id":1325898847,"fee":1,"payed":0,"st":0,"pl":0,"dl":0,"sp":0,"cp":0,"subp":0,"cs":false,"maxbr":999000,"fl":0,"toast":false,"flag":1092,"preSell":false}},"bannerId":"1563072323599594","alg":null,"scm":"1.music-homepage.homepage_banner_force.banner.479845.1288456793.null","requestId":"","showAdTag":true,"pid":null},{"pic":"http://p1.music.126.net/Q-ysRH7R0KffOPrYUu3Ygg==/109951164211866597.jpg","targetId":0,"adid":null,"targetType":3000,"titleColor":"blue","typeTitle":"活动","url":"https://music.163.com/m/at/5d28a4a7233fbb734db40d5d","adurlV2":null,"exclusive":false,"monitorImpress":null,"monitorClick":null,"monitorType":null,"monitorImpressList":[],"monitorClickList":[],"monitorBlackList":null,"extMonitor":null,"extMonitorInfo":null,"adSource":null,"adLocation":null,"encodeId":"0","program":null,"event":null,"video":null,"song":null,"bannerId":"1563028344018224","alg":null,"scm":"1.music-homepage.homepage_banner_force.banner.488537.1288573077.null","requestId":"","showAdTag":true,"pid":null},{"pic":"http://p1.music.126.net/SwRWflQi-dQL7gVD9IQ-MQ==/109951164211855867.jpg","targetId":1377515393,"adid":null,"targetType":1,"titleColor":"red","typeTitle":"独家","url":null,"adurlV2":null,"exclusive":false,"monitorImpress":null,"monitorClick":null,"monitorType":null,"monitorImpressList":[],"monitorClickList":[],"monitorBlackList":null,"extMonitor":null,"extMonitorInfo":null,"adSource":null,"adLocation":null,"encodeId":"1377515393","program":null,"event":null,"video":null,"song":{"name":"七月与安生","id":1377515393,"pst":0,"t":0,"ar":[{"id":12898054,"name":"沈月","tns":[],"alias":[]},{"id":32731198,"name":"陈都灵","tns":[],"alias":[]}],"alia":["电视剧《七月与安生》同名主题曲"],"pop":5,"st":0,"rt":"","fee":8,"v":3,"crbt":null,"cf":"","al":{"id":80361191,"name":"七月与安生","picUrl":"http://p1.music.126.net/tgQ7R7G2WtDXqbBhRkkzpQ==/109951164208846574.jpg","tns":[],"pic_str":"109951164208846574","pic":109951164208846580},"dt":244010,"h":{"br":320000,"fid":0,"size":9763570,"vd":-55157},"m":{"br":192000,"fid":0,"size":5858160,"vd":-52589},"l":{"br":128000,"fid":0,"size":3905454,"vd":-51005},"a":null,"cd":"01","no":0,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":0,"s_id":0,"mark":8192,"mst":9,"cp":707010,"mv":0,"rtype":0,"rurl":null,"publishTime":0,"privilege":{"id":1377515393,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":68,"preSell":false}},"bannerId":"1563028113304239","alg":null,"scm":"1.music-homepage.homepage_banner_force.banner.488538.1288666387.null","requestId":"","showAdTag":true,"pid":null},{"pic":"http://p1.music.126.net/kH_Jm6ID0ZYq_dpGfe8FLA==/109951164211847402.jpg","targetId":1377729716,"adid":null,"targetType":1,"titleColor":"red","typeTitle":"独家","url":null,"adurlV2":null,"exclusive":false,"monitorImpress":null,"monitorClick":null,"monitorType":null,"monitorImpressList":[],"monitorClickList":[],"monitorBlackList":null,"extMonitor":null,"extMonitorInfo":null,"adSource":null,"adLocation":null,"encodeId":"1377729716","program":null,"event":null,"video":null,"song":{"name":"夏日限定","id":1377729716,"pst":0,"t":0,"ar":[{"id":12358435,"name":"很美味","tns":[],"alias":[]},{"id":768266,"name":"Galaxy Knight","tns":[],"alias":[]}],"alia":[],"pop":5,"st":0,"rt":"","fee":8,"v":6,"crbt":null,"cf":"","al":{"id":80377531,"name":"夏日限定","picUrl":"http://p1.music.126.net/ecaQKiT5XV-pv-NuzxN1UA==/109951164211396263.jpg","tns":[],"pic_str":"109951164211396263","pic":109951164211396260},"dt":189507,"h":{"br":320000,"fid":0,"size":7582868,"vd":-35524},"m":{"br":192000,"fid":0,"size":4549738,"vd":-32939},"l":{"br":128000,"fid":0,"size":3033173,"vd":-31330},"a":null,"cd":"01","no":0,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":0,"s_id":0,"mark":0,"mst":9,"cp":0,"mv":0,"rtype":0,"rurl":null,"publishTime":0,"privilege":{"id":1377729716,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":64,"preSell":false}},"bannerId":"1563027942570987","alg":null,"scm":"1.music-homepage.homepage_banner_force.banner.481814.1288638456.null","requestId":"","showAdTag":true,"pid":null},{"pic":"http://p1.music.126.net/xNO5TcnxoXuXj0g1It89vg==/109951164213690237.jpg","targetId":1377450530,"adid":null,"targetType":1,"titleColor":"red","typeTitle":"新歌首发","url":null,"adurlV2":null,"exclusive":false,"monitorImpress":null,"monitorClick":null,"monitorType":null,"monitorImpressList":[],"monitorClickList":[],"monitorBlackList":null,"extMonitor":null,"extMonitorInfo":null,"adSource":null,"adLocation":null,"encodeId":"1377450530","program":null,"event":null,"video":null,"song":{"name":"bad guy (with Justin Bieber)","id":1377450530,"pst":0,"t":0,"ar":[{"id":11972054,"name":"Billie Eilish","tns":[],"alias":[]},{"id":35531,"name":"Justin Bieber","tns":[],"alias":[]}],"alia":[],"pop":100,"st":0,"rt":"","fee":8,"v":6,"crbt":null,"cf":"","al":{"id":80352303,"name":"bad guy (with Justin Bieber)","picUrl":"http://p1.music.126.net/9u0Yh6-yefEGNTFZ4ZNjNg==/109951164208231738.jpg","tns":[],"pic_str":"109951164208231738","pic":109951164208231740},"dt":194873,"h":{"br":320000,"fid":0,"size":7796027,"vd":-61878},"m":{"br":192000,"fid":0,"size":4677634,"vd":-59226},"l":{"br":128000,"fid":0,"size":3118437,"vd":-57426},"a":null,"cd":"01","no":1,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":1,"s_id":0,"mark":8192,"mst":9,"cp":7003,"mv":0,"rtype":0,"rurl":null,"publishTime":1562774400000,"privilege":{"id":1377450530,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":4,"preSell":false}},"bannerId":"1563105903170656","alg":null,"scm":"1.music-homepage.homepage_banner_force.banner.482860.1288661728.null","requestId":"","showAdTag":true,"pid":null},{"pic":"http://p1.music.126.net/FO5CvbtZNOo2LNOLKZS9HQ==/109951164211847309.jpg","targetId":0,"adid":null,"targetType":3000,"titleColor":"blue","typeTitle":"活动","url":"https://music.163.com/m/at/5d1ee976d7458e3f960b1bc6","adurlV2":null,"exclusive":false,"monitorImpress":null,"monitorClick":null,"monitorType":null,"monitorImpressList":[],"monitorClickList":[],"monitorBlackList":null,"extMonitor":null,"extMonitorInfo":null,"adSource":null,"adLocation":null,"encodeId":"0","program":null,"event":null,"video":null,"song":null,"bannerId":"1563027873059636","alg":null,"scm":"1.music-homepage.homepage_banner_force.banner.481815.1288602864.null","requestId":"","showAdTag":true,"pid":null},{"pic":"http://p1.music.126.net/9lf0wquHDLGXy2ZBXq4rzA==/109951164211843405.jpg","targetId":43359638,"adid":null,"targetType":1005,"titleColor":"blue","typeTitle":"演出专栏","url":null,"adurlV2":null,"exclusive":false,"monitorImpress":null,"monitorClick":null,"monitorType":null,"monitorImpressList":[],"monitorClickList":[],"monitorBlackList":null,"extMonitor":null,"extMonitorInfo":null,"adSource":null,"adLocation":null,"encodeId":"43359638","program":null,"event":null,"video":null,"song":null,"bannerId":"1563027836372103","alg":null,"scm":"1.music-homepage.homepage_banner_force.banner.481816.1288659806.null","requestId":"","showAdTag":true,"pid":null}]
     * code : 200
     */

    private int code;
    private List<BannersBean> banners;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public List<BannersBean> getBanners() {
        return banners;
    }

    public void setBanners(List<BannersBean> banners) {
        this.banners = banners;
    }

    public static class BannersBean {
        /**
         * pic : http://p1.music.126.net/FO8UwGJgs2o0v792EX7DfA==/109951164209688558.jpg
         * targetId : 0
         * adid : null
         * targetType : 3000
         * titleColor : blue
         * typeTitle : 数字专辑
         * url : https://music.163.com/store/newalbum/detail?id=80316851
         * adurlV2 : null
         * exclusive : false
         * monitorImpress : null
         * monitorClick : null
         * monitorType : null
         * monitorImpressList : []
         * monitorClickList : []
         * monitorBlackList : null
         * extMonitor : null
         * extMonitorInfo : null
         * adSource : null
         * adLocation : null
         * encodeId : 0
         * program : null
         * event : null
         * video : null
         * song : null
         * bannerId : 1562938875548669
         * alg : null
         * scm : 1.music-homepage.homepage_banner_force.banner.488515.1288604071.null
         * requestId :
         * showAdTag : true
         * pid : null
         */

        private String pic;
        private long targetId;
        private Object adid;
        private int targetType;
        private String titleColor;
        private String typeTitle;
        private String url;
        private Object adurlV2;
        private boolean exclusive;
        private Object monitorImpress;
        private Object monitorClick;
        private Object monitorType;
        private Object monitorBlackList;
        private Object extMonitor;
        private Object extMonitorInfo;
        private Object adSource;
        private Object adLocation;
        private String encodeId;
        private Object program;
        private Object event;
        private Object video;
        private Object song;
        private String bannerId;
        private Object alg;
        private String scm;
        private String requestId;
        private boolean showAdTag;
        private Object pid;
        private List<?> monitorImpressList;
        private List<?> monitorClickList;

        public String getPic() {
            return pic;
        }

        public void setPic(String pic) {
            this.pic = pic;
        }

        public long getTargetId() {
            return targetId;
        }

        public void setTargetId(long targetId) {
            this.targetId = targetId;
        }

        public Object getAdid() {
            return adid;
        }

        public void setAdid(Object adid) {
            this.adid = adid;
        }

        public int getTargetType() {
            return targetType;
        }

        public void setTargetType(int targetType) {
            this.targetType = targetType;
        }

        public String getTitleColor() {
            return titleColor;
        }

        public void setTitleColor(String titleColor) {
            this.titleColor = titleColor;
        }

        public String getTypeTitle() {
            return typeTitle;
        }

        public void setTypeTitle(String typeTitle) {
            this.typeTitle = typeTitle;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public Object getAdurlV2() {
            return adurlV2;
        }

        public void setAdurlV2(Object adurlV2) {
            this.adurlV2 = adurlV2;
        }

        public boolean isExclusive() {
            return exclusive;
        }

        public void setExclusive(boolean exclusive) {
            this.exclusive = exclusive;
        }

        public Object getMonitorImpress() {
            return monitorImpress;
        }

        public void setMonitorImpress(Object monitorImpress) {
            this.monitorImpress = monitorImpress;
        }

        public Object getMonitorClick() {
            return monitorClick;
        }

        public void setMonitorClick(Object monitorClick) {
            this.monitorClick = monitorClick;
        }

        public Object getMonitorType() {
            return monitorType;
        }

        public void setMonitorType(Object monitorType) {
            this.monitorType = monitorType;
        }

        public Object getMonitorBlackList() {
            return monitorBlackList;
        }

        public void setMonitorBlackList(Object monitorBlackList) {
            this.monitorBlackList = monitorBlackList;
        }

        public Object getExtMonitor() {
            return extMonitor;
        }

        public void setExtMonitor(Object extMonitor) {
            this.extMonitor = extMonitor;
        }

        public Object getExtMonitorInfo() {
            return extMonitorInfo;
        }

        public void setExtMonitorInfo(Object extMonitorInfo) {
            this.extMonitorInfo = extMonitorInfo;
        }

        public Object getAdSource() {
            return adSource;
        }

        public void setAdSource(Object adSource) {
            this.adSource = adSource;
        }

        public Object getAdLocation() {
            return adLocation;
        }

        public void setAdLocation(Object adLocation) {
            this.adLocation = adLocation;
        }

        public String getEncodeId() {
            return encodeId;
        }

        public void setEncodeId(String encodeId) {
            this.encodeId = encodeId;
        }

        public Object getProgram() {
            return program;
        }

        public void setProgram(Object program) {
            this.program = program;
        }

        public Object getEvent() {
            return event;
        }

        public void setEvent(Object event) {
            this.event = event;
        }

        public Object getVideo() {
            return video;
        }

        public void setVideo(Object video) {
            this.video = video;
        }

        public Object getSong() {
            return song;
        }

        public void setSong(Object song) {
            this.song = song;
        }

        public String getBannerId() {
            return bannerId;
        }

        public void setBannerId(String bannerId) {
            this.bannerId = bannerId;
        }

        public Object getAlg() {
            return alg;
        }

        public void setAlg(Object alg) {
            this.alg = alg;
        }

        public String getScm() {
            return scm;
        }

        public void setScm(String scm) {
            this.scm = scm;
        }

        public String getRequestId() {
            return requestId;
        }

        public void setRequestId(String requestId) {
            this.requestId = requestId;
        }

        public boolean isShowAdTag() {
            return showAdTag;
        }

        public void setShowAdTag(boolean showAdTag) {
            this.showAdTag = showAdTag;
        }

        public Object getPid() {
            return pid;
        }

        public void setPid(Object pid) {
            this.pid = pid;
        }

        public List<?> getMonitorImpressList() {
            return monitorImpressList;
        }

        public void setMonitorImpressList(List<?> monitorImpressList) {
            this.monitorImpressList = monitorImpressList;
        }

        public List<?> getMonitorClickList() {
            return monitorClickList;
        }

        public void setMonitorClickList(List<?> monitorClickList) {
            this.monitorClickList = monitorClickList;
        }

        @Override
        public String toString() {
            return "BannersBean{" +
                    "pic='" + pic + '\'' +
                    ", targetId=" + targetId +
                    ", adid=" + adid +
                    ", targetType=" + targetType +
                    ", titleColor='" + titleColor + '\'' +
                    ", typeTitle='" + typeTitle + '\'' +
                    ", url='" + url + '\'' +
                    ", adurlV2=" + adurlV2 +
                    ", exclusive=" + exclusive +
                    ", monitorImpress=" + monitorImpress +
                    ", monitorClick=" + monitorClick +
                    ", monitorType=" + monitorType +
                    ", monitorBlackList=" + monitorBlackList +
                    ", extMonitor=" + extMonitor +
                    ", extMonitorInfo=" + extMonitorInfo +
                    ", adSource=" + adSource +
                    ", adLocation=" + adLocation +
                    ", encodeId='" + encodeId + '\'' +
                    ", program=" + program +
                    ", event=" + event +
                    ", video=" + video +
                    ", song=" + song +
                    ", bannerId='" + bannerId + '\'' +
                    ", alg=" + alg +
                    ", scm='" + scm + '\'' +
                    ", requestId='" + requestId + '\'' +
                    ", showAdTag=" + showAdTag +
                    ", pid=" + pid +
                    ", monitorImpressList=" + monitorImpressList +
                    ", monitorClickList=" + monitorClickList +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "BannerBean{" +
                "code=" + code +
                ", banners=" + banners +
                '}';
    }
}
