<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="10dp"
    android:paddingBottom="10dp">
    <TextView
        android:id="@+id/tv_item_dj_rank_hours_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginLeft="15dp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:layout_gravity="center_vertical"/>
    <ImageView
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_marginLeft="5dp"
        android:layout_gravity="center_vertical"
        android:src="@drawable/fragmentation_ic_right"/>
</LinearLayout>