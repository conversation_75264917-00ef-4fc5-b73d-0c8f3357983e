<?xml version="1.0" encoding="utf-8"?>


<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/img_mine_radio_back"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_margin="10dp"
            android:src="@drawable/ic_left_arrow_black" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="7"
            android:gravity="center_vertical"
            android:text="我的电台"
            android:textColor="@color/black"
            android:textSize="19sp" />

    </LinearLayout>

    <android.support.v4.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="15dp"
                android:layout_marginBottom="10dp"
                android:text="我创建的电台(0)"
                android:textColor="@color/black"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_marginLeft="30dp"
                    android:src="@drawable/ic_mike" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:text="申请做主播"
                    android:textColor="@color/black" />

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="20dp"
                    android:src="@drawable/fragmentation_ic_right" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="7dp"
                android:background="@color/app_background" />

            <TextView
                android:id="@+id/tv_mine_sub_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="15dp"
                android:layout_marginBottom="10dp"
                android:text="我订阅的电台(0)"
                android:textColor="@color/black"
                android:textStyle="bold" />

            <android.support.v7.widget.RecyclerView
                android:id="@+id/rv_mine_sub_radio"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </LinearLayout>
    </android.support.v4.widget.NestedScrollView>
</LinearLayout>