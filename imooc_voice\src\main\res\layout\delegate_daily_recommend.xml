<?xml version="1.0" encoding="utf-8"?>
<android.support.design.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <android.support.design.widget.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <android.support.design.widget.CollapsingToolbarLayout
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:fitsSystemWindows="true"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <!-- 模糊的背景图片 -->
            <ImageView
                android:id="@+id/iv_background"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                app:layout_collapseMode="parallax" />

            <ImageView
                android:id="@+id/iv_background_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                app:layout_collapseMode="parallax" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_collapseMode="parallax">

                <TextView
                    android:id="@+id/tv_day"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="80dp"
                    android:text="12"
                    android:textColor="#f0f0f0f0"
                    android:textSize="30sp"
                    android:typeface="monospace" />

                <TextView
                    android:id="@+id/tv_month"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBottom="@id/tv_day"
                    android:layout_marginStart="8dp"
                    android:layout_toEndOf="@+id/tv_day"
                    android:text="/7"
                    android:textColor="#f0f0f0"
                    android:textSize="20sp"
                    android:typeface="monospace" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tv_day"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="20dp"
                    android:text="根据你的音乐口味，为你推荐好音乐、好朋友"
                    android:textColor="#f0f0f0" />
            </RelativeLayout>


            <android.support.v7.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="55dp"
                app:layout_collapseMode="pin"
                android:layout_marginBottom="10dp"
                app:contentInsetLeft="0dp"
                app:contentInsetStart="0dp"
                app:contentInsetEnd="0dp"
                app:maxButtonHeight="20dp"
                app:titleMargin="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/img_daily_back"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_margin="10dp"
                        android:src="@drawable/ic_left_arrow_white" />

                    <TextView
                        android:id="@+id/tv_left_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="15sp"
                        android:layout_toEndOf="@id/img_daily_back"
                        android:text="每日推荐"
                        android:textColor="@color/white"
                        android:textSize="18sp"
                        android:typeface="monospace"
                        android:visibility="gone" />


                </LinearLayout>
            </android.support.v7.widget.Toolbar>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="15dp"
                android:layout_marginTop="590dp"
                android:background="@drawable/bg_dailyrecommend"
                app:layout_collapseMode="pin" />
        </android.support.design.widget.CollapsingToolbarLayout>
    </android.support.design.widget.AppBarLayout>

    <RelativeLayout
        android:id="@+id/rl_play"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#ffffff"
        android:paddingBottom="40dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <RelativeLayout
            android:id="@+id/rl_playall_top"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            tools:ignore="RtlSymmetry">

            <RelativeLayout
                android:id="@+id/rl_playall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp">

                <ImageView
                    android:id="@+id/iv_playall"
                    android:layout_width="23dp"
                    android:layout_height="23dp"
                    app:srcCompat="@drawable/ic_play_gray" />

                <TextView
                    android:id="@+id/tv_playall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_toEndOf="@+id/iv_playall"
                    android:text="播放全部"
                    android:textColor="#333333"
                    android:textSize="16sp" />
            </RelativeLayout>
        </RelativeLayout>

        <TextView
            android:id="@+id/tv_gap"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_below="@id/rl_playall_top"
            android:background="#f0f0f0" />


        <FrameLayout
            android:id="@+id/loadframe"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/tv_gap"
            android:paddingLeft="10dp"
            android:paddingRight="10dp" />
    </RelativeLayout>

</android.support.design.widget.CoordinatorLayout>