<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="30dp"
    android:layout_marginBottom="10dp">

    <ImageView
        android:id="@+id/iv_item_album_song"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@drawable/ic_test" />

    <ImageView
        android:id="@+id/iv_item_album_icon"
        android:layout_width="20dp"
        android:layout_height="110dp"
        android:layout_marginTop="10dp"
        android:layout_toRightOf="@+id/iv_item_album_song"
        android:visibility="visible"
        android:src="@drawable/ic_album_attach" />

    <TextView
        android:id="@+id/tv_item_album_song_name"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:layout_below="@id/iv_item_album_song"
        android:layout_alignLeft="@+id/iv_item_album_song"
        android:layout_marginTop="5dp"
        android:maxLines="2"
        android:ellipsize="end"
        android:text="歌名"
        android:textColor="@color/black"
        android:textSize="13sp" />

    <TextView
        android:id="@+id/tv_item_album_song_artist"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_item_album_song_name"
        android:layout_alignLeft="@+id/iv_item_album_song"
        android:layout_marginTop="2dp"
        android:text="作者"
        android:textSize="11sp" />
</RelativeLayout>