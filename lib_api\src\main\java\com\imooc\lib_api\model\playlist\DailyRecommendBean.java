package com.imooc.lib_api.model.playlist;

import com.imooc.lib_api.model.song.SongDetailBean;

import java.util.List;

/**
 * 从api获取的日推Bean
 */
public class DailyRecommendBean {

    /**
     * code : 200
     * recommend : [{"name":"絕對是個夢","id":32705315,"position":13,"alias":[],"status":0,"fee":8,"copyrightId":7003,"disc":"1","no":13,"artists":[{"name":"汤宝如","id":9504,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"宝丽金真开心精选 Vol.2","id":3170209,"type":"专辑","size":13,"picId":7962663209006266,"blurPicUrl":"http://p2.music.126.net/GUnkmhDng1HQ6P3tYc1p3w==/7962663209006266.jpg","companyId":0,"pic":7962663209006266,"picUrl":"http://p2.music.126.net/GUnkmhDng1HQ6P3tYc1p3w==/7962663209006266.jpg","publishTime":717868800007,"description":"","tags":"","company":"宝丽金","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_3170209","artists":[{"name":"群星","id":122455,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":224000,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_32705315","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":0,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":95291317,"size":2695074,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":224000,"volumeDelta":-2.65076E-4},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":95291315,"size":8983164,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":224000,"volumeDelta":-0.38},"mMusic":{"name":null,"id":95291316,"size":4491671,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":224000,"volumeDelta":-2.65076E-4},"lMusic":{"name":null,"id":95291317,"size":2695074,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":224000,"volumeDelta":-2.65076E-4},"reason":"根据你可能喜欢的单曲 电视风云1314","privilege":{"id":32705315,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":4,"preSell":false},"alg":"itembased"},{"name":"听风的歌","id":88802,"position":2,"alias":[],"status":0,"fee":8,"copyrightId":7002,"disc":"1","no":2,"artists":[{"name":"郭富城","id":2852,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"听风的歌","id":8655,"type":"专辑","size":12,"picId":86861418599922,"blurPicUrl":"http://p2.music.126.net/WK1GNCGiiyn_Ifz6Duv41w==/86861418599922.jpg","companyId":0,"pic":86861418599922,"picUrl":"http://p2.music.126.net/WK1GNCGiiyn_Ifz6Duv41w==/86861418599922.jpg","publishTime":846777600000,"description":"","tags":"","company":"华纳唱片","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":1,"copyrightId":7002,"commentThreadId":"R_AL_3_8655","artists":[{"name":"郭富城","id":2852,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":226952,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"600902000006762869","crbt":"3a87a1a1fd1a0e6fb850479d33ed68e4","audition":null,"copyFrom":"","commentThreadId":"R_SO_4_88802","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"transName":null,"sign":null,"mark":0,"bMusic":{"name":"听风的歌","id":10054522,"size":2747269,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":226952,"volumeDelta":-2.65076E-4},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":"听风的歌","id":10054520,"size":9096486,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":226952,"volumeDelta":-2.65076E-4},"mMusic":{"name":"听风的歌","id":10054521,"size":4561630,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":226952,"volumeDelta":-2.65076E-4},"lMusic":{"name":"听风的歌","id":10054522,"size":2747269,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":226952,"volumeDelta":-2.65076E-4},"reason":"根据你可能喜欢的单曲 黄金时段1314","privilege":{"id":88802,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":256,"preSell":false},"alg":"itembased"},{"name":"开心的马骝(2007中国巡回演唱会)","id":30474601,"position":1,"alias":[],"status":0,"fee":0,"copyrightId":0,"disc":"1","no":1,"artists":[{"name":"刘德华","id":3691,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"热门华语195","id":2698114,"type":"专辑","size":33,"picId":3405187512278985,"blurPicUrl":"http://p2.music.126.net/TWHqAQG7F9XxxfcvnlmTEA==/3405187512278985.jpg","companyId":0,"pic":3405187512278985,"picUrl":"http://p2.music.126.net/TWHqAQG7F9XxxfcvnlmTEA==/3405187512278985.jpg","publishTime":1356969600004,"description":"","tags":"","company":"","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":2,"copyrightId":0,"commentThreadId":"R_AL_3_2698114","artists":[{"name":"群星","id":122455,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":224600,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_30474601","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"transName":null,"sign":null,"mark":0,"bMusic":{"name":"开心的马骝(2007中国巡回演唱会)","id":55857636,"size":2696919,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":224600,"volumeDelta":-0.44},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":null,"mMusic":{"name":"开心的马骝(2007中国巡回演唱会)","id":55857637,"size":3595423,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":224600,"volumeDelta":-0.95},"lMusic":{"name":"开心的马骝(2007中国巡回演唱会)","id":55857636,"size":2696919,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":224600,"volumeDelta":-0.44},"reason":"根据你可能喜欢的单曲 无间道(粤)","privilege":{"id":30474601,"fee":0,"payed":0,"st":0,"pl":128000,"dl":128000,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":128000,"fl":999000,"toast":false,"flag":128,"preSell":false},"alg":"itembased"},{"name":"听身体唱歌","id":112322,"position":16,"alias":[],"status":0,"fee":0,"copyrightId":5003,"disc":"1","no":16,"artists":[{"name":"黎明","id":3701,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"Club Sandwich","id":11058,"type":"专辑","size":33,"picId":126443837207430,"blurPicUrl":"http://p2.music.126.net/kCcMi9MoNXXVTKGQLuOAvg==/126443837207430.jpg","companyId":0,"pic":126443837207430,"picUrl":"http://p2.music.126.net/kCcMi9MoNXXVTKGQLuOAvg==/126443837207430.jpg","publishTime":975600000000,"description":"","tags":"","company":"Sony","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":1,"copyrightId":5003,"commentThreadId":"R_AL_3_11058","artists":[{"name":"黎明","id":3701,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":85,"score":85,"starredNum":0,"duration":196336,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":"40ced93593c357b0af16f07051f0cccc","audition":null,"copyFrom":"","commentThreadId":"R_SO_4_112322","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"transName":null,"sign":null,"mark":0,"bMusic":{"name":"听身体唱歌","id":10038291,"size":2386387,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":196336,"volumeDelta":-3.76},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":"听身体唱歌","id":10038289,"size":7879101,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":196336,"volumeDelta":-4.09},"mMusic":{"name":"听身体唱歌","id":10038290,"size":3956033,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":196336,"volumeDelta":-3.69},"lMusic":{"name":"听身体唱歌","id":10038291,"size":2386387,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":196336,"volumeDelta":-3.76},"reason":"根据你可能喜欢的单曲 越夜越有机","privilege":{"id":112322,"fee":0,"payed":0,"st":0,"pl":320000,"dl":320000,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":999000,"toast":false,"flag":128,"preSell":false},"alg":"itembased"},{"name":"相见恨晚","id":280761,"position":7,"alias":[],"status":0,"fee":8,"copyrightId":7001,"disc":"1","no":7,"artists":[{"name":"彭佳慧","id":9102,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"美声荣耀","id":27909,"type":"专辑","size":15,"picId":93458488373078,"blurPicUrl":"http://p2.music.126.net/GpsgjHB_9XgtrBVXt8XX4w==/93458488373078.jpg","companyId":0,"pic":93458488373078,"picUrl":"http://p2.music.126.net/GpsgjHB_9XgtrBVXt8XX4w==/93458488373078.jpg","publishTime":1141142400000,"description":"","tags":"","company":"Sony BMG","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":1,"copyrightId":5003,"commentThreadId":"R_AL_3_27909","artists":[{"name":"彭佳慧","id":9102,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":253000,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"600902000009572681","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_280761","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":64455125,"size":4064742,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":253000,"volumeDelta":-2},"mp3Url":null,"rtype":0,"rurl":null,"mvid":5403169,"hMusic":{"name":null,"id":64455124,"size":10161721,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":253000,"volumeDelta":-1200},"mMusic":{"name":null,"id":93640747,"size":6097068,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":253000,"volumeDelta":-2},"lMusic":{"name":null,"id":64455125,"size":4064742,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":253000,"volumeDelta":-2},"reason":"根据你可能喜欢的单曲 月半小夜曲","privilege":{"id":280761,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":0,"preSell":false},"alg":"itembased"},{"name":"Sign","id":1350265597,"position":0,"alias":["TV动画《五等分的新娘》片尾曲 ； TVアニメ「五等分の花嫁」EDテーマ"],"status":0,"fee":1,"copyrightId":1416160,"disc":"01","no":1,"artists":[{"name":"内田彩","id":18104,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"Sign / Candy Flavor","id":75777544,"type":"EP/Single","size":4,"picId":109951164020161950,"blurPicUrl":"http://p2.music.126.net/xEn36MYi6dTDAny5s9j-zA==/109951164020161954.jpg","companyId":0,"pic":109951164020161950,"picUrl":"http://p2.music.126.net/xEn36MYi6dTDAny5s9j-zA==/109951164020161954.jpg","publishTime":1551801600000,"description":"","tags":"","company":"(P)ZERO-A","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":1,"copyrightId":1416160,"commentThreadId":"R_AL_3_75777544","artists":[{"name":"内田彩","id":18104,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0,"picId_str":"109951164020161954"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":236040,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_1350265597","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":0,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":3751828270,"size":3777559,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":236040,"volumeDelta":-19200},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":3751828268,"size":9443831,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":236040,"volumeDelta":-23100},"mMusic":{"name":null,"id":3751828269,"size":5666316,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":236040,"volumeDelta":-20700},"lMusic":{"name":null,"id":3751828270,"size":3777559,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":236040,"volumeDelta":-19200},"reason":"根据你可能喜欢的单曲 チカっとチカ千花っ♡ (TV Size)","privilege":{"id":1350265597,"fee":1,"payed":0,"st":0,"pl":0,"dl":0,"sp":0,"cp":0,"subp":0,"cs":false,"maxbr":999000,"fl":0,"toast":false,"flag":1092,"preSell":false},"alg":"daily_audition_ts_itembased"},{"name":"词不达意","id":255858,"position":4,"alias":[],"status":0,"fee":8,"copyrightId":7002,"disc":"1","no":4,"artists":[{"name":"林忆莲","id":8336,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"呼吸","id":25554,"type":"专辑","size":14,"picId":19064432114268940,"blurPicUrl":"http://p2.music.126.net/05Mc3MS9L-hQQhgQnWEttg==/19064432114268939.jpg","companyId":0,"pic":19064432114268940,"picUrl":"http://p2.music.126.net/05Mc3MS9L-hQQhgQnWEttg==/19064432114268939.jpg","publishTime":1156435200007,"description":"","tags":"","company":"华纳唱片","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":1,"copyrightId":7002,"commentThreadId":"R_AL_3_25554","artists":[{"name":"林忆莲","id":8336,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0,"picId_str":"19064432114268939"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":266000,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"600902000005475180","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_255858","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":64223517,"size":4264605,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":266000,"volumeDelta":-11900},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":64223516,"size":10661262,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":266000,"volumeDelta":-16200},"mMusic":{"name":null,"id":93656701,"size":6396824,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":266000,"volumeDelta":-13600},"lMusic":{"name":null,"id":64223517,"size":4264605,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":266000,"volumeDelta":-11900},"reason":"根据你可能喜欢的单曲 再见悲哀","privilege":{"id":255858,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":260,"preSell":false},"alg":"itembased"},{"name":"玻璃之情","id":186436,"position":8,"alias":[],"status":0,"fee":8,"copyrightId":7003,"disc":"1","no":8,"artists":[{"name":"张国荣","id":6457,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"I Am What I Am","id":18937,"type":"专辑","size":35,"picId":73667279073787,"blurPicUrl":"http://p2.music.126.net/2YIpNoCzXfYgz4zIw3s0Vg==/73667279073787.jpg","companyId":0,"pic":73667279073787,"picUrl":"http://p2.music.126.net/2YIpNoCzXfYgz4zIw3s0Vg==/73667279073787.jpg","publishTime":1269273600000,"description":"","tags":"","company":"环球唱片","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":1,"copyrightId":5003,"commentThreadId":"R_AL_3_18937","artists":[{"name":"张国荣","id":6457,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":283147,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_186436","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":100793566,"size":4531556,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":283147,"volumeDelta":8200},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":100793564,"size":11328826,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":283147,"volumeDelta":4300},"mMusic":{"name":null,"id":100793565,"size":6797313,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":283147,"volumeDelta":6800},"lMusic":{"name":null,"id":100793566,"size":4531556,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":283147,"volumeDelta":8200},"reason":"根据你可能喜欢的单曲 漫步人生路","privilege":{"id":186436,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":4,"preSell":false},"alg":"itembased"},{"name":"倒刺","id":306763,"position":4,"alias":[],"status":0,"fee":8,"copyrightId":7002,"disc":"1","no":4,"artists":[{"name":"薛凯琪","id":9944,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"Filicious","id":30466,"type":"EP/Single","size":5,"picId":85761906984110,"blurPicUrl":"http://p2.music.126.net/64zSEcTcnnaLxppDqnwy2w==/85761906984110.jpg","companyId":0,"pic":85761906984110,"picUrl":"http://p2.music.126.net/64zSEcTcnnaLxppDqnwy2w==/85761906984110.jpg","publishTime":1340899200000,"description":"","tags":"","company":"华纳唱片","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":1,"copyrightId":5003,"commentThreadId":"R_AL_3_30466","artists":[{"name":"薛凯琪","id":9944,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":256470,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_306763","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"transName":null,"sign":null,"mark":0,"bMusic":{"name":"倒刺","id":10413876,"size":3106801,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":256470,"volumeDelta":-1.81},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":"倒刺","id":10413874,"size":10281801,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":256470,"volumeDelta":-2.15},"mMusic":{"name":"倒刺","id":10413875,"size":5157100,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":256470,"volumeDelta":-1.75},"lMusic":{"name":"倒刺","id":10413876,"size":3106801,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":256470,"volumeDelta":-1.81},"reason":"根据你可能喜欢的单曲 再见悲哀","privilege":{"id":306763,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":260,"preSell":false},"alg":"itembased"},{"name":"遥远的她","id":191232,"position":7,"alias":[],"status":0,"fee":8,"copyrightId":7003,"disc":"1","no":7,"artists":[{"name":"张学友","id":6460,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"遥远的她.Amour","id":19316,"type":"专辑","size":12,"picId":43980465112095,"blurPicUrl":"http://p2.music.126.net/rxyLRMZdqzHdxyP5cl8qQA==/43980465112095.jpg","companyId":0,"pic":43980465112095,"picUrl":"http://p2.music.126.net/rxyLRMZdqzHdxyP5cl8qQA==/43980465112095.jpg","publishTime":505238400000,"description":"","tags":"","company":"宝丽金","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":3,"copyrightId":7003,"commentThreadId":"R_AL_3_19316","artists":[{"name":"张学友","id":6460,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":259000,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_191232","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":99220861,"size":4157901,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":259000,"volumeDelta":3287},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":99220859,"size":10394688,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":259000,"volumeDelta":1123},"mMusic":{"name":null,"id":99220860,"size":6236830,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":259000,"volumeDelta":2190},"lMusic":{"name":null,"id":99220861,"size":4157901,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":259000,"volumeDelta":3287},"reason":"根据你可能喜欢的单曲 漫步人生路","privilege":{"id":191232,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":4,"preSell":false},"alg":"itembased"},{"name":"囍帖街","id":27867484,"position":62,"alias":[],"status":0,"fee":8,"copyrightId":7001,"disc":"1","no":62,"artists":[{"name":"谢安琪","id":9952,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"Concert YY 黄伟文作品展 演唱会","id":2685339,"type":"","size":84,"picId":109951163639097090,"blurPicUrl":"http://p2.music.126.net/eVNxevw1W5lyBmdq18tMJw==/109951163639097093.jpg","companyId":0,"pic":109951163639097090,"picUrl":"http://p2.music.126.net/eVNxevw1W5lyBmdq18tMJw==/109951163639097093.jpg","publishTime":1349193600007,"description":"","tags":"","company":"Wyman Ltd.","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":40,"copyrightId":0,"commentThreadId":"R_AL_3_2685339","artists":[{"name":"黄伟文","id":3078,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"现场版","transName":null,"mark":0,"picId_str":"109951163639097093"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":209000,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_27867484","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":64770109,"size":3359726,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":209000,"volumeDelta":11887},"mp3Url":null,"rtype":0,"rurl":null,"mvid":5570828,"hMusic":{"name":null,"id":64770108,"size":8399060,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":209000,"volumeDelta":12280},"mMusic":{"name":null,"id":92141161,"size":5039504,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":209000,"volumeDelta":12690},"lMusic":{"name":null,"id":64770109,"size":3359726,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":209000,"volumeDelta":11887},"reason":"根据你可能喜欢的单曲 再见悲哀","privilege":{"id":27867484,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":0,"preSell":false},"alg":"itembased"},{"name":"孽海记","id":1370879975,"position":0,"alias":[],"status":0,"fee":8,"copyrightId":1416247,"disc":"01","no":8,"artists":[{"name":"黄诗扶","id":12308369,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"(A) 入梦也","id":79696039,"type":"EP/Single","size":13,"picId":109951164137683060,"blurPicUrl":"http://p2.music.126.net/a5NVmn_xa9QAoXE46n7SJw==/109951164137683050.jpg","companyId":0,"pic":109951164137683060,"picUrl":"http://p2.music.126.net/a5NVmn_xa9QAoXE46n7SJw==/109951164137683050.jpg","publishTime":1560182400000,"description":"","tags":"","company":"锦瑟年华","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":0,"copyrightId":1416247,"commentThreadId":"R_AL_3_79696039","artists":[{"name":"黄诗扶","id":12308369,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0,"picId_str":"109951164137683050"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":229493,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_1370879975","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":0,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":3794264608,"size":3673069,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":229493,"volumeDelta":-21610},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":3794264606,"size":9182607,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":229493,"volumeDelta":-25958},"mMusic":{"name":null,"id":3794264607,"size":5509582,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":229493,"volumeDelta":-23339},"lMusic":{"name":null,"id":3794264608,"size":3673069,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":229493,"volumeDelta":-21610},"reason":"猜你喜欢","privilege":{"id":1370879975,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":68,"preSell":false},"alg":"HG3SPW_1370879975"},{"name":"醉人的一晚","id":115739,"position":8,"alias":[],"status":0,"fee":8,"copyrightId":7003,"disc":"1","no":8,"artists":[{"name":"李克勤","id":3699,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"一千零一夜","id":11325,"type":"专辑","size":11,"picId":36283883728653,"blurPicUrl":"http://p2.music.126.net/PrkqqtemI62OX_N-yiv0fA==/36283883728653.jpg","companyId":0,"pic":36283883728653,"picUrl":"http://p2.music.126.net/PrkqqtemI62OX_N-yiv0fA==/36283883728653.jpg","publishTime":644166000000,"description":"","tags":"","company":"宝丽金","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":1,"copyrightId":5003,"commentThreadId":"R_AL_3_11325","artists":[{"name":"李克勤","id":3699,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":85,"score":85,"starredNum":0,"duration":255582,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"600902000005372086","crbt":"6480620889f80cd0e169e6bc58c005ce","audition":null,"copyFrom":"","commentThreadId":"R_SO_4_115739","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"transName":null,"sign":null,"mark":0,"bMusic":{"name":"醉人的一晚","id":9985807,"size":3089105,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":255582,"volumeDelta":2.63853},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":"醉人的一晚","id":9985805,"size":10239236,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":255582,"volumeDelta":2.05683},"mMusic":{"name":"醉人的一晚","id":9985806,"size":5132298,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":255582,"volumeDelta":2.4805},"lMusic":{"name":"醉人的一晚","id":9985807,"size":3089105,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":255582,"volumeDelta":2.63853},"reason":"根据你可能喜欢的单曲 越夜越有机","privilege":{"id":115739,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":4,"preSell":false},"alg":"itembased"},{"name":"Outside","id":29561077,"position":6,"alias":[],"status":0,"fee":8,"copyrightId":7001,"disc":"1","no":6,"artists":[{"name":"Calvin Harris","id":29968,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},{"name":"Ellie Goulding","id":56598,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"Motion","id":3029170,"type":"专辑","size":17,"picId":18448705602716640,"blurPicUrl":"http://p2.music.126.net/QiZvbiNfkKPq0Tgm9XH4NQ==/18448705602716640.jpg","companyId":0,"pic":18448705602716640,"picUrl":"http://p2.music.126.net/QiZvbiNfkKPq0Tgm9XH4NQ==/18448705602716640.jpg","publishTime":1414972800000,"description":"","tags":"","company":"索尼音乐","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":3,"copyrightId":7001,"commentThreadId":"R_AL_3_3029170","artists":[{"name":"Calvin Harris","id":29968,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0,"picId_str":"18448705602716640"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":227266,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_29561077","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":3429727944,"size":3637542,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":227266,"volumeDelta":-35000},"mp3Url":null,"rtype":0,"rurl":null,"mvid":361398,"hMusic":{"name":null,"id":3429727942,"size":9093791,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":227266,"volumeDelta":-38900},"mMusic":{"name":null,"id":3429727943,"size":5456292,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":227266,"volumeDelta":-36300},"lMusic":{"name":null,"id":3429727944,"size":3637542,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":227266,"volumeDelta":-35000},"reason":"根据你可能喜欢的单曲 2017 End of the Year Mix","privilege":{"id":29561077,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":4,"preSell":false},"alg":"itembased"},{"name":"阴天快乐","id":28563317,"position":9,"alias":[],"status":0,"fee":8,"copyrightId":7003,"disc":"2","no":4,"artists":[{"name":"陈奕迅","id":2116,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"rice & shine","id":2801259,"type":"专辑","size":11,"picId":5962651557619306,"blurPicUrl":"http://p2.music.126.net/EES1U3UVWUdt_tHyiY8XAw==/5962651557619306.jpg","companyId":0,"pic":5962651557619306,"picUrl":"http://p2.music.126.net/EES1U3UVWUdt_tHyiY8XAw==/5962651557619306.jpg","publishTime":1400083200007,"description":"","tags":"","company":"Eas Music Ltd","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":3,"copyrightId":7003,"commentThreadId":"R_AL_3_2801259","artists":[{"name":"陈奕迅","id":2116,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":260000,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_28563317","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":64643898,"size":4168880,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":260000,"volumeDelta":-3200},"mp3Url":null,"rtype":0,"rurl":null,"mvid":377104,"hMusic":{"name":null,"id":64643897,"size":10421968,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":260000,"volumeDelta":-7500},"mMusic":{"name":null,"id":92016558,"size":6253243,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":260000,"volumeDelta":-5000},"lMusic":{"name":null,"id":64643898,"size":4168880,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":260000,"volumeDelta":-3200},"reason":"根据你可能喜欢的单曲 美人鱼","privilege":{"id":28563317,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":4,"preSell":false},"alg":"itembased"},{"name":"Angels (Radio Edit)","id":31477895,"position":1,"alias":[],"status":0,"fee":8,"copyrightId":7001,"disc":"1","no":1,"artists":[{"name":"Vicetone","id":747030,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},{"name":"Kat Nestel","id":1066090,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"Angels (Radio Edit)","id":3124174,"type":"EP/Single","size":1,"picId":18445407067833300,"blurPicUrl":"http://p1.music.126.net/XdY2TsaZh3Qo9a2gdvGVEQ==/18445407067833301.jpg","companyId":0,"pic":18445407067833300,"picUrl":"http://p1.music.126.net/XdY2TsaZh3Qo9a2gdvGVEQ==/18445407067833301.jpg","publishTime":1429488000000,"description":"","tags":"","company":"索尼音乐","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":0,"copyrightId":7001,"commentThreadId":"R_AL_3_3124174","artists":[{"name":"Vicetone","id":747030,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0,"picId_str":"18445407067833301"},"starred":false,"popularity":95,"score":95,"starredNum":0,"duration":214386,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_31477895","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":0,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":3625838557,"size":3430235,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":214386,"volumeDelta":-3},"mp3Url":null,"rtype":0,"rurl":null,"mvid":399143,"hMusic":{"name":null,"id":3625838555,"size":8575521,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":214386,"volumeDelta":-3},"mMusic":{"name":null,"id":3625838556,"size":5145330,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":214386,"volumeDelta":-3},"lMusic":{"name":null,"id":3625838557,"size":3430235,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":214386,"volumeDelta":-3},"reason":"根据你可能喜欢的单曲 2017 End of the Year Mix","privilege":{"id":31477895,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":0,"preSell":false},"alg":"itembased"},{"name":"小孩","id":28341500,"position":5,"alias":[],"status":0,"fee":8,"copyrightId":7002,"disc":"1","no":5,"artists":[{"name":"周柏豪","id":6479,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"同行 ","id":2720159,"type":"EP/Single","size":6,"picId":6069304185496291,"blurPicUrl":"http://p1.music.126.net/_0cP-hlq8BD9i3REtoPgiA==/6069304185496291.jpg","companyId":0,"pic":6069304185496291,"picUrl":"http://p1.music.126.net/_0cP-hlq8BD9i3REtoPgiA==/6069304185496291.jpg","publishTime":1395763200007,"description":"","tags":"","company":"华纳唱片","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":3,"copyrightId":7002,"commentThreadId":"R_AL_3_2720159","artists":[{"name":"周柏豪","id":6479,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":203900,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_28341500","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"transName":"Child","sign":null,"mark":0,"bMusic":{"name":null,"id":3714695238,"size":3263469,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":203900,"volumeDelta":-1},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":3714695236,"size":8158607,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":203900,"volumeDelta":-1},"mMusic":{"name":null,"id":3714695237,"size":4895182,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":203900,"volumeDelta":-1},"lMusic":{"name":null,"id":3714695238,"size":3263469,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":203900,"volumeDelta":-1},"transNames":["Child"],"privilege":{"id":28341500,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":260,"preSell":false},"reason":"根据你可能喜欢的单曲 一事无成","alg":"itembased"},{"name":"一曲相思 ","id":1313558186,"position":1,"alias":[],"status":0,"fee":8,"copyrightId":1403818,"disc":"01","no":1,"artists":[{"name":"半阳","id":30405247,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"一曲相思","id":73468042,"type":"EP/Single","size":4,"picId":109951163575213440,"blurPicUrl":"http://p1.music.126.net/yHRY23bKbLJjjbSnE-T8gA==/109951163575213436.jpg","companyId":0,"pic":109951163575213440,"picUrl":"http://p1.music.126.net/yHRY23bKbLJjjbSnE-T8gA==/109951163575213436.jpg","publishTime":1538150400007,"description":"","tags":"","company":"寿光坚诚文化","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":0,"copyrightId":1403818,"commentThreadId":"R_AL_3_73468042","artists":[{"name":"半阳","id":30405247,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0,"picId_str":"109951163575213436"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":167985,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_1313558186","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":3551965561,"size":2688775,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":167985,"volumeDelta":0},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":3551965559,"size":6721872,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":167985,"volumeDelta":0},"mMusic":{"name":null,"id":3551965560,"size":4033141,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":167985,"volumeDelta":0},"lMusic":{"name":null,"id":3551965561,"size":2688775,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":167985,"volumeDelta":0},"reason":"根据你可能喜欢的单曲 Sanlalala（Remix）","privilege":{"id":1313558186,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":0,"preSell":false},"alg":"itembased"},{"name":"Boomerang (Hokage Ninjia bootleg)","id":1340842066,"position":1,"alias":[],"status":0,"fee":0,"copyrightId":0,"disc":"01","no":1,"artists":[{"name":"DJ Forfeit/丧失","id":31082755,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"Boomerang (Hokage Ninjia bootleg)","id":75246054,"type":"专辑","size":1,"picId":109951163804193810,"blurPicUrl":"http://p1.music.126.net/DNTvDOoD8hon21Szr-fF-w==/109951163804193808.jpg","companyId":0,"pic":109951163804193810,"picUrl":"http://p1.music.126.net/DNTvDOoD8hon21Szr-fF-w==/109951163804193808.jpg","publishTime":1547827200000,"description":"","tags":"","company":null,"briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_75246054","artists":[{"name":"DJ Forfeit/丧失","id":31082755,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"混音版","transName":null,"mark":0,"picId_str":"109951163804193808"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":195058,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_1340842066","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":0,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":3610744248,"size":3122199,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":195058,"volumeDelta":-5},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":3610744246,"size":7805431,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":195058,"volumeDelta":-6},"mMusic":{"name":null,"id":3610744247,"size":4683276,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":195058,"volumeDelta":-6},"lMusic":{"name":null,"id":3610744248,"size":3122199,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":195058,"volumeDelta":-5},"reason":"根据你可能喜欢的单曲 Sanlalala（Remix）","privilege":{"id":1340842066,"fee":0,"payed":0,"st":0,"pl":999000,"dl":999000,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":999000,"toast":false,"flag":128,"preSell":false},"alg":"itembased"},{"name":"Afterglow (Bonus Track)","id":426291389,"position":16,"alias":[],"status":0,"fee":8,"copyrightId":7003,"disc":"1","no":16,"artists":[{"name":"Lindsey Stirling","id":66225,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},{"name":"Vicetone","id":747030,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"Brave Enough (Deluxe Edition)","id":34806274,"type":"专辑","size":18,"picId":1412872456241168,"blurPicUrl":"http://p1.music.126.net/sSRXWCOehgvhhZ55V9jXdQ==/1412872456241168.jpg","companyId":0,"pic":1412872456241168,"picUrl":"http://p1.music.126.net/sSRXWCOehgvhhZ55V9jXdQ==/1412872456241168.jpg","publishTime":1471564800000,"description":"","tags":"","company":"环球唱片","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":3,"copyrightId":7003,"commentThreadId":"R_AL_3_34806274","artists":[{"name":"Lindsey Stirling","id":66225,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":218076,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_426291389","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":1272393088,"size":3490421,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":218076,"volumeDelta":-23400},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":1272393086,"size":8725987,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":218076,"volumeDelta":-26900},"mMusic":{"name":null,"id":1272393087,"size":5235609,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":218076,"volumeDelta":-24700},"lMusic":{"name":null,"id":1272393088,"size":3490421,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":218076,"volumeDelta":-23400},"reason":"根据你可能喜欢的单曲 2017 End of the Year Mix","privilege":{"id":426291389,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":4,"preSell":false},"alg":"itembased"},{"name":"布拉格广场","id":210049,"position":5,"alias":[],"status":0,"fee":8,"copyrightId":7001,"disc":"1","no":5,"artists":[{"name":"蔡依林","id":7219,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},{"name":"周杰伦","id":6452,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"看我72变","id":21349,"type":"专辑","size":11,"picId":109951163611523280,"blurPicUrl":"http://p1.music.126.net/lsMlFshdJ96aTGFFgayh4Q==/109951163611523278.jpg","companyId":0,"pic":109951163611523280,"picUrl":"http://p1.music.126.net/lsMlFshdJ96aTGFFgayh4Q==/109951163611523278.jpg","publishTime":1046966400007,"description":"","tags":"","company":"Sony","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":1,"copyrightId":5003,"commentThreadId":"R_AL_3_21349","artists":[{"name":"蔡依林","id":7219,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":"Magic","mark":0,"picId_str":"109951163611523278","transNames":["Magic"]},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":294000,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"600902000000210868","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_210049","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":63947330,"size":4714716,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":294000,"volumeDelta":-22300},"mp3Url":null,"rtype":0,"rurl":null,"mvid":186025,"hMusic":{"name":null,"id":63947329,"size":11786586,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":294000,"volumeDelta":-26400},"mMusic":{"name":null,"id":93674839,"size":7072006,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":294000,"volumeDelta":-23699},"lMusic":{"name":null,"id":63947330,"size":4714716,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":294000,"volumeDelta":-22300},"reason":"根据你可能喜欢的单曲 美人鱼","privilege":{"id":210049,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":4,"preSell":false},"alg":"itembased"},{"name":"Get On My Knees","id":475221198,"position":1,"alias":[],"status":0,"fee":8,"copyrightId":7003,"disc":"1","no":1,"artists":[{"name":" Brian Deady","id":12149038,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"Get On My Knees","id":35357708,"type":"EP/Single","size":1,"picId":18218907672795744,"blurPicUrl":"http://p1.music.126.net/5xP7C42oACLvzVxX_nWpFw==/18218907672795745.jpg","companyId":0,"pic":18218907672795744,"picUrl":"http://p1.music.126.net/5xP7C42oACLvzVxX_nWpFw==/18218907672795745.jpg","publishTime":1493308800007,"description":"","tags":"","company":"环球唱片","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":3,"copyrightId":7003,"commentThreadId":"R_AL_3_35357708","artists":[{"name":" Brian Deady ","id":12149038,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0,"picId_str":"18218907672795745"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":156055,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_475221198","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":1307220069,"size":2497350,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":156055,"volumeDelta":-18500},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":1307220067,"size":6243309,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":156055,"volumeDelta":-21199},"mMusic":{"name":null,"id":1307220068,"size":3746003,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":156055,"volumeDelta":-19300},"lMusic":{"name":null,"id":1307220069,"size":2497350,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":156055,"volumeDelta":-18500},"reason":"根据你可能喜欢的单曲 Sail","privilege":{"id":475221198,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":4,"preSell":false},"alg":"itembased"},{"name":"Drown","id":517542039,"position":1,"alias":[],"status":0,"fee":8,"copyrightId":743010,"disc":"","no":1,"artists":[{"name":"ZABO","id":12825010,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"Drown","id":36746019,"type":"EP/Single","size":1,"picId":109951163551613570,"blurPicUrl":"http://p1.music.126.net/oDwupZyPmmWj8Iq9N779_g==/109951163551613560.jpg","companyId":0,"pic":109951163551613570,"picUrl":"http://p1.music.126.net/oDwupZyPmmWj8Iq9N779_g==/109951163551613560.jpg","publishTime":1509465600000,"description":"","tags":"","company":"Artist Intelligence Agency","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":0,"copyrightId":743010,"commentThreadId":"R_AL_3_36746019","artists":[{"name":"ZABO","id":12825010,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0,"picId_str":"109951163551613560"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":185142,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_517542039","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":0,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":3449980609,"size":2963374,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":185142,"volumeDelta":-1},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":3449980607,"size":7408370,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":185142,"volumeDelta":-1},"mMusic":{"name":null,"id":3449980608,"size":4445040,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":185142,"volumeDelta":-1},"lMusic":{"name":null,"id":3449980609,"size":2963374,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":185142,"volumeDelta":-1},"reason":"根据你可能喜欢的单曲 Sanlalala（Remix）","privilege":{"id":517542039,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":261,"preSell":false},"alg":"itembased"},{"name":"不染","id":536099160,"position":1,"alias":["电视剧《香蜜沉沉烬如霜》主题曲"],"status":0,"fee":8,"copyrightId":753018,"disc":"01","no":1,"artists":[{"name":"毛不易","id":12138269,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"香蜜沉沉烬如霜 电视原声音乐专辑","id":37548020,"type":"专辑","size":14,"picId":109951163456726960,"blurPicUrl":"http://p1.music.126.net/gEia-o05FSas8uJos54Sug==/109951163456726954.jpg","companyId":0,"pic":109951163456726960,"picUrl":"http://p1.music.126.net/gEia-o05FSas8uJos54Sug==/109951163456726954.jpg","publishTime":1534089600000,"description":"","tags":"","company":"智慧大狗","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":1,"copyrightId":753018,"commentThreadId":"R_AL_3_37548020","artists":[{"name":"群星","id":122455,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0,"picId_str":"109951163456726954"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":325844,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_536099160","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":1417858324,"size":5214502,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":325844,"volumeDelta":-11600},"mp3Url":null,"rtype":0,"rurl":null,"mvid":5841026,"hMusic":{"name":null,"id":1417858322,"size":13036191,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":325844,"volumeDelta":-16100},"mMusic":{"name":null,"id":1417858323,"size":7821732,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":325844,"volumeDelta":-13500},"lMusic":{"name":null,"id":1417858324,"size":5214502,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":325844,"volumeDelta":-11600},"reason":"根据你可能喜欢的单曲 美人鱼","privilege":{"id":536099160,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":68,"preSell":false},"alg":"itembased"},{"name":"JUVES","id":22736827,"position":7,"alias":[],"status":0,"fee":0,"copyrightId":663018,"disc":"1","no":7,"artists":[{"name":"Diggy-MO'","id":160637,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"Diggyism","id":2087740,"type":"专辑","size":13,"picId":873012232478237,"blurPicUrl":"http://p1.music.126.net/-Fk0QyGixznr6FHAvj5fCA==/873012232478237.jpg","companyId":0,"pic":873012232478237,"picUrl":"http://p1.music.126.net/-Fk0QyGixznr6FHAvj5fCA==/873012232478237.jpg","publishTime":1237910400007,"description":"","tags":"","company":"","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":1,"copyrightId":0,"commentThreadId":"R_AL_3_2087740","artists":[{"name":"Diggy-MO'","id":160637,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":90,"score":90,"starredNum":0,"duration":228232,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_22736827","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"transName":null,"sign":null,"mark":0,"bMusic":{"name":"JUVES","id":29072132,"size":2772149,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":228232,"volumeDelta":-4.71},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":"JUVES","id":29072130,"size":9164102,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":228232,"volumeDelta":-5},"mMusic":{"name":"JUVES","id":29072131,"size":4598421,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":228232,"volumeDelta":-4.61},"lMusic":{"name":"JUVES","id":29072132,"size":2772149,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":228232,"volumeDelta":-4.71},"reason":"根据你可能喜欢的单曲 JOJO的奇妙冒险全系列17首佳曲钢琴无缝串烧（JOJO的奇妙钢琴）（Cover：V.A.）","privilege":{"id":22736827,"fee":0,"payed":0,"st":0,"pl":320000,"dl":320000,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":999000,"toast":false,"flag":128,"preSell":false},"alg":"itembased"},{"name":"夕阳之歌(Live)","id":276225,"position":29,"alias":["电影《英雄本色3》主题曲"],"status":0,"fee":0,"copyrightId":0,"disc":"1","no":29,"artists":[{"name":"梅艳芳","id":8918,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"Anita Classic Moment(Live)","id":27436,"type":"专辑","size":29,"picId":43980465112216,"blurPicUrl":"http://p1.music.126.net/0M95B8YEeo5tGUPU306sAw==/43980465112216.jpg","companyId":0,"pic":43980465112216,"picUrl":"http://p1.music.126.net/0M95B8YEeo5tGUPU306sAw==/43980465112216.jpg","publishTime":1072886400000,"description":"","tags":"","company":"Music Nation","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":["梅艳芳 经典金曲演唱会"],"status":1,"copyrightId":0,"commentThreadId":"R_AL_3_27436","artists":[{"name":"梅艳芳","id":8918,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"现场版","transName":null,"mark":0},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":300000,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_276225","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":64394238,"size":4807482,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":300000,"volumeDelta":-2},"mp3Url":null,"rtype":0,"rurl":null,"mvid":5469747,"hMusic":{"name":null,"id":64394237,"size":12018532,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":300000,"volumeDelta":-2},"mMusic":{"name":null,"id":93770054,"size":7211165,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":300000,"volumeDelta":-2},"lMusic":{"name":null,"id":64394238,"size":4807482,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":300000,"volumeDelta":-2},"reason":"根据你可能喜欢的单曲 无间道(粤)","privilege":{"id":276225,"fee":0,"payed":0,"st":0,"pl":320000,"dl":320000,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":999000,"toast":false,"flag":128,"preSell":false},"alg":"itembased"},{"name":"星期五档案","id":214951,"position":3,"alias":[],"status":0,"fee":8,"copyrightId":7003,"disc":"1","no":3,"artists":[{"name":"陈慧琳","id":7235,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"大日子","id":21742,"type":"专辑","size":11,"picId":28587302334267,"blurPicUrl":"http://p1.music.126.net/uTbtzIxIuiYdNP2Gb09DyA==/28587302334267.jpg","companyId":0,"pic":28587302334267,"picUrl":"http://p1.music.126.net/uTbtzIxIuiYdNP2Gb09DyA==/28587302334267.jpg","publishTime":973008000000,"description":"","tags":"","company":"环球唱片","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":1,"copyrightId":7003,"commentThreadId":"R_AL_3_21742","artists":[{"name":"陈慧琳","id":7235,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":25,"score":25,"starredNum":0,"duration":196000,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"600902000005288569","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_214951","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":63978834,"size":3152337,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":196000,"volumeDelta":-32100},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":63978833,"size":7880709,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":196000,"volumeDelta":-35200},"mMusic":{"name":null,"id":93791105,"size":4728461,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":196000,"volumeDelta":-32800},"lMusic":{"name":null,"id":63978834,"size":3152337,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":196000,"volumeDelta":-32100},"reason":"根据你可能喜欢的单曲 恋爱情色","privilege":{"id":214951,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":4,"preSell":false},"alg":"itembased"},{"name":"心做し","id":29713638,"position":1,"alias":[],"status":0,"fee":0,"copyrightId":663018,"disc":"01","no":1,"artists":[{"name":"花たん","id":16523,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"最新热歌慢摇82","id":3024001,"type":null,"size":162,"picId":3286440257121624,"blurPicUrl":"http://p1.music.126.net/hvvqvDlfc1ffazb-wbT7XQ==/3286440257121624.jpg","companyId":0,"pic":3286440257121624,"picUrl":"http://p1.music.126.net/hvvqvDlfc1ffazb-wbT7XQ==/3286440257121624.jpg","publishTime":1388505600004,"description":"","tags":"","company":"","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":2,"copyrightId":0,"commentThreadId":"R_AL_3_3024001","artists":[{"name":"V.A.","id":21138,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":268000,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_29713638","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":64675830,"size":4300007,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":268000,"volumeDelta":-36100},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":64675829,"size":10749953,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":268000,"volumeDelta":-40000},"mMusic":{"name":null,"id":92013433,"size":6449989,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":268000,"volumeDelta":-37500},"lMusic":{"name":null,"id":64675830,"size":4300007,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":268000,"volumeDelta":-36100},"reason":"根据你可能喜欢的单曲 ISI","privilege":{"id":29713638,"fee":0,"payed":0,"st":0,"pl":320000,"dl":320000,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":999000,"toast":false,"flag":128,"preSell":false},"alg":"itembased"},{"name":"インドア系ならトラックメイカー","id":424262056,"position":1,"alias":["内向都是作曲家"],"status":0,"fee":0,"copyrightId":663018,"disc":"1","no":1,"artists":[{"name":"Yunomi","id":1025016,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},{"name":"nicamoq","id":12605718,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"インドア系ならトラックメイカー","id":34809022,"type":"EP/Single","size":1,"picId":3416182637666433,"blurPicUrl":"http://p1.music.126.net/F8QUlvMbIa5DG0Gu6wop_g==/3416182637666433.jpg","companyId":0,"pic":3416182637666433,"picUrl":"http://p1.music.126.net/F8QUlvMbIa5DG0Gu6wop_g==/3416182637666433.jpg","publishTime":1470153600007,"description":"","tags":"","company":"Self-Released","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":["内向都是作曲家"],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_34809022","artists":[{"name":"Yunomi","id":1025016,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":197407,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_424262056","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":0,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":1223882159,"size":3159397,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":197407,"volumeDelta":-26800},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":1223882157,"size":7898427,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":197407,"volumeDelta":-30300},"mMusic":{"name":null,"id":1223882158,"size":4739074,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":197407,"volumeDelta":-27800},"lMusic":{"name":null,"id":1223882159,"size":3159397,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":197407,"volumeDelta":-26800},"reason":"根据你可能喜欢的单曲 チカっとチカ千花っ♡ (TV Size)","privilege":{"id":424262056,"fee":0,"payed":0,"st":0,"pl":320000,"dl":320000,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":999000,"toast":false,"flag":128,"preSell":false},"alg":"itembased"},{"name":"All for you","id":33190533,"position":1,"alias":[],"status":0,"fee":0,"copyrightId":0,"disc":"1","no":1,"artists":[{"name":"Imagine Dragons","id":94779,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"Transformers: Age of Extinction","id":3189115,"type":"EP/Single","size":1,"picId":7946170535153116,"blurPicUrl":"http://p1.music.126.net/o10kCPmPrIFVpBYT3yjHBw==/7946170535153116.jpg","companyId":0,"pic":7946170535153116,"picUrl":"http://p1.music.126.net/o10kCPmPrIFVpBYT3yjHBw==/7946170535153116.jpg","publishTime":1262275200007,"description":"","tags":"","company":"Interscope","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":["电影《变形金刚4：绝迹重生》"],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_3189115","artists":[{"name":"Imagine Dragons","id":94779,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":95,"score":95,"starredNum":0,"duration":216227,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_33190533","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":0,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":1205963431,"size":2595570,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":216227,"volumeDelta":-2.65076E-4},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":1205963429,"size":8651798,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":216227,"volumeDelta":-2.65076E-4},"mMusic":{"name":null,"id":1205963430,"size":4325921,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":216227,"volumeDelta":-2.65076E-4},"lMusic":{"name":null,"id":1205963431,"size":2595570,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":216227,"volumeDelta":-2.65076E-4},"reason":"根据你可能喜欢的单曲 Gold (Jorgen Odegard Remix)","privilege":{"id":33190533,"fee":0,"payed":0,"st":0,"pl":999000,"dl":999000,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":999000,"toast":false,"flag":128,"preSell":false},"alg":"itembased"},{"name":"なごり雪","id":480751,"position":2,"alias":[],"status":0,"fee":0,"copyrightId":663018,"disc":"1","no":2,"artists":[{"name":"中西保志","id":14967,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"メロディーズ","id":46085,"type":"专辑","size":12,"picId":915893185937526,"blurPicUrl":"http://p1.music.126.net/XG7X81LBMZdDFsj3YQRrqw==/915893185937526.jpg","companyId":0,"pic":915893185937526,"picUrl":"http://p1.music.126.net/XG7X81LBMZdDFsj3YQRrqw==/915893185937526.jpg","publishTime":1257868800000,"description":"","tags":"","company":"徳间ジャパンコミュニケーションズ","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":1,"copyrightId":0,"commentThreadId":"R_AL_3_46085","artists":[{"name":"中西保志","id":14967,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0},"starred":false,"popularity":95,"score":95,"starredNum":0,"duration":237244,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_480751","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"transName":null,"sign":null,"mark":0,"bMusic":{"name":"なごり雪","id":35433282,"size":2876915,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":237244,"volumeDelta":-2.65076E-4},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":"なごり雪","id":35433283,"size":5714692,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":237244,"volumeDelta":-2.65076E-4},"mMusic":{"name":"なごり雪","id":35433281,"size":4775286,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":237244,"volumeDelta":-2.65076E-4},"lMusic":{"name":"なごり雪","id":35433282,"size":2876915,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":237244,"volumeDelta":-2.65076E-4},"reason":"根据你可能喜欢的单曲 碧い瞳のエリス","privilege":{"id":480751,"fee":0,"payed":0,"st":0,"pl":192000,"dl":192000,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":192000,"fl":999000,"toast":false,"flag":128,"preSell":false},"alg":"itembased"},{"name":"关键词","id":40147554,"position":4,"alias":[],"status":0,"fee":1,"copyrightId":7002,"disc":"1","no":4,"artists":[{"name":"林俊杰","id":3684,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"和自己对话 From M.E. To Myself","id":3438282,"type":"专辑","size":18,"picId":16561943649388272,"blurPicUrl":"http://p1.music.126.net/CKcTyKux_UTt0sO_5VWR9w==/16561943649388272.jpg","companyId":0,"pic":16561943649388272,"picUrl":"http://p1.music.126.net/CKcTyKux_UTt0sO_5VWR9w==/16561943649388272.jpg","publishTime":1450972800007,"description":"","tags":"","company":"华纳唱片","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":3,"copyrightId":7002,"commentThreadId":"R_AL_3_3438282","artists":[{"name":"林俊杰","id":3684,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0,"picId_str":"16561943649388272"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":212266,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_40147554","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":1197915808,"size":3397215,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":212266,"volumeDelta":-6400},"mp3Url":null,"rtype":0,"rurl":null,"mvid":5308076,"hMusic":{"name":null,"id":1197915806,"size":8492973,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":212266,"volumeDelta":-10800},"mMusic":{"name":null,"id":1197915807,"size":5095801,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":212266,"volumeDelta":-8100},"lMusic":{"name":null,"id":1197915808,"size":3397215,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":212266,"volumeDelta":-6400},"reason":"根据你喜欢的艺人","privilege":{"id":40147554,"fee":1,"payed":0,"st":0,"pl":0,"dl":0,"sp":0,"cp":0,"subp":0,"cs":false,"maxbr":999000,"fl":0,"toast":false,"flag":260,"preSell":false},"alg":"daily_ts_recall"},{"name":"不为谁而作的歌","id":37196629,"position":1,"alias":[],"status":0,"fee":1,"copyrightId":7002,"disc":"1","no":1,"artists":[{"name":"林俊杰","id":3684,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"album":{"name":"不为谁而作的歌","id":3397890,"type":"EP/Single","size":1,"picId":18362943695761230,"blurPicUrl":"http://p1.music.126.net/WDtmDgph6NKuKNbiwTDXHw==/18362943695761234.jpg","companyId":0,"pic":18362943695761230,"picUrl":"http://p1.music.126.net/WDtmDgph6NKuKNbiwTDXHw==/18362943695761234.jpg","publishTime":1449072000007,"description":"","tags":"","company":"华纳唱片","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":3,"copyrightId":7002,"commentThreadId":"R_AL_3_3397890","artists":[{"name":"林俊杰","id":3684,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0,"picId_str":"18362943695761234"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":265848,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_37196629","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"transName":null,"sign":null,"mark":0,"bMusic":{"name":null,"id":1185026346,"size":4254449,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":265848,"volumeDelta":-19100},"mp3Url":null,"rtype":0,"rurl":null,"mvid":0,"hMusic":{"name":null,"id":1185026344,"size":10636059,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":265848,"volumeDelta":-23300},"mMusic":{"name":null,"id":1185026345,"size":6381652,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":265848,"volumeDelta":-20700},"lMusic":{"name":null,"id":1185026346,"size":4254449,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":265848,"volumeDelta":-19100},"reason":"根据你喜欢的艺人","privilege":{"id":37196629,"fee":1,"payed":0,"st":0,"pl":0,"dl":0,"sp":0,"cp":0,"subp":0,"cs":false,"maxbr":320000,"fl":0,"toast":false,"flag":260,"preSell":false},"alg":"daily_ts_recall"}]
     */

    private int code;

    private RecommendData data;

    public RecommendData getData() {
        return data;
    }

    public void setData(RecommendData data) {
        this.data = data;
    }

    public DailyRecommendBean() {
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }


    public static class RecommendData {
        private List<SongDetailBean.SongsBean> dailySongs;
        private List<SongDetailBean.SongsBean> orderSongs;
        private List<RecommendReason> recommendReasons;

        public List<SongDetailBean.SongsBean> getDailySongs() {
            return dailySongs;
        }

        public void setDailySongs(List<SongDetailBean.SongsBean> dailySongs) {
            this.dailySongs = dailySongs;
        }

        public List<SongDetailBean.SongsBean> getOrderSongs() {
            return orderSongs;
        }

        public void setOrderSongs(List<SongDetailBean.SongsBean> orderSongs) {
            this.orderSongs = orderSongs;
        }

        public List<RecommendReason> getRecommendReasons() {
            return recommendReasons;
        }

        public void setRecommendReasons(List<RecommendReason> recommendReasons) {
            this.recommendReasons = recommendReasons;
        }
    }

    public static class RecommendReason {
        private long songId;
        private String reason;

        public long getSongId() {
            return songId;
        }

        public void setSongId(long songId) {
            this.songId = songId;
        }

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }
    }

    public static class RecommendBean {
        /**
         * name : 絕對是個夢
         * id : 32705315
         * position : 13
         * alias : []
         * status : 0
         * fee : 8
         * copyrightId : 7003
         * disc : 1
         * no : 13
         * artists : [{"name":"汤宝如","id":9504,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}]
         * album : {"name":"宝丽金真开心精选 Vol.2","id":3170209,"type":"专辑","size":13,"picId":7962663209006266,"blurPicUrl":"http://p2.music.126.net/GUnkmhDng1HQ6P3tYc1p3w==/7962663209006266.jpg","companyId":0,"pic":7962663209006266,"picUrl":"http://p2.music.126.net/GUnkmhDng1HQ6P3tYc1p3w==/7962663209006266.jpg","publishTime":717868800007,"description":"","tags":"","company":"宝丽金","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0},"songs":[],"alias":[],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_3170209","artists":[{"name":"群星","id":122455,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}],"subType":"录音室版","transName":null,"mark":0}
         * starred : false
         * popularity : 100
         * score : 100
         * starredNum : 0
         * duration : 224000
         * playedNum : 0
         * dayPlays : 0
         * hearTime : 0
         * ringtone :
         * crbt : null
         * audition : null
         * copyFrom :
         * commentThreadId : R_SO_4_32705315
         * rtUrl : null
         * ftype : 0
         * rtUrls : []
         * copyright : 0
         * transName : null
         * sign : null
         * mark : 0
         * bMusic : {"name":null,"id":95291317,"size":2695074,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":224000,"volumeDelta":-2.65076E-4}
         * mp3Url : null
         * rtype : 0
         * rurl : null
         * mvid : 0
         * hMusic : {"name":null,"id":95291315,"size":8983164,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":224000,"volumeDelta":-0.38}
         * mMusic : {"name":null,"id":95291316,"size":4491671,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":224000,"volumeDelta":-2.65076E-4}
         * lMusic : {"name":null,"id":95291317,"size":2695074,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":224000,"volumeDelta":-2.65076E-4}
         * reason : 根据你可能喜欢的单曲 电视风云1314
         * privilege : {"id":32705315,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":4,"preSell":false}
         * alg : itembased
         * transNames : ["Child"]
         */

        private String name;
        private long id;
        private int position;
        private int status;
        private int fee;
        private int copyrightId;
        private String disc;
        private int no;
        private AlbumBean album;
        private boolean starred;
        private int popularity;
        private int score;
        private int starredNum;
        private int duration;
        private int playedNum;
        private int dayPlays;
        private int hearTime;
        private String ringtone;
        private Object crbt;
        private Object audition;
        private String copyFrom;
        private String commentThreadId;
        private Object rtUrl;
        private int ftype;
        private int copyright;
        private Object transName;
        private Object sign;
        private int mark;
        private BMusicBean bMusic;
        private Object mp3Url;
        private int rtype;
        private Object rurl;
        private int mvid;
        private HMusicBean hMusic;
        private MMusicBean mMusic;
        private LMusicBean lMusic;
        private String reason;
        private PrivilegeBean privilege;
        private String alg;
        private List<?> alias;
        private List<ArtistsBeanX> artists;
        private List<?> rtUrls;
        private List<String> transNames;

        @Override
        public String toString() {
            return "RecommendBean{" +
                    "name='" + name + '\'' +
                    ", id=" + id +
                    ", position=" + position +
                    ", status=" + status +
                    ", fee=" + fee +
                    ", copyrightId=" + copyrightId +
                    ", disc='" + disc + '\'' +
                    ", no=" + no +
                    ", album=" + album +
                    ", starred=" + starred +
                    ", popularity=" + popularity +
                    ", score=" + score +
                    ", starredNum=" + starredNum +
                    ", duration=" + duration +
                    ", playedNum=" + playedNum +
                    ", dayPlays=" + dayPlays +
                    ", hearTime=" + hearTime +
                    ", ringtone='" + ringtone + '\'' +
                    ", crbt=" + crbt +
                    ", audition=" + audition +
                    ", copyFrom='" + copyFrom + '\'' +
                    ", commentThreadId='" + commentThreadId + '\'' +
                    ", rtUrl=" + rtUrl +
                    ", ftype=" + ftype +
                    ", copyright=" + copyright +
                    ", transName=" + transName +
                    ", sign=" + sign +
                    ", mark=" + mark +
                    ", bMusic=" + bMusic +
                    ", mp3Url=" + mp3Url +
                    ", rtype=" + rtype +
                    ", rurl=" + rurl +
                    ", mvid=" + mvid +
                    ", hMusic=" + hMusic +
                    ", mMusic=" + mMusic +
                    ", lMusic=" + lMusic +
                    ", reason='" + reason + '\'' +
                    ", privilege=" + privilege +
                    ", alg='" + alg + '\'' +
                    ", alias=" + alias +
                    ", artists=" + artists +
                    ", rtUrls=" + rtUrls +
                    ", transNames=" + transNames +
                    '}';
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public int getPosition() {
            return position;
        }

        public void setPosition(int position) {
            this.position = position;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getFee() {
            return fee;
        }

        public void setFee(int fee) {
            this.fee = fee;
        }

        public int getCopyrightId() {
            return copyrightId;
        }

        public void setCopyrightId(int copyrightId) {
            this.copyrightId = copyrightId;
        }

        public String getDisc() {
            return disc;
        }

        public void setDisc(String disc) {
            this.disc = disc;
        }

        public int getNo() {
            return no;
        }

        public void setNo(int no) {
            this.no = no;
        }

        public AlbumBean getAlbum() {
            return album;
        }

        public void setAlbum(AlbumBean album) {
            this.album = album;
        }

        public boolean isStarred() {
            return starred;
        }

        public void setStarred(boolean starred) {
            this.starred = starred;
        }

        public int getPopularity() {
            return popularity;
        }

        public void setPopularity(int popularity) {
            this.popularity = popularity;
        }

        public int getScore() {
            return score;
        }

        public void setScore(int score) {
            this.score = score;
        }

        public int getStarredNum() {
            return starredNum;
        }

        public void setStarredNum(int starredNum) {
            this.starredNum = starredNum;
        }

        public int getDuration() {
            return duration;
        }

        public void setDuration(int duration) {
            this.duration = duration;
        }

        public int getPlayedNum() {
            return playedNum;
        }

        public void setPlayedNum(int playedNum) {
            this.playedNum = playedNum;
        }

        public int getDayPlays() {
            return dayPlays;
        }

        public void setDayPlays(int dayPlays) {
            this.dayPlays = dayPlays;
        }

        public int getHearTime() {
            return hearTime;
        }

        public void setHearTime(int hearTime) {
            this.hearTime = hearTime;
        }

        public String getRingtone() {
            return ringtone;
        }

        public void setRingtone(String ringtone) {
            this.ringtone = ringtone;
        }

        public Object getCrbt() {
            return crbt;
        }

        public void setCrbt(Object crbt) {
            this.crbt = crbt;
        }

        public Object getAudition() {
            return audition;
        }

        public void setAudition(Object audition) {
            this.audition = audition;
        }

        public String getCopyFrom() {
            return copyFrom;
        }

        public void setCopyFrom(String copyFrom) {
            this.copyFrom = copyFrom;
        }

        public String getCommentThreadId() {
            return commentThreadId;
        }

        public void setCommentThreadId(String commentThreadId) {
            this.commentThreadId = commentThreadId;
        }

        public Object getRtUrl() {
            return rtUrl;
        }

        public void setRtUrl(Object rtUrl) {
            this.rtUrl = rtUrl;
        }

        public int getFtype() {
            return ftype;
        }

        public void setFtype(int ftype) {
            this.ftype = ftype;
        }

        public int getCopyright() {
            return copyright;
        }

        public void setCopyright(int copyright) {
            this.copyright = copyright;
        }

        public Object getTransName() {
            return transName;
        }

        public void setTransName(Object transName) {
            this.transName = transName;
        }

        public Object getSign() {
            return sign;
        }

        public void setSign(Object sign) {
            this.sign = sign;
        }

        public int getMark() {
            return mark;
        }

        public void setMark(int mark) {
            this.mark = mark;
        }

        public BMusicBean getBMusic() {
            return bMusic;
        }

        public void setBMusic(BMusicBean bMusic) {
            this.bMusic = bMusic;
        }

        public Object getMp3Url() {
            return mp3Url;
        }

        public void setMp3Url(Object mp3Url) {
            this.mp3Url = mp3Url;
        }

        public int getRtype() {
            return rtype;
        }

        public void setRtype(int rtype) {
            this.rtype = rtype;
        }

        public Object getRurl() {
            return rurl;
        }

        public void setRurl(Object rurl) {
            this.rurl = rurl;
        }

        public int getMvid() {
            return mvid;
        }

        public void setMvid(int mvid) {
            this.mvid = mvid;
        }

        public HMusicBean getHMusic() {
            return hMusic;
        }

        public void setHMusic(HMusicBean hMusic) {
            this.hMusic = hMusic;
        }

        public MMusicBean getMMusic() {
            return mMusic;
        }

        public void setMMusic(MMusicBean mMusic) {
            this.mMusic = mMusic;
        }

        public LMusicBean getLMusic() {
            return lMusic;
        }

        public void setLMusic(LMusicBean lMusic) {
            this.lMusic = lMusic;
        }

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }

        public PrivilegeBean getPrivilege() {
            return privilege;
        }

        public void setPrivilege(PrivilegeBean privilege) {
            this.privilege = privilege;
        }

        public String getAlg() {
            return alg;
        }

        public void setAlg(String alg) {
            this.alg = alg;
        }

        public List<?> getAlias() {
            return alias;
        }

        public void setAlias(List<?> alias) {
            this.alias = alias;
        }

        public List<ArtistsBeanX> getArtists() {
            return artists;
        }

        public void setArtists(List<ArtistsBeanX> artists) {
            this.artists = artists;
        }

        public List<?> getRtUrls() {
            return rtUrls;
        }

        public void setRtUrls(List<?> rtUrls) {
            this.rtUrls = rtUrls;
        }

        public List<String> getTransNames() {
            return transNames;
        }

        public void setTransNames(List<String> transNames) {
            this.transNames = transNames;
        }

        public static class AlbumBean {
            /**
             * name : 宝丽金真开心精选 Vol.2
             * id : 3170209
             * type : 专辑
             * size : 13
             * picId : 7962663209006266
             * blurPicUrl : http://p2.music.126.net/GUnkmhDng1HQ6P3tYc1p3w==/7962663209006266.jpg
             * companyId : 0
             * pic : 7962663209006266
             * picUrl : http://p2.music.126.net/GUnkmhDng1HQ6P3tYc1p3w==/7962663209006266.jpg
             * publishTime : 717868800007
             * description :
             * tags :
             * company : 宝丽金
             * briefDesc :
             * artist : {"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}
             * songs : []
             * alias : []
             * status : 0
             * copyrightId : 0
             * commentThreadId : R_AL_3_3170209
             * artists : [{"name":"群星","id":122455,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0}]
             * subType : 录音室版
             * transName : null
             * mark : 0
             */

            private String name;
            private long id;
            private String type;
            private int size;
            private long picId;
            private String blurPicUrl;
            private int companyId;
            private long pic;
            private String picUrl;
            private long publishTime;
            private String description;
            private String tags;
            private String company;
            private String briefDesc;
            private ArtistBean artist;
            private int status;
            private int copyrightId;
            private String commentThreadId;
            private String subType;
            private Object transName;
            private int mark;
            private List<?> songs;
            private List<?> alias;
            private List<ArtistsBean> artists;

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public long getId() {
                return id;
            }

            public void setId(long id) {
                this.id = id;
            }

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public int getSize() {
                return size;
            }

            public void setSize(int size) {
                this.size = size;
            }

            public long getPicId() {
                return picId;
            }

            public void setPicId(long picId) {
                this.picId = picId;
            }

            public String getBlurPicUrl() {
                return blurPicUrl;
            }

            public void setBlurPicUrl(String blurPicUrl) {
                this.blurPicUrl = blurPicUrl;
            }

            public int getCompanyId() {
                return companyId;
            }

            public void setCompanyId(int companyId) {
                this.companyId = companyId;
            }

            public long getPic() {
                return pic;
            }

            public void setPic(long pic) {
                this.pic = pic;
            }

            public String getPicUrl() {
                return picUrl;
            }

            public void setPicUrl(String picUrl) {
                this.picUrl = picUrl;
            }

            public long getPublishTime() {
                return publishTime;
            }

            public void setPublishTime(long publishTime) {
                this.publishTime = publishTime;
            }

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public String getTags() {
                return tags;
            }

            public void setTags(String tags) {
                this.tags = tags;
            }

            public String getCompany() {
                return company;
            }

            public void setCompany(String company) {
                this.company = company;
            }

            public String getBriefDesc() {
                return briefDesc;
            }

            public void setBriefDesc(String briefDesc) {
                this.briefDesc = briefDesc;
            }

            public ArtistBean getArtist() {
                return artist;
            }

            public void setArtist(ArtistBean artist) {
                this.artist = artist;
            }

            public int getStatus() {
                return status;
            }

            public void setStatus(int status) {
                this.status = status;
            }

            public int getCopyrightId() {
                return copyrightId;
            }

            public void setCopyrightId(int copyrightId) {
                this.copyrightId = copyrightId;
            }

            public String getCommentThreadId() {
                return commentThreadId;
            }

            public void setCommentThreadId(String commentThreadId) {
                this.commentThreadId = commentThreadId;
            }

            public String getSubType() {
                return subType;
            }

            public void setSubType(String subType) {
                this.subType = subType;
            }

            public Object getTransName() {
                return transName;
            }

            public void setTransName(Object transName) {
                this.transName = transName;
            }

            public int getMark() {
                return mark;
            }

            public void setMark(int mark) {
                this.mark = mark;
            }

            public List<?> getSongs() {
                return songs;
            }

            public void setSongs(List<?> songs) {
                this.songs = songs;
            }

            public List<?> getAlias() {
                return alias;
            }

            public void setAlias(List<?> alias) {
                this.alias = alias;
            }

            public List<ArtistsBean> getArtists() {
                return artists;
            }

            public void setArtists(List<ArtistsBean> artists) {
                this.artists = artists;
            }

            public static class ArtistBean {
                /**
                 * name :
                 * id : 0
                 * picId : 0
                 * img1v1Id : 0
                 * briefDesc :
                 * picUrl : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                 * img1v1Url : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                 * albumSize : 0
                 * alias : []
                 * trans :
                 * musicSize : 0
                 * topicPerson : 0
                 */

                private String name;
                private long id;
                private int picId;
                private String img1v1Id;
                private String briefDesc;
                private String picUrl;
                private String img1v1Url;
                private int albumSize;
                private String trans;
                private int musicSize;
                private int topicPerson;
                private List<?> alias;

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public long getId() {
                    return id;
                }

                public void setId(long id) {
                    this.id = id;
                }

                public int getPicId() {
                    return picId;
                }

                public void setPicId(int picId) {
                    this.picId = picId;
                }

                public String getImg1v1Id() {
                    return img1v1Id;
                }

                public void setImg1v1Id(String img1v1Id) {
                    this.img1v1Id = img1v1Id;
                }

                public String getBriefDesc() {
                    return briefDesc;
                }

                public void setBriefDesc(String briefDesc) {
                    this.briefDesc = briefDesc;
                }

                public String getPicUrl() {
                    return picUrl;
                }

                public void setPicUrl(String picUrl) {
                    this.picUrl = picUrl;
                }

                public String getImg1v1Url() {
                    return img1v1Url;
                }

                public void setImg1v1Url(String img1v1Url) {
                    this.img1v1Url = img1v1Url;
                }

                public int getAlbumSize() {
                    return albumSize;
                }

                public void setAlbumSize(int albumSize) {
                    this.albumSize = albumSize;
                }

                public String getTrans() {
                    return trans;
                }

                public void setTrans(String trans) {
                    this.trans = trans;
                }

                public int getMusicSize() {
                    return musicSize;
                }

                public void setMusicSize(int musicSize) {
                    this.musicSize = musicSize;
                }

                public int getTopicPerson() {
                    return topicPerson;
                }

                public void setTopicPerson(int topicPerson) {
                    this.topicPerson = topicPerson;
                }

                public List<?> getAlias() {
                    return alias;
                }

                public void setAlias(List<?> alias) {
                    this.alias = alias;
                }
            }

            public static class ArtistsBean {
                /**
                 * name : 群星
                 * id : 122455
                 * picId : 0
                 * img1v1Id : 0
                 * briefDesc :
                 * picUrl : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                 * img1v1Url : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                 * albumSize : 0
                 * alias : []
                 * trans :
                 * musicSize : 0
                 * topicPerson : 0
                 */

                private String name;
                private long id;
                private int picId;
                private String img1v1Id;
                private String briefDesc;
                private String picUrl;
                private String img1v1Url;
                private int albumSize;
                private String trans;
                private int musicSize;
                private int topicPerson;
                private List<?> alias;

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public long getId() {
                    return id;
                }

                public void setId(long id) {
                    this.id = id;
                }

                public int getPicId() {
                    return picId;
                }

                public void setPicId(int picId) {
                    this.picId = picId;
                }

                public String getImg1v1Id() {
                    return img1v1Id;
                }

                public void setImg1v1Id(String img1v1Id) {
                    this.img1v1Id = img1v1Id;
                }

                public String getBriefDesc() {
                    return briefDesc;
                }

                public void setBriefDesc(String briefDesc) {
                    this.briefDesc = briefDesc;
                }

                public String getPicUrl() {
                    return picUrl;
                }

                public void setPicUrl(String picUrl) {
                    this.picUrl = picUrl;
                }

                public String getImg1v1Url() {
                    return img1v1Url;
                }

                public void setImg1v1Url(String img1v1Url) {
                    this.img1v1Url = img1v1Url;
                }

                public int getAlbumSize() {
                    return albumSize;
                }

                public void setAlbumSize(int albumSize) {
                    this.albumSize = albumSize;
                }

                public String getTrans() {
                    return trans;
                }

                public void setTrans(String trans) {
                    this.trans = trans;
                }

                public int getMusicSize() {
                    return musicSize;
                }

                public void setMusicSize(int musicSize) {
                    this.musicSize = musicSize;
                }

                public int getTopicPerson() {
                    return topicPerson;
                }

                public void setTopicPerson(int topicPerson) {
                    this.topicPerson = topicPerson;
                }

                public List<?> getAlias() {
                    return alias;
                }

                public void setAlias(List<?> alias) {
                    this.alias = alias;
                }
            }
        }

        public static class BMusicBean {
            /**
             * name : null
             * id : 95291317
             * size : 2695074
             * extension : mp3
             * sr : 44100
             * dfsId : 0
             * bitrate : 96000
             * playTime : 224000
             * volumeDelta : -2.65076E-4
             */

            private Object name;
            private long id;
            private int size;
            private String extension;
            private int sr;
            private int dfsId;
            private int bitrate;
            private int playTime;
            private double volumeDelta;

            public Object getName() {
                return name;
            }

            public void setName(Object name) {
                this.name = name;
            }

            public long getId() {
                return id;
            }

            public void setId(long id) {
                this.id = id;
            }

            public int getSize() {
                return size;
            }

            public void setSize(int size) {
                this.size = size;
            }

            public String getExtension() {
                return extension;
            }

            public void setExtension(String extension) {
                this.extension = extension;
            }

            public int getSr() {
                return sr;
            }

            public void setSr(int sr) {
                this.sr = sr;
            }

            public int getDfsId() {
                return dfsId;
            }

            public void setDfsId(int dfsId) {
                this.dfsId = dfsId;
            }

            public int getBitrate() {
                return bitrate;
            }

            public void setBitrate(int bitrate) {
                this.bitrate = bitrate;
            }

            public int getPlayTime() {
                return playTime;
            }

            public void setPlayTime(int playTime) {
                this.playTime = playTime;
            }

            public double getVolumeDelta() {
                return volumeDelta;
            }

            public void setVolumeDelta(double volumeDelta) {
                this.volumeDelta = volumeDelta;
            }
        }

        public static class HMusicBean {
            /**
             * name : null
             * id : 95291315
             * size : 8983164
             * extension : mp3
             * sr : 44100
             * dfsId : 0
             * bitrate : 320000
             * playTime : 224000
             * volumeDelta : -0.38
             */

            private Object name;
            private long id;
            private int size;
            private String extension;
            private int sr;
            private int dfsId;
            private int bitrate;
            private int playTime;
            private double volumeDelta;

            public Object getName() {
                return name;
            }

            public void setName(Object name) {
                this.name = name;
            }

            public long getId() {
                return id;
            }

            public void setId(long id) {
                this.id = id;
            }

            public int getSize() {
                return size;
            }

            public void setSize(int size) {
                this.size = size;
            }

            public String getExtension() {
                return extension;
            }

            public void setExtension(String extension) {
                this.extension = extension;
            }

            public int getSr() {
                return sr;
            }

            public void setSr(int sr) {
                this.sr = sr;
            }

            public int getDfsId() {
                return dfsId;
            }

            public void setDfsId(int dfsId) {
                this.dfsId = dfsId;
            }

            public int getBitrate() {
                return bitrate;
            }

            public void setBitrate(int bitrate) {
                this.bitrate = bitrate;
            }

            public int getPlayTime() {
                return playTime;
            }

            public void setPlayTime(int playTime) {
                this.playTime = playTime;
            }

            public double getVolumeDelta() {
                return volumeDelta;
            }

            public void setVolumeDelta(double volumeDelta) {
                this.volumeDelta = volumeDelta;
            }
        }

        public static class MMusicBean {
            /**
             * name : null
             * id : 95291316
             * size : 4491671
             * extension : mp3
             * sr : 44100
             * dfsId : 0
             * bitrate : 160000
             * playTime : 224000
             * volumeDelta : -2.65076E-4
             */

            private Object name;
            private long id;
            private int size;
            private String extension;
            private int sr;
            private int dfsId;
            private int bitrate;
            private int playTime;
            private double volumeDelta;

            public Object getName() {
                return name;
            }

            public void setName(Object name) {
                this.name = name;
            }

            public long getId() {
                return id;
            }

            public void setId(long id) {
                this.id = id;
            }

            public int getSize() {
                return size;
            }

            public void setSize(int size) {
                this.size = size;
            }

            public String getExtension() {
                return extension;
            }

            public void setExtension(String extension) {
                this.extension = extension;
            }

            public int getSr() {
                return sr;
            }

            public void setSr(int sr) {
                this.sr = sr;
            }

            public int getDfsId() {
                return dfsId;
            }

            public void setDfsId(int dfsId) {
                this.dfsId = dfsId;
            }

            public int getBitrate() {
                return bitrate;
            }

            public void setBitrate(int bitrate) {
                this.bitrate = bitrate;
            }

            public int getPlayTime() {
                return playTime;
            }

            public void setPlayTime(int playTime) {
                this.playTime = playTime;
            }

            public double getVolumeDelta() {
                return volumeDelta;
            }

            public void setVolumeDelta(double volumeDelta) {
                this.volumeDelta = volumeDelta;
            }
        }

        public static class LMusicBean {
            /**
             * name : null
             * id : 95291317
             * size : 2695074
             * extension : mp3
             * sr : 44100
             * dfsId : 0
             * bitrate : 96000
             * playTime : 224000
             * volumeDelta : -2.65076E-4
             */

            private Object name;
            private long id;
            private int size;
            private String extension;
            private int sr;
            private int dfsId;
            private int bitrate;
            private int playTime;
            private double volumeDelta;

            public Object getName() {
                return name;
            }

            public void setName(Object name) {
                this.name = name;
            }

            public long getId() {
                return id;
            }

            public void setId(long id) {
                this.id = id;
            }

            public int getSize() {
                return size;
            }

            public void setSize(int size) {
                this.size = size;
            }

            public String getExtension() {
                return extension;
            }

            public void setExtension(String extension) {
                this.extension = extension;
            }

            public int getSr() {
                return sr;
            }

            public void setSr(int sr) {
                this.sr = sr;
            }

            public int getDfsId() {
                return dfsId;
            }

            public void setDfsId(int dfsId) {
                this.dfsId = dfsId;
            }

            public int getBitrate() {
                return bitrate;
            }

            public void setBitrate(int bitrate) {
                this.bitrate = bitrate;
            }

            public int getPlayTime() {
                return playTime;
            }

            public void setPlayTime(int playTime) {
                this.playTime = playTime;
            }

            public double getVolumeDelta() {
                return volumeDelta;
            }

            public void setVolumeDelta(double volumeDelta) {
                this.volumeDelta = volumeDelta;
            }
        }

        public static class PrivilegeBean {
            /**
             * id : 32705315
             * fee : 8
             * payed : 0
             * st : 0
             * pl : 128000
             * dl : 0
             * sp : 7
             * cp : 1
             * subp : 1
             * cs : false
             * maxbr : 999000
             * fl : 128000
             * toast : false
             * flag : 4
             * preSell : false
             */

            private int id;
            private int fee;
            private int payed;
            private int st;
            private int pl;
            private int dl;
            private int sp;
            private int cp;
            private int subp;
            private boolean cs;
            private int maxbr;
            private int fl;
            private boolean toast;
            private int flag;
            private boolean preSell;

            public int getId() {
                return id;
            }

            public void setId(int id) {
                this.id = id;
            }

            public int getFee() {
                return fee;
            }

            public void setFee(int fee) {
                this.fee = fee;
            }

            public int getPayed() {
                return payed;
            }

            public void setPayed(int payed) {
                this.payed = payed;
            }

            public int getSt() {
                return st;
            }

            public void setSt(int st) {
                this.st = st;
            }

            public int getPl() {
                return pl;
            }

            public void setPl(int pl) {
                this.pl = pl;
            }

            public int getDl() {
                return dl;
            }

            public void setDl(int dl) {
                this.dl = dl;
            }

            public int getSp() {
                return sp;
            }

            public void setSp(int sp) {
                this.sp = sp;
            }

            public int getCp() {
                return cp;
            }

            public void setCp(int cp) {
                this.cp = cp;
            }

            public int getSubp() {
                return subp;
            }

            public void setSubp(int subp) {
                this.subp = subp;
            }

            public boolean isCs() {
                return cs;
            }

            public void setCs(boolean cs) {
                this.cs = cs;
            }

            public int getMaxbr() {
                return maxbr;
            }

            public void setMaxbr(int maxbr) {
                this.maxbr = maxbr;
            }

            public int getFl() {
                return fl;
            }

            public void setFl(int fl) {
                this.fl = fl;
            }

            public boolean isToast() {
                return toast;
            }

            public void setToast(boolean toast) {
                this.toast = toast;
            }

            public int getFlag() {
                return flag;
            }

            public void setFlag(int flag) {
                this.flag = flag;
            }

            public boolean isPreSell() {
                return preSell;
            }

            public void setPreSell(boolean preSell) {
                this.preSell = preSell;
            }
        }

        public static class ArtistsBeanX {
            /**
             * name : 汤宝如
             * id : 9504
             * picId : 0
             * img1v1Id : 0
             * briefDesc :
             * picUrl : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
             * img1v1Url : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
             * albumSize : 0
             * alias : []
             * trans :
             * musicSize : 0
             * topicPerson : 0
             */

            private String name;
            private int id;
            private int picId;
            private String img1v1Id;
            private String briefDesc;
            private String picUrl;
            private String img1v1Url;
            private int albumSize;
            private String trans;
            private int musicSize;
            private int topicPerson;
            private List<?> alias;

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public int getId() {
                return id;
            }

            public void setId(int id) {
                this.id = id;
            }

            public int getPicId() {
                return picId;
            }

            public void setPicId(int picId) {
                this.picId = picId;
            }

            public String getImg1v1Id() {
                return img1v1Id;
            }

            public void setImg1v1Id(String img1v1Id) {
                this.img1v1Id = img1v1Id;
            }

            public String getBriefDesc() {
                return briefDesc;
            }

            public void setBriefDesc(String briefDesc) {
                this.briefDesc = briefDesc;
            }

            public String getPicUrl() {
                return picUrl;
            }

            public void setPicUrl(String picUrl) {
                this.picUrl = picUrl;
            }

            public String getImg1v1Url() {
                return img1v1Url;
            }

            public void setImg1v1Url(String img1v1Url) {
                this.img1v1Url = img1v1Url;
            }

            public int getAlbumSize() {
                return albumSize;
            }

            public void setAlbumSize(int albumSize) {
                this.albumSize = albumSize;
            }

            public String getTrans() {
                return trans;
            }

            public void setTrans(String trans) {
                this.trans = trans;
            }

            public int getMusicSize() {
                return musicSize;
            }

            public void setMusicSize(int musicSize) {
                this.musicSize = musicSize;
            }

            public int getTopicPerson() {
                return topicPerson;
            }

            public void setTopicPerson(int topicPerson) {
                this.topicPerson = topicPerson;
            }

            public List<?> getAlias() {
                return alias;
            }

            public void setAlias(List<?> alias) {
                this.alias = alias;
            }
        }

    }

}
