<?xml version="1.0" encoding="utf-8"?>
<android.support.design.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <android.support.design.widget.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <android.support.design.widget.CollapsingToolbarLayout
            android:layout_width="match_parent"
            android:layout_height="290dp"
            android:fitsSystemWindows="true"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <ImageView
                android:id="@+id/iv_user_detail_background"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/gray"
                app:layout_collapseMode="parallax" />

            <ImageView
                android:id="@+id/iv_user_detail_background_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                app:layout_collapseMode="parallax" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_collapseMode="parallax">

                <ImageView
                    android:id="@+id/iv_user_detail_avatar"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginTop="100dp" />

                <TextView
                    android:id="@+id/tv_user_detail_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/iv_user_detail_avatar"
                    android:layout_alignLeft="@+id/iv_user_detail_avatar"
                    android:layout_toEndOf="@+id/tv_day"
                    android:layout_marginTop="10dp"
                    android:textStyle="bold"
                    android:textColor="#f0f0f0"
                    android:textSize="17sp"
                    android:typeface="monospace" />

                <TextView
                    android:id="@+id/tv_user_detail_sub"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tv_user_detail_name"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:textSize="13sp"
                    android:textColor="#f0f0f0" />
                <ImageView
                    android:id="@+id/iv_user_detail_vip"
                    android:layout_width="30dp"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/tv_user_level"
                    android:layout_alignBottom="@+id/tv_user_level"
                    android:layout_below="@+id/tv_user_detail_sub"
                    android:layout_alignLeft="@+id/tv_user_detail_sub"
                    android:layout_marginLeft="5dp"
                    android:src="@drawable/ic_vip"/>
                <TextView
                    android:id="@+id/tv_user_level"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/iv_user_detail_vip"
                    android:layout_below="@+id/tv_user_detail_sub"
                    android:textStyle="bold|italic"
                    android:textColor="@color/white"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/bg_round_white"
                    android:text="Lv.8"
                    android:textSize="11sp"/>

                <LinearLayout
                    android:id="@+id/ll_user_detail_follow"
                    android:layout_width="70dp"
                    android:layout_height="30dp"
                    android:layout_alignBottom="@+id/iv_user_detail_vip"
                    android:layout_marginRight="20dp"
                    android:layout_toLeftOf="@+id/tv_user_detail_send_msg"
                    android:background="@drawable/bg_round_red"
                    android:visibility="invisible">

                    <ImageView
                        android:layout_width="13dp"
                        android:layout_height="13dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="5dp"
                        android:layout_marginRight="5dp"
                        android:src="@drawable/ic_add_white" />

                    <TextView
                        android:id="@+id/tv_item_search_user_follow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical|right"
                        android:layout_marginRight="5dp"
                        android:text="关注"
                        android:textColor="@color/white"
                        android:textSize="11sp" />
                </LinearLayout>
                <FrameLayout
                    android:id="@+id/fl_user_detail_followed"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_alignBottom="@+id/iv_user_detail_vip"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/bg_user_detail_followed"
                    android:layout_toLeftOf="@+id/tv_user_detail_send_msg"
                    android:visibility="invisible">
                    <ImageView
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_followed"/>
                </FrameLayout>

                <TextView
                    android:id="@+id/tv_user_detail_send_msg"
                    android:layout_width="80dp"
                    android:layout_height="30dp"
                    android:layout_alignTop="@+id/ll_user_detail_follow"
                    android:layout_marginRight="20dp"
                    android:textColor="@color/white"
                    android:textSize="11sp"
                    android:gravity="center"
                    android:background="@drawable/bg_round_white"
                    android:layout_alignParentRight="true"
                    android:text="发私信"/>
            </RelativeLayout>

            <android.support.v7.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="55dp"
                app:layout_collapseMode="pin"
                app:contentInsetLeft="0dp"
                app:contentInsetStart="0dp"
                app:contentInsetEnd="0dp"
                app:maxButtonHeight="20dp"
                app:titleMargin="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/img_user_detail_back"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="10dp"
                        android:layout_marginLeft="10dp"
                        android:src="@drawable/ic_left_arrow_white" />
                    <TextView
                        android:id="@+id/tv_left_title"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="15sp"
                        android:layout_toEndOf="@id/img_daily_back"
                        android:layout_gravity="center_vertical"
                        android:textColor="@color/white"
                        android:textSize="18sp"
                        android:typeface="monospace"
                        android:visibility="visible" />

                    <ImageView
                        android:id="@+id/img_tab_more"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginRight="10dp"
                        android:src="@drawable/ic_more_white" />
                </LinearLayout>
            </android.support.v7.widget.Toolbar>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="15dp"
                android:layout_marginTop="650dp"
                android:background="@drawable/bg_dailyrecommend"
                app:layout_collapseMode="pin" />
        </android.support.design.widget.CollapsingToolbarLayout>
    </android.support.design.widget.AppBarLayout>

    <RelativeLayout
        android:id="@+id/rl_user_detail_bottom"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#ffffff"
        android:paddingBottom="40dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:id="@+id/ll_user_magicindicator"
            android:layout_width="match_parent"
            android:layout_height="25dp"
            tools:ignore="RtlSymmetry"
            android:orientation="horizontal">
            <net.lucode.hackware.magicindicator.MagicIndicator
                android:id="@+id/magic_user_indicator"
                android:layout_width="match_parent"
                android:layout_gravity="center"
                android:layout_height="wrap_content"/>
        </LinearLayout>

        <TextView
            android:id="@+id/tv_gap"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#f0f0f0"
            android:layout_below="@id/ll_user_magicindicator" />

        <android.support.v4.view.ViewPager
            android:id="@+id/view_pager_user_detail"
            android:layout_below="@+id/tv_gap"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </RelativeLayout>

</android.support.design.widget.CoordinatorLayout>