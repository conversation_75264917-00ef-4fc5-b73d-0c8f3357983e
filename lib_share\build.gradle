apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.android.compileSdkVersion
    
    namespace 'com.imooc.lib_share'

    defaultConfig {
        minSdkVersion rootProject.android.minSdkVersion
        targetSdkVersion rootProject.android.targetSdkVersion
        versionCode rootProject.android.versionCode
        versionName rootProject.android.versionName
        multiDexEnabled rootProject.android.multiDexEnabled

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}

repositories {
    flatDir {
        dirs 'libs'
    }
}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    compileOnly this.rootProject.depsLibs.appcompat
    compileOnly project(':lib_common_ui')

}

