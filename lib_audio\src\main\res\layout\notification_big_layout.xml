<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="125dp">

    <ImageView
        android:id="@+id/image_view"
        android:layout_width="112dp"
        android:layout_height="match_parent"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/title_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="10dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_toEndOf="@id/image_view"
        android:textColor="#333333"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:text="四块五" />

    <ImageView
        android:id="@+id/close_view"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_alignParentRight="true"
        android:layout_marginTop="10dp"
        android:layout_marginRight="10dp"
        android:src="@drawable/ic_error_gray"/>
    <TextView
        android:id="@+id/tip_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/title_view"
        android:layout_alignStart="@id/title_view"
        android:layout_marginTop="5dp"
        android:textColor="#999999"
        android:textSize="12sp"
        tools:text="隔壁老王" />

    <ImageView
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@id/tip_view"
        android:layout_marginTop="8dp"
        android:layout_toEndOf="@id/image_view"
        android:background="#dddddd" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/divider"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:layout_toEndOf="@id/image_view"
        android:paddingStart="15dp"
        android:paddingRight="15dp">

        <ImageView
            android:id="@+id/favourite_view"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_centerVertical="true"
            android:scaleType="fitXY" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="词"
            android:textColor="#666666"
            android:textSize="18sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:gravity="center">

            <ImageView
                android:id="@+id/previous_view"
                android:layout_width="20dp"
                android:layout_height="20dp" />

            <ImageView
                android:id="@+id/play_view"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp" />

            <ImageView
                android:id="@+id/next_view"
                android:layout_width="20dp"
                android:layout_height="20dp" />
        </LinearLayout>
    </RelativeLayout>

</RelativeLayout>