<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_feed"
    android:layout_width="match_parent"
    android:layout_height="85dp"
    android:paddingTop="7dp"
    android:paddingBottom="7dp">

    <ImageView
        android:id="@+id/iv_item_video_cover"
        android:layout_width="130dp"
        android:layout_height="match_parent"
        android:layout_marginStart="12dp"
        android:scaleType="centerCrop" />

    <ImageView
        android:id="@+id/iv_play_num"
        android:layout_width="13dp"
        android:layout_height="13dp"
        android:layout_marginTop="3dp"
        android:layout_toLeftOf="@+id/tv_item_video_playnum"
        android:src="@drawable/ic_song_play_num" />

    <TextView
        android:id="@+id/tv_item_video_playnum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/iv_play_num"
        android:layout_alignRight="@+id/iv_item_video_cover"
        android:layout_alignBottom="@+id/iv_play_num"
        android:layout_marginRight="5dp"
        android:gravity="center"
        android:text="11万"
        android:textColor="@color/white"
        android:textSize="10sp" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_toEndOf="@id/iv_item_video_cover">


        <TextView
            android:id="@+id/tv_item_mv_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/tv_item_video_name"
            android:layout_marginRight="5dp"
            android:background="@drawable/bg_radio_sort"
            android:paddingLeft="5dp"
            android:paddingTop="3dp"
            android:paddingRight="5dp"
            android:paddingBottom="3dp"
            android:text="MV"
            android:textColor="@color/red"
            android:textSize="9sp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_item_video_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="30dp"
            android:layout_toEndOf="@id/tv_item_mv_type"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="xxxxxx\nxxx"
            android:textColor="#333333"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/tv_item_video_creator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_item_video_name"
            android:layout_marginTop="5dp"
            android:layout_marginRight="30dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:singleLine="true"
            android:textSize="11sp" />
    </RelativeLayout>
</RelativeLayout>