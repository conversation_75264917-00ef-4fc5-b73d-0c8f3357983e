<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <android.support.design.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <android.support.design.widget.AppBarLayout
            android:id="@+id/appbar_gedan_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/gray"
            app:elevation="0dp">

            <android.support.design.widget.CollapsingToolbarLayout
                android:id="@+id/collapsing_toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="310dp"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <ImageView
                    android:id="@+id/iv_gedan_detail_img_back"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    app:layout_collapseMode="parallax" />

                <ImageView
                    android:id="@+id/iv_gedan_detail_img_cover"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    app:layout_collapseMode="parallax" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:id="@+id/rl_gedan_detail_img"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/rl_toolbar"
                        android:layout_marginTop="?attr/actionBarSize"
                        android:paddingLeft="10dp"
                        android:paddingBottom="20dp">

                        <ImageView
                            android:id="@+id/iv_gedan_detail_album_tag"
                            android:layout_width="125dp"
                            android:layout_height="30dp"
                            android:layout_marginLeft="10dp"
                            android:src="@drawable/ic_album_attach_top"
                            android:visibility="gone" />

                        <ImageView
                            android:id="@+id/iv_gedan_detail_img"
                            android:layout_width="125dp"
                            android:layout_height="125dp"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="20dp" />

                        <ImageView
                            android:id="@+id/iv_gedan_detail_playnum"
                            android:layout_width="13dp"
                            android:layout_height="13dp"
                            android:layout_alignTop="@+id/iv_gedan_detail_img"
                            android:layout_alignBottom="@+id/tv_gedan_detail_playnum"
                            android:layout_marginTop="5dp"
                            android:layout_toLeftOf="@+id/tv_gedan_detail_playnum"
                            android:src="@drawable/ic_song_play_num"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/tv_gedan_detail_playnum"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@+id/iv_gedan_detail_img"
                            android:layout_alignRight="@+id/iv_gedan_detail_img"
                            android:layout_marginTop="5dp"
                            android:layout_marginRight="5dp"
                            android:textColor="@color/white"
                            android:textSize="10sp" />

                        <TextView
                            android:id="@+id/tv_gedan_detail_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_alignTop="@+id/iv_gedan_detail_img"
                            android:layout_marginLeft="20dp"
                            android:layout_marginRight="10dp"
                            android:layout_toRightOf="@+id/iv_gedan_detail_img"
                            android:maxLines="2"
                            android:text=""
                            android:textColor="@color/white"
                            android:textSize="17sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="25dp"
                            android:layout_below="@+id/tv_gedan_detail_title"
                            android:layout_alignLeft="@+id/tv_gedan_detail_title"
                            android:layout_marginTop="15dp">

                            <ImageView
                                android:id="@+id/iv_gedan_detail_avatar_img"
                                android:layout_width="25dp"
                                android:layout_height="25dp"
                                android:layout_marginRight="10dp" />

                            <TextView
                                android:id="@+id/tv_gedan_detail_avatar_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:text=""
                                android:textColor="@color/white"
                                android:textSize="12sp" />

                            <ImageView
                                android:layout_width="10dp"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_vertical"
                                android:src="@drawable/ic_right_arrow_white" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_gedan_detail_desc"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignLeft="@+id/tv_gedan_detail_title"
                            android:layout_alignBottom="@+id/iv_gedan_detail_img"
                            android:layout_marginRight="5dp"
                            android:ellipsize="end"
                            android:gravity="bottom"
                            android:maxLines="2"
                            android:text=""
                            android:textColor="@color/white"
                            android:textSize="11sp" />

                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:id="@+id/ll_gedan_detail_comment"
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="35dp"
                                android:layout_height="35dp"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_comment_white" />

                            <TextView
                                android:id="@+id/tv_gedan_detail_comment_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:textColor="@color/white" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="60dp"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="35dp"
                                android:layout_height="35dp"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_share_white" />

                            <TextView
                                android:id="@+id/tv_gedan_detail_share_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:textColor="@color/white" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="35dp"
                                android:layout_height="35dp"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_download_white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:text="下载"
                                android:textColor="@color/white" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="35dp"
                                android:layout_height="35dp"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_download_white" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:text="多选"
                                android:textColor="@color/white" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

                <android.support.v7.widget.Toolbar
                    android:id="@+id/toolbar_gedan_detail"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:layout_marginBottom="15dp"
                    app:contentInsetEnd="0dp"
                    app:contentInsetLeft="0dp"
                    app:contentInsetStart="0dp"
                    app:layout_collapseMode="pin"
                    app:maxButtonHeight="20dp"
                    app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
                    app:titleMargin="0dp">

                    <RelativeLayout
                        android:id="@+id/rl_toolbar_gedan_detail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:id="@+id/img_gedan_detail_back"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:layout_centerVertical="true"
                            android:layout_marginTop="10dp"
                            android:src="@drawable/ic_left_arrow_white1" />

                        <ImageView
                            android:id="@+id/iv_gedan_detail_search"
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:layout_marginTop="12dp"
                            android:layout_marginRight="10dp"
                            android:layout_toLeftOf="@+id/iv_gedan_detail_more"
                            android:src="@drawable/ic_search_white" />

                        <ImageView
                            android:id="@+id/iv_gedan_detail_more"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:layout_alignParentRight="true"
                            android:layout_marginTop="10dp"
                            android:layout_marginRight="10dp"
                            android:src="@drawable/ic_more_white" />

                        <TextView
                            android:id="@+id/tv_gedan_detail_toolbar_title"
                            android:layout_width="200dp"
                            android:layout_height="30dp"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="10dp"
                            android:layout_toRightOf="@+id/img_gedan_detail_back"
                            android:gravity="center_vertical"
                            android:text="歌单"
                            android:textColor="@color/white"
                            android:textSize="17sp" />

                        <TextView
                            android:id="@+id/tv_gedan_detail_toolbar_reason"
                            android:layout_width="wrap_content"
                            android:layout_height="15dp"
                            android:layout_below="@+id/tv_gedan_detail_toolbar_title"
                            android:layout_alignLeft="@+id/tv_gedan_detail_toolbar_title"
                            android:gravity="top"
                            android:textColor="@color/white"
                            android:textSize="8sp"
                            android:visibility="gone" />
                    </RelativeLayout>
                </android.support.v7.widget.Toolbar>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="15dp"
                    android:layout_marginTop="620dp"
                    android:background="@drawable/bg_dailyrecommend"
                    app:layout_collapseMode="pin" />
            </android.support.design.widget.CollapsingToolbarLayout>

        </android.support.design.widget.AppBarLayout>

        <RelativeLayout
            android:id="@+id/rl_play_header"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:id="@+id/ll_play_header"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_gravity="top"
                android:background="@color/white"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="23dp"
                    android:layout_height="23dp"
                    android:layout_gravity="top"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="5dp"
                    android:src="@drawable/ic_play_gray" />

                <TextView
                    android:id="@+id/tv_gedan_detaill_all_play"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="5dp"
                    android:text="全部播放"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_gedan_detail_song_num"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:layout_weight="1" />

                <LinearLayout
                    android:id="@+id/ll_gedan_subscribe"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:background="@drawable/bg_round_red"
                    android:orientation="horizontal"
                    android:visibility="visible">

                    <ImageView
                        android:id="@+id/iv_gedan_detail_collect_status"
                        android:layout_width="10dp"
                        android:layout_height="10dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_add_white" />

                    <TextView
                        android:id="@+id/tv_gedan_detail_collect_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:layout_marginRight="5dp"
                        android:text="收藏(1222)"
                        android:textColor="@color/white"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_gedan_subscribed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_collected" />

                    <TextView
                        android:id="@+id/tv_gedan_detail_collect_count1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:text="1222" />
                </LinearLayout>

            </LinearLayout>

            <FrameLayout
                android:id="@+id/loadframe"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/ll_play_header" />

        </RelativeLayout>
    </android.support.design.widget.CoordinatorLayout>
</LinearLayout>