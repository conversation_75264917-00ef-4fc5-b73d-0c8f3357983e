package com.imooc.lib_api.model.mv;

import com.chad.library.adapter.base.entity.SectionEntity;

public class MvMoreEntity implements SectionEntity {

    private boolean isHeader;
    private String header;
    private MvBean.MvDetailBean data;

	public MvMoreEntity(MvBean.MvDetailBean mvDetailBean) {
		this.isHeader = false;
		this.data = mvDetailBean;
	}

	public MvMoreEntity(boolean isHeader, String header) {
		this.isHeader = isHeader;
		this.header = header;
	}

	@Override
	public boolean isHeader() {
		return isHeader;
	}

	public String getHeader() {
		return header;
	}

	public MvBean.MvDetailBean getData() {
		return data;
	}
}
