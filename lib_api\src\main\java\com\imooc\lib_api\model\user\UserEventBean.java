package com.imooc.lib_api.model.user;

import java.util.List;

/**
 * 通过api获取的用户动态Bean
 */
public class UserEventBean {

    /**
     * lasttime : *************
     * more : true
     * size : 361
     * events : [{"actName":null,"forwardCount":3,"lotteryEventData":null,"user":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/P_GOWHWAqFIjN6oOqYsdbw==/109951164225662722.jpg","accountStatus":0,"gender":1,"city":1005500,"birthday":************,"userId":*********,"userType":0,"nickname":"璀璨的幻夢星光","signature":"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]","description":"","detailDescription":"","avatarImgId":109951164225662720,"backgroundImgId":109951163528584620,"backgroundUrl":"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"109951163528584619","avatarImgIdStr":"109951164225662722","urlAnalyze":false,"avatarImgId_str":"109951164225662722","followeds":5226},"eventTime":*************,"json":"{\"msg\":\"继续接上一条动态：\\n7月10日除了是奥特曼之日外，同时也是《银河奥特曼》的首播日。是继梦比优斯7年之后首部以TV形式正式登场的奥特英雄。\\n故事讲述了一群年轻人追寻着梦想与奥特英雄们相偶一起冒险的热血故事。\\n从此刻起奥特曼系列再次复活！他们又回归在人们的眼前，也从此开启了新世代奥特曼的第1页\",\"song\":{\"name\":\"Legend of Galaxy ~銀河の覇者\",\"id\":27630046,\"position\":4,\"alias\":[\"特摄《银河奥特曼特别剧场版》片头曲 \",\" 特撮「ウルトラマンギンガ 劇場スペシャル」OPテーマ\"],\"status\":0,\"fee\":0,\"copyrightId\":663018,\"disc\":\"1\",\"no\":4,\"artists\":[{\"name\":\"高見沢俊彦\",\"id\":15653,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"album\":{\"name\":\"雷神 \",\"id\":2646122,\"type\":\"专辑\",\"size\":11,\"picId\":1984618488161533,\"blurPicUrl\":\"http://p2.music.126.net/JD5bmx2AUOPga7qSIx-sUQ==/1984618488161533.jpg\",\"companyId\":0,\"pic\":1984618488161533,\"picUrl\":\"http://p2.music.126.net/JD5bmx2AUOPga7qSIx-sUQ==/1984618488161533.jpg\",\"publishTime\":1375200000007,\"description\":\"\",\"tags\":\"\",\"company\":\"EMI Records Japan\",\"briefDesc\":\"\",\"artist\":{\"name\":\"\",\"id\":0,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0},\"songs\":[],\"alias\":[],\"status\":0,\"copyrightId\":0,\"commentThreadId\":\"R_AL_3_2646122\",\"artists\":[{\"name\":\"高見沢俊彦\",\"id\":15653,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"img80x80\":\"https://p1.music.126.net/JD5bmx2AUOPga7qSIx-sUQ==/1984618488161533.jpg?param=80x80x1\"},\"starred\":false,\"popularity\":85.0,\"score\":85,\"starredNum\":0,\"duration\":354000,\"playedNum\":0,\"dayPlays\":0,\"hearTime\":0,\"ringtone\":\"\",\"crbt\":null,\"audition\":null,\"copyFrom\":\"\",\"commentThreadId\":\"R_SO_4_27630046\",\"rtUrl\":null,\"ftype\":0,\"rtUrls\":[],\"copyright\":2,\"rtype\":0,\"rurl\":null,\"mvid\":0,\"bMusic\":{\"name\":null,\"id\":65141836,\"size\":5668740,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":128000,\"playTime\":354000,\"volumeDelta\":-43300.0},\"mp3Url\":null,\"hMusic\":{\"name\":null,\"id\":65141835,\"size\":14171284,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":320000,\"playTime\":354000,\"volumeDelta\":-46400.0},\"mMusic\":{\"name\":null,\"id\":92109324,\"size\":8502921,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":192000,\"playTime\":354000,\"volumeDelta\":-44000.0},\"lMusic\":{\"name\":null,\"id\":65141836,\"size\":5668740,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":128000,\"playTime\":354000,\"volumeDelta\":-43300.0}}}","expireTime":0,"uuid":null,"rcmdInfo":null,"actId":0,"pics":[{"width":560,"height":792,"originUrl":"https://p1.music.126.net/JidENtRyj8PdSK3kYn8shQ==/109951164205011321.jpg","squareUrl":"https://p1.music.126.net/qu3jn-FjIQ4RlPey7b0kSw==/109951164205013214.jpg","rectangleUrl":"https://p1.music.126.net/tPv7c0rcNov9epWEqlZJDA==/109951164205016150.jpg","pcSquareUrl":"https://p1.music.126.net/SsjDOgM_kCcbzBqDBTo28Q==/109951164205012730.jpg","pcRectangleUrl":"https://p1.music.126.net/faNh4O7vHSxKM3alojw7VA==/109951164205017123.jpg","format":"jpg"},{"width":560,"height":792,"originUrl":"https://p1.music.126.net/yEddOoZm9TFIoYLBHP5Ihw==/109951164205016662.jpg","squareUrl":"https://p1.music.126.net/pMHA9A_BKrTYBOVQ1GVDbQ==/109951164205012240.jpg","rectangleUrl":"https://p1.music.126.net/iv0e_2UIl1DC7Ii5Q5ovsg==/109951164205014172.jpg","pcSquareUrl":"https://p1.music.126.net/vefi2m_MrxKr4Ib9asRUVg==/109951164205007494.jpg","pcRectangleUrl":"https://p1.music.126.net/Y_pXaPqsPgcZdgLiQu9XfA==/109951164205007495.jpg","format":"jpg"},{"width":560,"height":793,"originUrl":"https://p1.music.126.net/OTs8ascSl6KMR1XCbwQ2vg==/109951164205016153.jpg","squareUrl":"https://p1.music.126.net/J8J4YO82bsabUihKizmFmA==/109951164205007996.jpg","rectangleUrl":"https://p1.music.126.net/W042Z33HUwlp98MqV1R0Fw==/109951164205014173.jpg","pcSquareUrl":"https://p1.music.126.net/cJAzJu1kX2rj71c9l1l3lA==/109951164205009419.jpg","pcRectangleUrl":"https://p1.music.126.net/RTwj1_gjU6qdb4Vs7JC_vw==/109951164205008973.jpg","format":"jpg"},{"width":798,"height":635,"originUrl":"https://p1.music.126.net/vS5xKiI2ocauyMT_2ulMrQ==/109951164205010785.jpg","squareUrl":"https://p1.music.126.net/2hLSOWfB5eDJ4mj28pCtrw==/109951164205014731.jpg","rectangleUrl":"https://p1.music.126.net/WBgdnR7yhh4FkNrz2koTtQ==/109951164205017631.jpg","pcSquareUrl":"https://p1.music.126.net/0K11NdI7vFNsHYeHh-qv3w==/109951164205008976.jpg","pcRectangleUrl":"https://p1.music.126.net/DvPq6w6OMaYl_xiM-cAr1Q==/109951164205010370.jpg","format":"jpg"},{"width":960,"height":640,"originUrl":"https://p1.music.126.net/4vWh8WjCU8Hc_C6w3URZcg==/109951164205019545.jpg","squareUrl":"https://p1.music.126.net/9s-Q1yv3fltvVaMoMEiylw==/109951164205010371.jpg","rectangleUrl":"https://p1.music.126.net/jaMiYmgiO9wD2kYmTJ4ANQ==/109951164205016666.jpg","pcSquareUrl":"https://p1.music.126.net/dzdHRV7nOdW7Gu3gv1d5Nw==/109951164205009916.jpg","pcRectangleUrl":"https://p1.music.126.net/jiaoXPW6bt2NgLr5hR2m0w==/109951164205017634.jpg","format":"jpg"},{"width":436,"height":306,"originUrl":"https://p1.music.126.net/tvJvZ7tNckDLN8E-ZsqMvA==/109951164205018111.jpg","squareUrl":"https://p1.music.126.net/wMX2JzxgYykkMUL0-fjI3g==/109951164205012739.jpg","rectangleUrl":"https://p1.music.126.net/zJuct5f8QbHlXCkb6aOqPA==/109951164205018584.jpg","pcSquareUrl":"https://p1.music.126.net/N61OLlsSXiOQF64dmTn10w==/109951164205017636.jpg","pcRectangleUrl":"https://p1.music.126.net/h2DRtUjDZco0btmu5uz56Q==/109951164205010373.jpg","format":"jpg"},{"width":649,"height":376,"originUrl":"https://p1.music.126.net/7lPvvfhWqJzY7mXQULrY6w==/109951164205016161.jpg","squareUrl":"https://p1.music.126.net/bGggc2sghj2kCxBm0N4fnQ==/109951164205016669.jpg","rectangleUrl":"https://p1.music.126.net/NMydE0akVC4318HBz_EAuQ==/109951164205008462.jpg","pcSquareUrl":"https://p1.music.126.net/qrh4da4_vJS-tM3kugk6UQ==/109951164205020541.jpg","pcRectangleUrl":"https://p1.music.126.net/kYjdw_YNCp6wQYrJA3Igbg==/109951164205008463.jpg","format":"jpg"},{"width":900,"height":1200,"originUrl":"https://p1.music.126.net/qDvh-uALAoTkVx8-lujNSA==/109951164205013227.jpg","squareUrl":"https://p1.music.126.net/oXKAjOV9wkRfRD_AjFtOfA==/109951164205015189.jpg","rectangleUrl":"https://p1.music.126.net/62wO_US0VV36VU78VQsIqQ==/109951164205012741.jpg","pcSquareUrl":"https://p1.music.126.net/7wRuM5r5HNqzU3uZNS25qQ==/109951164205016164.jpg","pcRectangleUrl":"https://p1.music.126.net/xnyHdqU5a6OLAQZsh_9YgA==/109951164205010378.jpg","format":"jpg"},{"width":1920,"height":1079,"originUrl":"https://p1.music.126.net/CbIsJmSvxDPhnvZXtlyJYg==/109951164205014192.jpg","squareUrl":"https://p1.music.126.net/A5inFiJmf4nhEj2tFPFjlA==/109951164205011774.jpg","rectangleUrl":"https://p1.music.126.net/Nux7CKRVK7XE_2mpwSG8aA==/109951164205010797.jpg","pcSquareUrl":"https://p1.music.126.net/nFT-iGGqLHFKQYrs7-NoeA==/109951164205011337.jpg","pcRectangleUrl":"https://p1.music.126.net/FmvCh3umZSjI9gqO6zEHjQ==/109951164205019554.jpg","format":"jpg"}],"showTime":*************,"tmplId":0,"id":**********,"type":18,"topEvent":false,"insiteForwardCount":3,"info":{"commentThread":{"id":"A_EV_2_**********_*********","resourceInfo":{"id":**********,"userId":*********,"name":"分享单曲：「Legend of Galaxy ~銀河の覇者 (特摄《银河奥特曼特别剧场版》片头曲 )」","imgUrl":null,"creator":null,"eventType":18},"resourceType":2,"commentCount":13,"likedCount":81,"shareCount":3,"hotCount":6,"latestLikedUsers":[{"s":125480200,"t":1563778997011},{"s":562373716,"t":1563699815075},{"s":136573487,"t":1563631631945},{"s":255876719,"t":1563595442198},{"s":253528058,"t":1563594284815},{"s":1316129489,"t":1563495689051},{"s":1628836123,"t":1563438018553},{"s":135427249,"t":1563416164439},{"s":1352058031,"t":1563359988652},{"s":1772445458,"t":1563357333159}],"resourceOwnerId":*********,"resourceTitle":"分享单曲：「Legend of Galaxy ~銀河の覇者 (特摄《银河奥特曼特别剧场版》片头曲 )」","resourceId":**********},"latestLikedUsers":null,"liked":false,"comments":null,"resourceType":2,"resourceId":**********,"shareCount":3,"commentCount":13,"likedCount":81,"threadId":"A_EV_2_**********_*********"}},{"actName":null,"forwardCount":17,"lotteryEventData":null,"user":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/P_GOWHWAqFIjN6oOqYsdbw==/109951164225662722.jpg","accountStatus":0,"gender":1,"city":1005500,"birthday":************,"userId":*********,"userType":0,"nickname":"璀璨的幻夢星光","signature":"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]","description":"","detailDescription":"","avatarImgId":109951164225662720,"backgroundImgId":109951163528584620,"backgroundUrl":"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"109951163528584619","avatarImgIdStr":"109951164225662722","urlAnalyze":false,"avatarImgId_str":"109951164225662722","followeds":5226},"eventTime":*************,"json":"{\"msg\":\"1966年7月10日是奥特曼之日。奥特曼也首次出现在了众人眼前，从这天起光之巨人打击侵略者的故事拉开了序幕。一位又一位的奥特英雄给予孩子们爱与梦想，教育着人们永不放弃的精神！\\n经历了半个多世纪，现在，依旧被传承着\u2026\u2026\",\"program\":{\"mainSong\":null,\"songs\":null,\"dj\":{\"defaultAvatar\":false,\"province\":1000000,\"authStatus\":0,\"followed\":false,\"avatarUrl\":\"http://p1.music.126.net/HQWN4fvgmRPazLWD1irkAg==/109951164168186484.jpg\",\"accountStatus\":0,\"gender\":1,\"city\":1005500,\"birthday\":************,\"userId\":*********,\"userType\":0,\"nickname\":\"璀璨的幻夢星光\",\"signature\":\"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]\",\"description\":\"\",\"detailDescription\":\"\",\"avatarImgId\":109951164168186484,\"backgroundImgId\":109951163528584619,\"backgroundUrl\":\"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg\",\"authority\":0,\"mutual\":false,\"expertTags\":null,\"experts\":null,\"djStatus\":10,\"vipType\":0,\"remarkName\":null,\"backgroundImgIdStr\":\"109951163528584619\",\"avatarImgIdStr\":\"109951164168186484\",\"avatarImgId_str\":\"109951164168186484\",\"brand\":\"円谷プロ&奥特曼周年纪念歌曲电台（ULTRAMAN）\"},\"blurCoverUrl\":\"http://music.163.com/api/dj/img/blur/****************\",\"radio\":{\"id\":*********,\"dj\":null,\"name\":\"円谷プロ&奥特曼周年纪念歌曲电台（ULTRAMAN）\",\"picUrl\":\"http://p1.music.126.net/6BDeGvl0LGfleVfTpMyUoA==/18784056651148822.jpg\",\"desc\":\"【影视资讯〗：《泰迦奥特曼》7月6日每周六10:00放送！\\n\\n电台简介：不用我说想必各位奥迷们都知道吧，7月7日是什么日子？没错！7月7日就是我们大家熟悉的摄影之神円谷英二导演的出生日期哦，他也是奥特曼的诞生之父。刚好本电台也是7月7日建成的，这也是为了纪念円谷他老人家吧\u2026\u2026\u2026\u2026在这里也祝奥特曼50周年诞生纪念日快乐！٩(ˊωˋ*)و✧\\n\u201c相信奥特曼还能再战50年！\u201d\\n在这里热衷感谢大家对本电台支持，以后也会为大家献出更多更好听的歌♪(^∇^*)\",\"subCount\":3998,\"programCount\":462,\"createTime\":1467842983344,\"categoryId\":4,\"category\":\"娱乐|影视\",\"radioFeeType\":0,\"feeScope\":0,\"buyed\":true,\"videos\":null,\"finished\":false,\"underShelf\":false,\"purchaseCount\":0,\"price\":0,\"originalPrice\":0,\"discountPrice\":null,\"lastProgramCreateTime\":1561703958390,\"lastProgramName\":null,\"lastProgramId\":2061892511,\"picId\":18784056651148822,\"rcmdText\":null,\"composeVideo\":false,\"subed\":true},\"subscribedCount\":0,\"reward\":false,\"mainTrackId\":424112020,\"serialNum\":51,\"listenerCount\":6502,\"name\":\"【伝說の巨人~ウルトラマン】\",\"id\":791879582,\"createTime\":1470196176848,\"description\":\"光の巨人~ウルトラマン！\",\"userId\":*********,\"coverUrl\":\"http://p1.music.126.net/7iaPPsJcYHz6FiQ-8aiWUg==/****************.jpg\",\"commentThreadId\":\"A_DJ_1_791879582\",\"channels\":[\"娱乐八卦\"],\"titbits\":null,\"titbitImages\":null,\"pubStatus\":2,\"trackCount\":0,\"bdAuditStatus\":2,\"programFeeType\":0,\"buyed\":false,\"programDesc\":null,\"h5Links\":[],\"coverId\":****************,\"adjustedPlayCount\":6502.0,\"canReward\":false,\"auditStatus\":10,\"duration\":0,\"publish\":true,\"img80x80\":\"https://p1.music.126.net/7iaPPsJcYHz6FiQ-8aiWUg==/****************.jpg?param=80x80x1\"}}","expireTime":0,"uuid":null,"rcmdInfo":null,"actId":0,"pics":[{"width":300,"height":225,"originUrl":"https://p1.music.126.net/W4NKvd6yudLekMQ60OPSkg==/109951164204884547.jpg","squareUrl":"https://p1.music.126.net/blAZJYpzVVmxUM8-AskZFA==/109951164204885024.jpg","rectangleUrl":"https://p1.music.126.net/XEUymWuxG0Bq_KgU8wGCvA==/109951164204877284.jpg","pcSquareUrl":"https://p1.music.126.net/8FK-wvSfPfHYZ5e-QvfTpg==/109951164204881734.jpg","pcRectangleUrl":"https://p1.music.126.net/ZZuVXWaZLvMBuj1k_UlbMw==/109951164204876815.jpg","format":"jpg"},{"width":308,"height":415,"originUrl":"https://p1.music.126.net/51yzOA6pKAn0bEa7tnu5Zw==/109951164204876816.jpg","squareUrl":"https://p1.music.126.net/09V1QdTrIyvdtQiWwQivYA==/109951164204883098.jpg","rectangleUrl":"https://p1.music.126.net/N8-Vgv14eZrZGHCtraGZtg==/109951164204885512.jpg","pcSquareUrl":"https://p1.music.126.net/4uPuuDyUuPRocCwAJk455A==/109951164204880261.jpg","pcRectangleUrl":"https://p1.music.126.net/di8FsY6zQjFnacZGduMTEA==/109951164204881735.jpg","format":"jpg"},{"width":576,"height":975,"originUrl":"https://p1.music.126.net/WH-KNBtVWqQGZw63H0Xw_w==/109951164204878248.jpg","squareUrl":"https://p1.music.126.net/kZeLlLQpFUX1RNfU8J8IXQ==/109951164204885515.jpg","rectangleUrl":"https://p1.music.126.net/SwmSoJMCVwQrMjm2ZZm0RQ==/109951164204873982.jpg","pcSquareUrl":"https://p1.music.126.net/CDd6Mej2gVfacp-dwTZISw==/109951164204872992.jpg","pcRectangleUrl":"https://p1.music.126.net/61Z7Jbock03zdLj4_AOWRg==/109951164204877290.jpg","format":"jpg"},{"width":590,"height":758,"originUrl":"https://p1.music.126.net/EMEg97Jk12F2BYoXpMGhlA==/109951164204877799.jpg","squareUrl":"https://p1.music.126.net/kDFbUXMSf_NAcB4j3H4Rlw==/109951164204884549.jpg","rectangleUrl":"https://p1.music.126.net/n5I108PauH5ri8hF7-mF-g==/109951164204877801.jpg","pcSquareUrl":"https://p1.music.126.net/ASjIC99NaQudvsuBcaX-1w==/109951164204874915.jpg","pcRectangleUrl":"https://p1.music.126.net/o0LsxLfjTgJH2omkRWLAqQ==/109951164204880730.jpg","format":"jpg"},{"width":690,"height":503,"originUrl":"https://p1.music.126.net/92eqzUiv4J-ygipHwytw_A==/109951164204873984.jpg","squareUrl":"https://p1.music.126.net/dHeSJKdfzNMkImOi0CaXsw==/109951164204879747.jpg","rectangleUrl":"https://p1.music.126.net/Nqyqio_MCCOL3ktwaynxvw==/109951164204877294.jpg","pcSquareUrl":"https://p1.music.126.net/-ILvi-m7D-oOeaBAd2rA4g==/109951164204875386.jpg","pcRectangleUrl":"https://p1.music.126.net/auHcAQeGyWqqiDRafhvQog==/109951164204879259.jpg","format":"jpg"},{"width":600,"height":567,"originUrl":"https://p1.music.126.net/_NEMrvC_k25o5VLkp3pMbA==/109951164204880736.jpg","squareUrl":"https://p1.music.126.net/8L3QyhiNDKRD78u_DXcnAw==/109951164204879260.jpg","rectangleUrl":"https://p1.music.126.net/Lo9CSmJJfnNkwFFY6FEVkA==/109951164204880739.jpg","pcSquareUrl":"https://p1.music.126.net/rrZWvgdyzhHwRXbxYoOchw==/109951164204875894.jpg","pcRectangleUrl":"https://p1.music.126.net/d14-tix7UyFnI6NGCun6bA==/109951164204873986.jpg","format":"jpg"},{"width":360,"height":5664,"originUrl":"https://p1.music.126.net/HZJD0nsDj1asi_uzRZwSFw==/109951164204880288.jpg","squareUrl":"https://p1.music.126.net/3zLgoAv_2x_0Su0BNJakjw==/109951164204874937.jpg","rectangleUrl":"https://p1.music.126.net/Z4bN17i9N_Z1OmrIV9Cr7g==/109951164204886522.jpg","pcSquareUrl":"https://p1.music.126.net/lnW1_LeFzjzilUzvmP7how==/109951164204882228.jpg","pcRectangleUrl":"https://p1.music.126.net/sOIVt9Y2FJlnNPH-tKFVdA==/109951164204884571.jpg","format":"jpg"},{"width":1080,"height":1215,"originUrl":"https://p1.music.126.net/A1QZtSxzDMrtluRlJqsBvg==/109951164204878793.jpg","squareUrl":"https://p1.music.126.net/UvWpvFDzdKi7vEhnURIW8w==/109951164204887017.jpg","rectangleUrl":"https://p1.music.126.net/6W7KEL6s1nk37IZDUA8BmA==/109951164204886034.jpg","pcSquareUrl":"https://p1.music.126.net/FDz4A8ldX3b6HXxIpNV-lw==/109951164204874943.jpg","pcRectangleUrl":"https://p1.music.126.net/G5a7n3Jx0MQ35kZ-nay9FA==/109951164204879286.jpg","format":"jpg"},{"width":640,"height":352,"originUrl":"https://p1.music.126.net/V0nOLhzszBiNlkUQrO4sRw==/109951164204881250.jpg","squareUrl":"https://p1.music.126.net/pKjEsdhRsDVGaGq0yB7iyg==/109951164204885059.jpg","rectangleUrl":"https://p1.music.126.net/1S9lcRBXbFnCtNyT_rQWqg==/109951164204881252.jpg","pcSquareUrl":"https://p1.music.126.net/w6QqdG51imWi93bbvnrCFA==/109951164204881768.jpg","pcRectangleUrl":"https://p1.music.126.net/R5bz6xWjKoeSpK5ozd-Rcw==/109951164204876360.jpg","format":"jpg"}],"showTime":*************,"tmplId":0,"id":**********,"type":17,"topEvent":false,"insiteForwardCount":13,"info":{"commentThread":{"id":"A_EV_2_**********_*********","resourceInfo":{"id":**********,"userId":*********,"name":"分享DJ节目：「【伝說の巨人~ウルトラマン】」","imgUrl":null,"creator":null,"eventType":17},"resourceType":2,"commentCount":20,"likedCount":106,"shareCount":17,"hotCount":9,"latestLikedUsers":[{"s":472428539,"t":1564041160402},{"s":125480200,"t":1563779002531},{"s":423771189,"t":1563760239662},{"s":562373716,"t":1563699903281},{"s":136573487,"t":1563631657657},{"s":253528058,"t":1563594291624},{"s":1316129489,"t":1563495691401},{"s":1628836123,"t":1563438020517},{"s":135427249,"t":1563416166917},{"s":253390303,"t":1563374859996}],"resourceOwnerId":*********,"resourceTitle":"分享DJ节目：「【伝說の巨人~ウルトラマン】」","resourceId":**********},"latestLikedUsers":null,"liked":false,"comments":null,"resourceType":2,"resourceId":**********,"shareCount":17,"commentCount":20,"likedCount":106,"threadId":"A_EV_2_**********_*********"}},{"actName":null,"forwardCount":5,"lotteryEventData":null,"user":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/P_GOWHWAqFIjN6oOqYsdbw==/109951164225662722.jpg","accountStatus":0,"gender":1,"city":1005500,"birthday":************,"userId":*********,"userType":0,"nickname":"璀璨的幻夢星光","signature":"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]","description":"","detailDescription":"","avatarImgId":109951164225662720,"backgroundImgId":109951163528584620,"backgroundUrl":"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"109951163528584619","avatarImgIdStr":"109951164225662722","urlAnalyze":false,"avatarImgId_str":"109951164225662722","followeds":5226},"eventTime":*************,"json":"{\"msg\":\"接上一条动态：\\n7月7日也是《ウルトラマン幻夢ノフィー》的开播日期，所以呢，还是继续放预告图[多多捂脸]（正片什么的不可能存在的，自行脑补吧）\\n电台也迎来三周年了，由于有点赶今年就不搞节目了[多多比耶]\\n7月7日真是不可思议的一天呢（笑）[星星]\",\"djRadio\":{\"id\":*********,\"dj\":{\"defaultAvatar\":false,\"province\":1000000,\"authStatus\":0,\"followed\":false,\"avatarUrl\":\"http://p1.music.126.net/HQWN4fvgmRPazLWD1irkAg==/109951164168186484.jpg\",\"accountStatus\":0,\"gender\":1,\"city\":1005500,\"birthday\":************,\"userId\":*********,\"userType\":0,\"nickname\":\"璀璨的幻夢星光\",\"signature\":\"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]\",\"description\":\"\",\"detailDescription\":\"\",\"avatarImgId\":109951164168186484,\"backgroundImgId\":109951163528584619,\"backgroundUrl\":\"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg\",\"authority\":0,\"mutual\":false,\"expertTags\":null,\"experts\":null,\"djStatus\":10,\"vipType\":0,\"remarkName\":null,\"backgroundImgIdStr\":\"109951163528584619\",\"avatarImgIdStr\":\"109951164168186484\",\"avatarImgId_str\":\"109951164168186484\"},\"name\":\"円谷プロ&奥特曼周年纪念歌曲电台（ULTRAMAN）\",\"picUrl\":\"http://p2.music.126.net/lhozD4T3SKUcTeB-shC9UQ==/*****************.jpg\",\"desc\":\"【影视资讯〗：《泰迦奥特曼》7月6日每周六10:00放送！\\n\\n电台简介：不用我说想必各位奥迷们都知道吧，7月7日是什么日子？没错！7月7日就是我们大家熟悉的摄影之神円谷英二导演的出生日期哦，他也是奥特曼的诞生之父。刚好本电台也是7月7日建成的，这也是为了纪念円谷他老人家吧\u2026\u2026\u2026\u2026在这里也祝奥特曼50周年诞生纪念日快乐！٩(ˊωˋ*)و✧\\n\u201c相信奥特曼还能再战50年！\u201d\\n在这里热衷感谢大家对本电台支持，以后也会为大家献出更多更好听的歌♪(^∇^*)\",\"subCount\":3956,\"programCount\":462,\"createTime\":1467842983344,\"categoryId\":4,\"category\":\"娱乐|影视\",\"radioFeeType\":0,\"feeScope\":0,\"buyed\":false,\"videos\":null,\"finished\":false,\"underShelf\":false,\"purchaseCount\":0,\"price\":0,\"originalPrice\":0,\"discountPrice\":null,\"lastProgramCreateTime\":1561703958390,\"lastProgramName\":null,\"lastProgramId\":2061892511,\"picId\":*****************,\"rcmdText\":null,\"composeVideo\":false,\"img80x80\":\"https://p2.music.126.net/lhozD4T3SKUcTeB-shC9UQ==/*****************.jpg?param=80x80x1\"}}","expireTime":0,"uuid":null,"rcmdInfo":null,"actId":0,"pics":[{"width":1557,"height":1080,"originUrl":"https://p1.music.126.net/LP1ucKLU4yOex5etNh0kjg==/109951164198740714.jpg","squareUrl":"https://p1.music.126.net/KA2hcTdfT3IQBPi-Y7MZBg==/109951164198740279.jpg","rectangleUrl":"https://p1.music.126.net/nkRkDehl2jnoQoq7doL1SA==/109951164198743623.jpg","pcSquareUrl":"https://p1.music.126.net/7q5ROOdBIpsallO-EKsRMg==/109951164198747103.jpg","pcRectangleUrl":"https://p1.music.126.net/LFnsIIr2HOW4yjt7eD8OYg==/109951164198746129.jpg","format":"jpg"},{"width":1080,"height":1080,"originUrl":"https://p1.music.126.net/yXTgp5Km2wjTgPnJAEsvJQ==/109951164198744141.jpg","squareUrl":"https://p1.music.126.net/vwHWD-XYzDZSkJTcZyt5kA==/109951164198742678.jpg","rectangleUrl":"https://p1.music.126.net/Chvh2SwlNxmLzQ_x4wtMxg==/109951164198734977.jpg","pcSquareUrl":"https://p1.music.126.net/fy_eLsFZyVP9Cxmr83UdUA==/109951164198740721.jpg","pcRectangleUrl":"https://p1.music.126.net/eNntHkTvE5mDCVdcPzh_hQ==/109951164198745130.jpg","format":"jpg"}],"showTime":*************,"tmplId":0,"id":**********,"type":28,"topEvent":false,"insiteForwardCount":4,"info":{"commentThread":{"id":"A_EV_2_**********_*********","resourceInfo":{"id":**********,"userId":*********,"name":"分享电台：「円谷プロ&奥特曼周年纪念歌曲电台（ULTRAMAN）」","imgUrl":null,"creator":null,"eventType":28},"resourceType":2,"commentCount":32,"likedCount":77,"shareCount":5,"hotCount":15,"latestLikedUsers":[{"s":125480200,"t":1563779005021},{"s":1373931927,"t":1563674202713},{"s":136573487,"t":1563631655087},{"s":393969138,"t":1563546995304},{"s":1316129489,"t":1563495692451},{"s":1438036746,"t":1563281887395},{"s":410116068,"t":1563262507097},{"s":320697573,"t":1563077603818},{"s":103366184,"t":1563074677824},{"s":590859641,"t":1563062504267}],"resourceOwnerId":*********,"resourceTitle":"分享电台：「円谷プロ&奥特曼周年纪念歌曲电台（ULTRAMAN）」","resourceId":**********},"latestLikedUsers":null,"liked":false,"comments":null,"resourceType":2,"resourceId":**********,"shareCount":5,"commentCount":32,"likedCount":77,"threadId":"A_EV_2_**********_*********"}},{"actName":null,"forwardCount":18,"lotteryEventData":null,"user":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/P_GOWHWAqFIjN6oOqYsdbw==/109951164225662722.jpg","accountStatus":0,"gender":1,"city":1005500,"birthday":************,"userId":*********,"userType":0,"nickname":"璀璨的幻夢星光","signature":"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]","description":"","detailDescription":"","avatarImgId":109951164225662720,"backgroundImgId":109951163528584620,"backgroundUrl":"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"109951163528584619","avatarImgIdStr":"109951164225662722","urlAnalyze":false,"avatarImgId_str":"109951164225662722","followeds":5226},"eventTime":*************,"json":"{\"msg\":\"118年前7月7日的今日他出生于日本福岛须贺川，取名为\u201c円谷英一\u201d（原名），也就是我们现在熟知的円谷英二。\\n没错，今天是円谷老爷子诞生118年纪念日哦~\\n今天除了是老爷子的诞生纪念外，7月7日也是《高斯奥特曼》&《罗布奥特曼》的首播日期，同时也是北斗与南夕子的生日~\\n祝今天的寿星生日快乐！[蛋糕]\",\"song\":{\"name\":\"円谷英二の肖像 (ウルトラQのおやじ)\",\"id\":547969518,\"position\":9,\"alias\":[],\"status\":0,\"fee\":0,\"copyrightId\":0,\"disc\":\"1\",\"no\":9,\"artists\":[{\"name\":\"冬木透\",\"id\":1087211,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"album\":{\"name\":\"円谷英二生誕100周年記念CD-BOX - Music from the works of EIJI TSUBURAYA -\",\"id\":38045121,\"type\":\"合集\",\"size\":133,\"picId\":109951163212833929,\"blurPicUrl\":\"http://p1.music.126.net/a2Q5DTqCWfDY0M_zzHhsYQ==/109951163212833929.jpg\",\"companyId\":0,\"pic\":109951163212833929,\"picUrl\":\"http://p1.music.126.net/a2Q5DTqCWfDY0M_zzHhsYQ==/109951163212833929.jpg\",\"publishTime\":995558400007,\"description\":\"\",\"tags\":\"\",\"company\":\"日本コロムビア\",\"briefDesc\":\"\",\"artist\":{\"name\":\"\",\"id\":0,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0},\"songs\":[],\"alias\":[\"\u201c特摄之神\u201d圆谷英二诞辰100周年纪念CD-BOX\"],\"status\":0,\"copyrightId\":0,\"commentThreadId\":\"R_AL_3_38045121\",\"artists\":[{\"name\":\"宮内國郎\",\"id\":12278554,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0},{\"name\":\"冬木透\",\"id\":1087211,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0},{\"name\":\"伊福部昭\",\"id\":197592,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"img80x80\":\"https://p2.music.126.net/a2Q5DTqCWfDY0M_zzHhsYQ==/109951163212833929.jpg?param=80x80x1\"},\"starred\":false,\"popularity\":5.0,\"score\":5,\"starredNum\":0,\"duration\":200973,\"playedNum\":0,\"dayPlays\":0,\"hearTime\":0,\"ringtone\":null,\"crbt\":null,\"audition\":null,\"copyFrom\":\"\",\"commentThreadId\":\"R_SO_4_547969518\",\"rtUrl\":null,\"ftype\":0,\"rtUrls\":[],\"copyright\":0,\"hMusic\":{\"name\":\"\",\"id\":1444917122,\"size\":8041578,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":320000,\"playTime\":200973,\"volumeDelta\":4.0},\"mMusic\":{\"name\":\"\",\"id\":1444917123,\"size\":4824964,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":192000,\"playTime\":200973,\"volumeDelta\":5.0},\"lMusic\":{\"name\":\"\",\"id\":1444917124,\"size\":3216657,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":128000,\"playTime\":200973,\"volumeDelta\":5.0},\"bMusic\":{\"name\":\"\",\"id\":1444917124,\"size\":3216657,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":128000,\"playTime\":200973,\"volumeDelta\":5.0},\"rtype\":0,\"rurl\":null,\"mvid\":0,\"mp3Url\":null}}","expireTime":0,"uuid":null,"rcmdInfo":null,"actId":0,"pics":[{"width":994,"height":918,"originUrl":"https://p1.music.126.net/FC9G6NOnu47f_L53w_vGMw==/109951164198596788.jpg","squareUrl":"https://p1.music.126.net/tbsBKBHvKrREJ6RHdFZi2A==/109951164198593425.jpg","rectangleUrl":"https://p1.music.126.net/LIF_oN2-ijYZjXufUqAMAA==/109951164198592455.jpg","pcSquareUrl":"https://p1.music.126.net/zejSRfXC9eWNWtcQMt9HVA==/109951164198603566.jpg","pcRectangleUrl":"https://p1.music.126.net/UAstny7Gc1BDLyC8IgeV7g==/109951164198604557.jpg","format":"jpg"},{"width":1211,"height":864,"originUrl":"https://p1.music.126.net/7LEic64CTzZ1q8_AptRBXQ==/109951164198595874.jpg","squareUrl":"https://p1.music.126.net/hhI2oraBo7G2Dnq9Zwenvg==/109951164198592943.jpg","rectangleUrl":"https://p1.music.126.net/zej_ixqykHk8gbU38akhbw==/109951164198594391.jpg","pcSquareUrl":"https://p1.music.126.net/PDU81o8D_3r7UwwA3ZDypw==/109951164198597258.jpg","pcRectangleUrl":"https://p1.music.126.net/iS3Uka6-cPkNtFT6XlOjcQ==/109951164198604560.jpg","format":"jpg"},{"width":459,"height":640,"originUrl":"https://p1.music.126.net/spHnaxifQz57RsUkmAt_8Q==/109951164198599226.jpg","squareUrl":"https://p1.music.126.net/PzE55X7tOjn7-00MfZTrRA==/109951164198602651.jpg","rectangleUrl":"https://p1.music.126.net/sabLcPTC8M27UNTlMTU55A==/109951164198595878.jpg","pcSquareUrl":"https://p1.music.126.net/OoY-CtFA4Lbb941A8QEPVQ==/109951164198599707.jpg","pcRectangleUrl":"https://p1.music.126.net/8lK2cSxD4IHfnfYgekcnnA==/109951164198596792.jpg","format":"jpg"},{"width":1080,"height":1530,"originUrl":"https://p1.music.126.net/E3FQdGMvKfLV6pMZVV1krw==/109951164198592463.jpg","squareUrl":"https://p1.music.126.net/T_VrPI0Hly3rZ-t0UrH_rg==/109951164198601656.jpg","rectangleUrl":"https://p1.music.126.net/L7z_zcsLonCnxDi2mcCArw==/109951164198603573.jpg","pcSquareUrl":"https://p1.music.126.net/aA_JlrwIN1DO5Earaq0paQ==/109951164198592467.jpg","pcRectangleUrl":"https://p1.music.126.net/GRFonv58eee5P3jgwLut2A==/109951164198594873.jpg","format":"jpg"},{"width":400,"height":541,"originUrl":"https://p1.music.126.net/VuWkUeaJ0BOHS1Hk4wVM4Q==/109951164198594876.jpg","squareUrl":"https://p1.music.126.net/x_eTBBvI9ybrVTlD6bBMPQ==/109951164198598227.jpg","rectangleUrl":"https://p1.music.126.net/--u0DxIFwaEdfEHHpTjnfA==/109951164198604082.jpg","pcSquareUrl":"https://p1.music.126.net/0VoOYHomEiDruZWYc9JJtg==/109951164198594877.jpg","pcRectangleUrl":"https://p1.music.126.net/0_CUfXbiDJLKPYWkpEfirQ==/109951164198603578.jpg","format":"jpg"},{"width":500,"height":417,"originUrl":"https://p1.music.126.net/nzq7m653xt9x-AiEYlqTuA==/109951164198594879.jpg","squareUrl":"https://p1.music.126.net/mNl8GuXBYBqoY3lKfjaFCg==/109951164198604567.jpg","rectangleUrl":"https://p1.music.126.net/lXplfRduWGm8gRk99SjflQ==/109951164198593921.jpg","pcSquareUrl":"https://p1.music.126.net/9WBCJ586SDp-0Ae-CW9Nsw==/109951164198599232.jpg","pcRectangleUrl":"https://p1.music.126.net/7onrnIuPeioc8JlhV6DfyA==/109951164198605542.jpg","format":"jpg"},{"width":400,"height":400,"originUrl":"https://p1.music.126.net/1DW1kogSYnRzG12npquVBw==/109951164198599717.jpg","squareUrl":"https://p1.music.126.net/S-6KDIf_gnq7FhhYtEz3BA==/109951164198598235.jpg","rectangleUrl":"https://p1.music.126.net/5ldqhXkAujN3SgZswi6iTA==/109951164198604090.jpg","pcSquareUrl":"https://p1.music.126.net/ydeR60wDaW4IJBKHbObe0g==/109951164198603581.jpg","pcRectangleUrl":"https://p1.music.126.net/2BWEErgP-Jjzym-huEKATQ==/109951164198604571.jpg","format":"jpg"},{"width":1130,"height":1130,"originUrl":"https://p1.music.126.net/j_s4Aw5EciCDTBrrfQNjfQ==/109951164198596805.jpg","squareUrl":"https://p1.music.126.net/Vrc-jTvMlJ7w0PPA7ICSqw==/109951164198603162.jpg","rectangleUrl":"https://p1.music.126.net/KsbO9Eqmp_YNDXrpmyItdg==/109951164198604572.jpg","pcSquareUrl":"https://p1.music.126.net/lG0YnapKPV9HY5fnAgZDqQ==/109951164198603585.jpg","pcRectangleUrl":"https://p1.music.126.net/EQ_yED6g6o7Q1u5D2k9RIQ==/109951164198600214.jpg","format":"jpg"},{"width":299,"height":353,"originUrl":"https://p1.music.126.net/NJwhTgkyxCa6YeN8wrlTNA==/109951164198605545.jpg","squareUrl":"https://p1.music.126.net/USE8oUbTyGbQ_JWq2ME48A==/109951164198600215.jpg","rectangleUrl":"https://p1.music.126.net/DszXGynHeB0s4M9HFS81BQ==/109951164198597718.jpg","pcSquareUrl":"https://p1.music.126.net/jrjMGLN0FCfd0JN1DrB36Q==/109951164198596374.jpg","pcRectangleUrl":"https://p1.music.126.net/va86KCgYYPrmYt6k4GpgLA==/109951164198598703.jpg","format":"jpg"}],"showTime":*************,"tmplId":0,"id":**********,"type":18,"topEvent":false,"insiteForwardCount":11,"info":{"commentThread":{"id":"A_EV_2_**********_*********","resourceInfo":{"id":**********,"userId":*********,"name":"分享单曲：「円谷英二の肖像 (ウルトラQのおやじ)」","imgUrl":null,"creator":null,"eventType":18},"resourceType":2,"commentCount":27,"likedCount":114,"shareCount":18,"hotCount":16,"latestLikedUsers":[{"s":1536026891,"t":1563935687445},{"s":125480200,"t":1563779007268},{"s":531229899,"t":1563603220315},{"s":1520942724,"t":1563512931108},{"s":1316129489,"t":1563495695198},{"s":1372176262,"t":1563492078176},{"s":253390303,"t":1563374974141},{"s":385944444,"t":1563270126874},{"s":410116068,"t":1563262508028},{"s":54391624,"t":1563160354986}],"resourceOwnerId":*********,"resourceTitle":"分享单曲：「円谷英二の肖像 (ウルトラQのおやじ)」","resourceId":**********},"latestLikedUsers":null,"liked":false,"comments":null,"resourceType":2,"resourceId":**********,"shareCount":18,"commentCount":27,"likedCount":114,"threadId":"A_EV_2_**********_*********"}},{"actName":null,"forwardCount":1,"lotteryEventData":null,"user":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/P_GOWHWAqFIjN6oOqYsdbw==/109951164225662722.jpg","accountStatus":0,"gender":1,"city":1005500,"birthday":************,"userId":*********,"userType":0,"nickname":"璀璨的幻夢星光","signature":"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]","description":"","detailDescription":"","avatarImgId":109951164225662720,"backgroundImgId":109951163528584620,"backgroundUrl":"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"109951163528584619","avatarImgIdStr":"109951164225662722","urlAnalyze":false,"avatarImgId_str":"109951164225662722","followeds":5226},"eventTime":*************,"json":"{\"msg\":\"起床看泰迦了[爱心]\",\"program\":{\"mainSong\":null,\"songs\":null,\"dj\":{\"defaultAvatar\":false,\"province\":1000000,\"authStatus\":0,\"followed\":false,\"avatarUrl\":\"http://p1.music.126.net/HQWN4fvgmRPazLWD1irkAg==/109951164168186484.jpg\",\"accountStatus\":0,\"gender\":1,\"city\":1005500,\"birthday\":************,\"userId\":*********,\"userType\":0,\"nickname\":\"璀璨的幻夢星光\",\"signature\":\"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]\",\"description\":\"\",\"detailDescription\":\"\",\"avatarImgId\":109951164168186484,\"backgroundImgId\":109951163528584619,\"backgroundUrl\":\"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg\",\"authority\":0,\"mutual\":false,\"expertTags\":null,\"experts\":null,\"djStatus\":10,\"vipType\":0,\"remarkName\":null,\"backgroundImgIdStr\":\"109951163528584619\",\"avatarImgIdStr\":\"109951164168186484\",\"avatarImgId_str\":\"109951164168186484\",\"brand\":\"円谷プロ&奥特曼周年纪念歌曲电台（ULTRAMAN）\"},\"blurCoverUrl\":\"http://music.163.com/api/dj/img/blur/****************\",\"radio\":{\"id\":*********,\"dj\":null,\"name\":\"円谷プロ&奥特曼周年纪念歌曲电台（ULTRAMAN）\",\"picUrl\":\"http://p1.music.126.net/KNpLnjTys7A7GK2o5Vd-3g==/18975371672766681.jpg\",\"desc\":\"【影视资讯〗：《泰迦奥特曼》7月6日每周六10:00放送！\\n\\n电台简介：不用我说想必各位奥迷们都知道吧，7月7日是什么日子？没错！7月7日就是我们大家熟悉的摄影之神円谷英二导演的出生日期哦，他也是奥特曼的诞生之父。刚好本电台也是7月7日建成的，这也是为了纪念円谷他老人家吧\u2026\u2026\u2026\u2026在这里也祝奥特曼50周年诞生纪念日快乐！٩(ˊωˋ*)و✧\\n\u201c相信奥特曼还能再战50年！\u201d\\n在这里热衷感谢大家对本电台支持，以后也会为大家献出更多更好听的歌♪(^∇^*)\",\"subCount\":3938,\"programCount\":461,\"createTime\":1467842983344,\"categoryId\":4,\"category\":\"娱乐|影视\",\"radioFeeType\":0,\"feeScope\":0,\"buyed\":true,\"videos\":null,\"finished\":false,\"underShelf\":false,\"purchaseCount\":0,\"price\":0,\"originalPrice\":0,\"discountPrice\":null,\"lastProgramCreateTime\":1561703958390,\"lastProgramName\":null,\"lastProgramId\":2061892511,\"picId\":18975371672766681,\"rcmdText\":null,\"composeVideo\":false,\"subed\":true},\"subscribedCount\":0,\"reward\":false,\"mainTrackId\":1373158980,\"serialNum\":460,\"listenerCount\":21292,\"name\":\"【ウルトラマンタイガ】主題歌-「Buddy, steady, go!!」［试听］\",\"id\":2061776053,\"createTime\":1561089063572,\"description\":\"〖新番組〗\\n《ウルトラマンタイガ》OP：『Buddy, steady, go!!』（试听版）\\nアーティスト：寺島拓篤\\n作詞：寺島拓篤\\n作曲：渡部チェル\\n\\n発売日：2019年8月28日\\n発売・販売元：株式会社バンダイナムコアーツ\\n商品番号：初回限定盤LACM-34923／通常盤LACM-14923\\n税抜価格：初回限定盤1,800円＋税／通常盤1,200円＋税\",\"userId\":*********,\"coverUrl\":\"http://p1.music.126.net/jQRPCVVP8UDGzlW0Yxe7pw==/****************.jpg\",\"commentThreadId\":\"A_DJ_1_2061776053\",\"channels\":[\"娱乐八卦\"],\"titbits\":null,\"titbitImages\":null,\"pubStatus\":1,\"trackCount\":0,\"bdAuditStatus\":2,\"programFeeType\":0,\"buyed\":false,\"programDesc\":null,\"h5Links\":[],\"coverId\":****************,\"adjustedPlayCount\":21292.0,\"canReward\":false,\"auditStatus\":10,\"duration\":0,\"publish\":true,\"img80x80\":\"https://p1.music.126.net/jQRPCVVP8UDGzlW0Yxe7pw==/****************.jpg?param=80x80x1\"}}","expireTime":0,"uuid":null,"rcmdInfo":null,"actId":0,"pics":[{"width":1778,"height":2560,"originUrl":"https://p1.music.126.net/UBqqax5OU5VAoKtFEukjKw==/109951164195995738.jpg","squareUrl":"https://p1.music.126.net/WDiFrQtXoElIRx-TN0jHAw==/109951164196000656.jpg","rectangleUrl":"https://p1.music.126.net/tkab2N0NyVG1M6Ywm-npKQ==/109951164196006007.jpg","pcSquareUrl":"https://p1.music.126.net/Dt-ZE0wXh78pIFUrCP7CyA==/109951164196002636.jpg","pcRectangleUrl":"https://p1.music.126.net/96bG55s7N273fJAeK9dROQ==/109951164196005506.jpg","format":"jpg"},{"width":1920,"height":1080,"originUrl":"https://p1.music.126.net/4su5TSXHw6kDMZD__FG-XQ==/109951164196004562.jpg","squareUrl":"https://p1.music.126.net/qItO_NreEMxvzXWdP3NvTg==/109951164195994818.jpg","rectangleUrl":"https://p1.music.126.net/YNMKLTljmvADStvkeEercg==/109951164196000217.jpg","pcSquareUrl":"https://p1.music.126.net/p3XiQnJG7TGfcAA8m4Zdmw==/109951164196006011.jpg","pcRectangleUrl":"https://p1.music.126.net/FatlKkjJvCWtTdq5Wl909g==/109951164196002641.jpg","format":"jpg"}],"showTime":*************,"tmplId":0,"id":**********,"type":17,"topEvent":false,"insiteForwardCount":1,"info":{"commentThread":{"id":"A_EV_2_**********_*********","resourceInfo":{"id":**********,"userId":*********,"name":"分享DJ节目：「【ウルトラマンタイガ】主題歌-「Buddy, steady, go!!」［试听］」","imgUrl":null,"creator":null,"eventType":17},"resourceType":2,"commentCount":24,"likedCount":85,"shareCount":1,"hotCount":7,"latestLikedUsers":[{"s":125480200,"t":1563779008905},{"s":1316129489,"t":1563495696631},{"s":1372176262,"t":1563492233496},{"s":1436963722,"t":1563457956744},{"s":1634739248,"t":1563414731295},{"s":506221851,"t":1563355598107},{"s":348185391,"t":1563291293805},{"s":547523889,"t":1563291275413},{"s":410116068,"t":1563262509721},{"s":103366184,"t":1563074680092}],"resourceOwnerId":*********,"resourceTitle":"分享DJ节目：「【ウルトラマンタイガ】主題歌-「Buddy, steady, go!!」［试听］」","resourceId":**********},"latestLikedUsers":null,"liked":false,"comments":null,"resourceType":2,"resourceId":**********,"shareCount":1,"commentCount":24,"likedCount":85,"threadId":"A_EV_2_**********_*********"}},{"actName":null,"forwardCount":4,"lotteryEventData":null,"user":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/P_GOWHWAqFIjN6oOqYsdbw==/109951164225662722.jpg","accountStatus":0,"gender":1,"city":1005500,"birthday":************,"userId":*********,"userType":0,"nickname":"璀璨的幻夢星光","signature":"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]","description":"","detailDescription":"","avatarImgId":109951164225662720,"backgroundImgId":109951163528584620,"backgroundUrl":"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"109951163528584619","avatarImgIdStr":"109951164225662722","urlAnalyze":false,"avatarImgId_str":"109951164225662722","followeds":5226},"eventTime":*************,"json":"{\"msg\":\"【伪】新番組\\n《ウルトラマン幻夢ノフィー》预告图+Logo（未完全/调整中\u2026\u2026）\\n7月7日（日）あさ9時からテレビ東京系にて放送スタート！\",\"song\":{\"name\":\"myself (off vocal)\",\"id\":********,\"position\":4,\"alias\":[],\"status\":0,\"fee\":0,\"copyrightId\":663018,\"disc\":\"1\",\"no\":4,\"artists\":[{\"name\":\"鈴木達央\",\"id\":15337,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"album\":{\"name\":\"VOICE\",\"id\":3139024,\"type\":\"EP/Single\",\"size\":4,\"picId\":2893914605438838,\"blurPicUrl\":\"http://p1.music.126.net/Qb8Oag4sCp3NRJO8Ea79AQ==/2893914605438838.jpg\",\"companyId\":0,\"pic\":2893914605438838,\"picUrl\":\"http://p1.music.126.net/Qb8Oag4sCp3NRJO8Ea79AQ==/2893914605438838.jpg\",\"publishTime\":1121875200000,\"description\":\"\",\"tags\":\"\",\"company\":\"lantis\",\"briefDesc\":\"\",\"artist\":{\"name\":\"\",\"id\":0,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0},\"songs\":[],\"alias\":[],\"status\":0,\"copyrightId\":0,\"commentThreadId\":\"R_AL_3_3139024\",\"artists\":[{\"name\":\"鈴木達央\",\"id\":15337,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"img80x80\":\"https://p2.music.126.net/Qb8Oag4sCp3NRJO8Ea79AQ==/2893914605438838.jpg?param=80x80x1\"},\"starred\":false,\"popularity\":20.0,\"score\":20,\"starredNum\":0,\"duration\":207000,\"playedNum\":0,\"dayPlays\":0,\"hearTime\":0,\"ringtone\":null,\"crbt\":null,\"audition\":null,\"copyFrom\":\"\",\"commentThreadId\":\"R_SO_4_********\",\"rtUrl\":null,\"ftype\":0,\"rtUrls\":[],\"copyright\":0,\"rtype\":0,\"rurl\":null,\"mvid\":0,\"bMusic\":{\"name\":null,\"id\":61504941,\"size\":2487212,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":96000,\"playTime\":207000,\"volumeDelta\":0.0},\"mp3Url\":null,\"hMusic\":{\"name\":null,\"id\":61504939,\"size\":8290365,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":320000,\"playTime\":207000,\"volumeDelta\":0.0},\"mMusic\":{\"name\":null,\"id\":61504940,\"size\":4145256,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":160000,\"playTime\":207000,\"volumeDelta\":0.0},\"lMusic\":{\"name\":null,\"id\":61504941,\"size\":2487212,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":96000,\"playTime\":207000,\"volumeDelta\":0.0}}}","expireTime":0,"uuid":null,"rcmdInfo":null,"actId":0,"pics":[{"width":2289,"height":1526,"originUrl":"https://p1.music.126.net/emOUtesFxU44ZIDKZApZ0A==/109951164175241284.jpg","squareUrl":"https://p1.music.126.net/J6_repa4uqRX2nSkUeakzA==/109951164175239856.jpg","rectangleUrl":"https://p1.music.126.net/z4LIf1L9YjklgEvotkWBJA==/109951164175247526.jpg","pcSquareUrl":"https://p1.music.126.net/9tk0ZtN-7OVi9C38LVEHzA==/109951164175238382.jpg","pcRectangleUrl":"https://p1.music.126.net/lodlJdpCcpbtJTg7ZcPZhQ==/109951164175242215.jpg","format":"jpg"}],"showTime":*************,"tmplId":0,"id":**********,"type":18,"topEvent":false,"insiteForwardCount":3,"info":{"commentThread":{"id":"A_EV_2_**********_*********","resourceInfo":{"id":**********,"userId":*********,"name":"分享单曲：「myself (off vocal)」","imgUrl":null,"creator":null,"eventType":18},"resourceType":2,"commentCount":18,"likedCount":71,"shareCount":4,"hotCount":11,"latestLikedUsers":[{"s":125480200,"t":1563779009820},{"s":136573487,"t":1563631652177},{"s":301830375,"t":1563544710601},{"s":1316129489,"t":1563495701512},{"s":410116068,"t":1563262510441},{"s":103366184,"t":1563074681152},{"s":590859641,"t":1563062531314},{"s":375976156,"t":1562741098636},{"s":135427249,"t":1562589511630},{"s":343373041,"t":1562584306051}],"resourceOwnerId":*********,"resourceTitle":"分享单曲：「myself (off vocal)」","resourceId":**********},"latestLikedUsers":null,"liked":false,"comments":null,"resourceType":2,"resourceId":**********,"shareCount":4,"commentCount":18,"likedCount":71,"threadId":"A_EV_2_**********_*********"}},{"actName":"迷惑行为大赏","forwardCount":1,"lotteryEventData":null,"user":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/P_GOWHWAqFIjN6oOqYsdbw==/109951164225662722.jpg","accountStatus":0,"gender":1,"city":1005500,"birthday":************,"userId":*********,"userType":0,"nickname":"璀璨的幻夢星光","signature":"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]","description":"","detailDescription":"","avatarImgId":109951164225662720,"backgroundImgId":109951163528584620,"backgroundUrl":"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"109951163528584619","avatarImgIdStr":"109951164225662722","urlAnalyze":false,"avatarImgId_str":"109951164225662722","followeds":5226},"eventTime":*************,"json":"{\"msg\":\"？？？#迷惑行为大赏#\",\"song\":{\"name\":\"ほえ?\",\"id\":********,\"position\":11,\"alias\":[],\"status\":0,\"fee\":0,\"copyrightId\":663018,\"disc\":\"1\",\"no\":11,\"artists\":[{\"name\":\"根岸貴幸\",\"id\":15027,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"album\":{\"name\":\"カードキャプターさくら オリジナル・サウンドトラック\",\"id\":3051011,\"type\":null,\"size\":26,\"picId\":2543170396428208,\"blurPicUrl\":\"http://p1.music.126.net/lt5YIxydPPfnIkxoIsh7ZA==/2543170396428208.jpg\",\"companyId\":0,\"pic\":2543170396428208,\"picUrl\":\"http://p1.music.126.net/lt5YIxydPPfnIkxoIsh7ZA==/2543170396428208.jpg\",\"publishTime\":901123200000,\"description\":\"\",\"tags\":\"\",\"company\":\"ビクターエンタテインメント\",\"briefDesc\":\"\",\"artist\":{\"name\":\"\",\"id\":0,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0},\"songs\":[],\"alias\":[\"魔卡少女樱 TV版原声集1\"],\"status\":0,\"copyrightId\":0,\"commentThreadId\":\"R_AL_3_3051011\",\"artists\":[{\"name\":\"根岸貴幸\",\"id\":15027,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"img80x80\":\"https://p2.music.126.net/lt5YIxydPPfnIkxoIsh7ZA==/2543170396428208.jpg?param=80x80x1\"},\"starred\":false,\"popularity\":100.0,\"score\":100,\"starredNum\":0,\"duration\":91428,\"playedNum\":0,\"dayPlays\":0,\"hearTime\":0,\"ringtone\":null,\"crbt\":null,\"audition\":null,\"copyFrom\":\"\",\"commentThreadId\":\"R_SO_4_********\",\"rtUrl\":null,\"ftype\":0,\"rtUrls\":[],\"copyright\":2,\"rtype\":0,\"rurl\":null,\"mvid\":0,\"bMusic\":{\"name\":\"ほえ?\",\"id\":52014891,\"size\":1097882,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":96000,\"playTime\":91428,\"volumeDelta\":4.97375},\"mp3Url\":null,\"hMusic\":{\"name\":\"ほえ?\",\"id\":52014889,\"size\":3657150,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":320000,\"playTime\":91428,\"volumeDelta\":4.64859},\"mMusic\":{\"name\":\"ほえ?\",\"id\":52014890,\"size\":1829102,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":160000,\"playTime\":91428,\"volumeDelta\":5.11877},\"lMusic\":{\"name\":\"ほえ?\",\"id\":52014891,\"size\":1097882,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":96000,\"playTime\":91428,\"volumeDelta\":4.97375}}}","expireTime":0,"uuid":null,"rcmdInfo":null,"actId":45429555,"pics":[{"width":1069,"height":883,"originUrl":"https://p1.music.126.net/cU4XWbD56mP54gr7hZ6Pkg==/109951164149343194.jpg","squareUrl":"https://p1.music.126.net/ye46lz-1fip3DQVMYgo-Vw==/109951164149344619.jpg","rectangleUrl":"https://p1.music.126.net/GGIda18pGW49jVZF8RSwjg==/109951164149342192.jpg","pcSquareUrl":"https://p1.music.126.net/4GOa_wLb8J1FM90xmyzGig==/109951164149337847.jpg","pcRectangleUrl":"https://p1.music.126.net/gsqqjjeDTk7DvLCD9kCXnw==/109951164149347013.jpg","format":"jpg"}],"showTime":*************,"tmplId":0,"id":**********,"type":18,"topEvent":false,"insiteForwardCount":1,"info":{"commentThread":{"id":"A_EV_2_**********_*********","resourceInfo":{"id":**********,"userId":*********,"name":"分享单曲：「ほえ?」","imgUrl":null,"creator":null,"eventType":18},"resourceType":2,"commentCount":25,"likedCount":90,"shareCount":1,"hotCount":8,"latestLikedUsers":[{"s":472428539,"t":1564041182284},{"s":333067071,"t":1563953691980},{"s":125480200,"t":1563779011799},{"s":266337556,"t":1563771499794},{"s":563344190,"t":1563637832477},{"s":136573487,"t":1563631644847},{"s":1511490887,"t":1563626402176},{"s":1333716335,"t":1563602220443},{"s":1525427555,"t":1563517206523},{"s":1316129489,"t":1563495705425}],"resourceOwnerId":*********,"resourceTitle":"分享单曲：「ほえ?」","resourceId":**********},"latestLikedUsers":null,"liked":false,"comments":null,"resourceType":2,"resourceId":**********,"shareCount":1,"commentCount":25,"likedCount":90,"threadId":"A_EV_2_**********_*********"}},{"actName":null,"forwardCount":0,"lotteryEventData":null,"user":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/P_GOWHWAqFIjN6oOqYsdbw==/109951164225662722.jpg","accountStatus":0,"gender":1,"city":1005500,"birthday":************,"userId":*********,"userType":0,"nickname":"璀璨的幻夢星光","signature":"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]","description":"","detailDescription":"","avatarImgId":109951164225662720,"backgroundImgId":109951163528584620,"backgroundUrl":"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"109951163528584619","avatarImgIdStr":"109951164225662722","urlAnalyze":false,"avatarImgId_str":"109951164225662722","followeds":5226},"eventTime":*************,"json":"{\"msg\":\"清了一些关注，如有不小心取关的，可以和我说一下_(:з」∠)_\",\"song\":{\"name\":\"poco\",\"id\":********,\"position\":10,\"alias\":[],\"status\":0,\"fee\":0,\"copyrightId\":663018,\"disc\":\"1\",\"no\":10,\"artists\":[{\"name\":\"まふまふ\",\"id\":896913,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"album\":{\"name\":\"アフターレインクエスト\",\"id\":2805030,\"type\":\"专辑\",\"size\":13,\"picId\":6050612487968730,\"blurPicUrl\":\"http://p1.music.126.net/rtKW0Vy2juNnXxbEFLzsJQ==/6050612487968730.jpg\",\"companyId\":0,\"pic\":6050612487968730,\"picUrl\":\"http://p1.music.126.net/rtKW0Vy2juNnXxbEFLzsJQ==/6050612487968730.jpg\",\"publishTime\":1398441600000,\"description\":\"\",\"tags\":\"\",\"company\":\"THE VOC@LOiD 超 M@STER28\",\"briefDesc\":\"\",\"artist\":{\"name\":\"\",\"id\":0,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0},\"songs\":[],\"alias\":[\"そらいろまふらー(そらる×まふまふ)\"],\"status\":0,\"copyrightId\":0,\"commentThreadId\":\"R_AL_3_2805030\",\"artists\":[{\"name\":\"そらる\",\"id\":15513,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0},{\"name\":\"まふまふ\",\"id\":896913,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"img80x80\":\"https://p2.music.126.net/rtKW0Vy2juNnXxbEFLzsJQ==/6050612487968730.jpg?param=80x80x1\"},\"starred\":false,\"popularity\":95.0,\"score\":95,\"starredNum\":0,\"duration\":174552,\"playedNum\":0,\"dayPlays\":0,\"hearTime\":0,\"ringtone\":null,\"crbt\":null,\"audition\":null,\"copyFrom\":\"\",\"commentThreadId\":\"R_SO_4_********\",\"rtUrl\":null,\"ftype\":0,\"rtUrls\":[],\"copyright\":2,\"hMusic\":{\"name\":\"poco\",\"id\":47040826,\"size\":7032313,\"extension\":\"mp3\",\"sr\":48000,\"dfsId\":0,\"bitrate\":320000,\"playTime\":174552,\"volumeDelta\":-2.34},\"mMusic\":{\"name\":\"poco\",\"id\":47040827,\"size\":3540313,\"extension\":\"mp3\",\"sr\":48000,\"dfsId\":0,\"bitrate\":160000,\"playTime\":174552,\"volumeDelta\":-1.93},\"lMusic\":{\"name\":\"poco\",\"id\":47040828,\"size\":2143513,\"extension\":\"mp3\",\"sr\":48000,\"dfsId\":0,\"bitrate\":96000,\"playTime\":174552,\"volumeDelta\":-1.96},\"bMusic\":{\"name\":\"poco\",\"id\":47040828,\"size\":2143513,\"extension\":\"mp3\",\"sr\":48000,\"dfsId\":0,\"bitrate\":96000,\"playTime\":174552,\"volumeDelta\":-1.96},\"rtype\":0,\"rurl\":null,\"mvid\":0,\"mp3Url\":null}}","expireTime":0,"uuid":null,"rcmdInfo":null,"actId":0,"pics":[{"width":1080,"height":841,"originUrl":"https://p1.music.126.net/BivzschgW1WljJ8cvdRXjA==/109951164146319088.jpg","squareUrl":"https://p1.music.126.net/dm3HYl8dZmn1NN7JPRaSBA==/109951164146320038.jpg","rectangleUrl":"https://p1.music.126.net/zcLCTqMP9DuAy9rofwTtdw==/109951164146319090.jpg","pcSquareUrl":"https://p1.music.126.net/WQvrYCq8JN7S8q9m25GZdw==/109951164146318606.jpg","pcRectangleUrl":"https://p1.music.126.net/dPkRGsy466-4oCbyXz-72w==/109951164146318143.jpg","format":"jpg"}],"showTime":*************,"tmplId":0,"id":**********,"type":18,"topEvent":false,"insiteForwardCount":0,"info":{"commentThread":{"id":"A_EV_2_**********_*********","resourceInfo":{"id":**********,"userId":*********,"name":"分享单曲：「poco」","imgUrl":null,"creator":null,"eventType":18},"resourceType":2,"commentCount":23,"likedCount":64,"shareCount":0,"hotCount":8,"latestLikedUsers":[{"s":125480200,"t":1563779014568},{"s":136573487,"t":1563631641313},{"s":1511490887,"t":1563626465173},{"s":1316129489,"t":1563495715260},{"s":410116068,"t":1563262521065},{"s":103366184,"t":1563074686010},{"s":135427249,"t":1562589514934},{"s":1653916066,"t":1562506305157},{"s":1352058031,"t":1562483165302},{"s":387936012,"t":1562440990053}],"resourceOwnerId":*********,"resourceTitle":"分享单曲：「poco」","resourceId":**********},"latestLikedUsers":null,"liked":false,"comments":null,"resourceType":2,"resourceId":**********,"shareCount":0,"commentCount":23,"likedCount":64,"threadId":"A_EV_2_**********_*********"}},{"actName":null,"forwardCount":0,"lotteryEventData":null,"user":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/P_GOWHWAqFIjN6oOqYsdbw==/109951164225662722.jpg","accountStatus":0,"gender":1,"city":1005500,"birthday":************,"userId":*********,"userType":0,"nickname":"璀璨的幻夢星光","signature":"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]","description":"","detailDescription":"","avatarImgId":109951164225662720,"backgroundImgId":109951163528584620,"backgroundUrl":"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"109951163528584619","avatarImgIdStr":"109951164225662722","urlAnalyze":false,"avatarImgId_str":"109951164225662722","followeds":5226},"eventTime":*************,"json":"{\"msg\":\"至少隔壁在剧情方面能圆的回来，这简直看得我一脸懵[多多捂脸]\",\"song\":{\"name\":\"仮面ライダー平成ジェネレーションズ FOREVER_タイトル\",\"id\":**********,\"position\":0,\"alias\":[],\"status\":0,\"fee\":8,\"copyrightId\":457010,\"disc\":\"01\",\"no\":4,\"artists\":[{\"name\":\"川井憲次\",\"id\":14437,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"album\":{\"name\":\"仮面ライダー平成ジェネレーションズFOREVER オリジナル サウンドトラック\",\"id\":74939049,\"type\":\"专辑\",\"size\":45,\"picId\":109951163735349277,\"blurPicUrl\":\"http://p2.music.126.net/t84rDHyXmOidH8uS2obnPg==/109951163735349277.jpg\",\"companyId\":0,\"pic\":109951163735349277,\"picUrl\":\"http://p2.music.126.net/t84rDHyXmOidH8uS2obnPg==/109951163735349277.jpg\",\"publishTime\":1545408000000,\"description\":\"\",\"tags\":\"\",\"company\":\"(P)2018 AVEX ENTERTAINMENT INC.\",\"briefDesc\":\"\",\"artist\":{\"name\":\"\",\"id\":0,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0},\"songs\":[],\"alias\":[\"剧场版《假面骑士平成世代 FOREVER》原声带\"],\"status\":0,\"copyrightId\":457010,\"commentThreadId\":\"R_AL_3_74939049\",\"artists\":[{\"name\":\"佐橋俊彦\",\"id\":15291,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0},{\"name\":\"川井憲次\",\"id\":14437,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"img80x80\":\"https://p1.music.126.net/t84rDHyXmOidH8uS2obnPg==/109951163735349277.jpg?param=80x80x1\"},\"starred\":false,\"popularity\":45.0,\"score\":45,\"starredNum\":0,\"duration\":54666,\"playedNum\":0,\"dayPlays\":0,\"hearTime\":0,\"ringtone\":\"\",\"crbt\":null,\"audition\":null,\"copyFrom\":\"\",\"commentThreadId\":\"R_SO_4_**********\",\"rtUrl\":null,\"ftype\":0,\"rtUrls\":[],\"copyright\":0,\"mvid\":0,\"bMusic\":{\"name\":null,\"id\":3539672595,\"size\":875668,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":128000,\"playTime\":54666,\"volumeDelta\":-1.0},\"mp3Url\":null,\"rtype\":0,\"rurl\":null,\"hMusic\":{\"name\":null,\"id\":3539672593,\"size\":2189105,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":320000,\"playTime\":54666,\"volumeDelta\":-2.0},\"mMusic\":{\"name\":null,\"id\":3539672594,\"size\":1313480,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":192000,\"playTime\":54666,\"volumeDelta\":-1.0},\"lMusic\":{\"name\":null,\"id\":3539672595,\"size\":875668,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":128000,\"playTime\":54666,\"volumeDelta\":-1.0},\"transNames\":[\"假面骑士 平成GENERATIONS FOREVER_Title\"]}}","expireTime":0,"uuid":null,"rcmdInfo":null,"actId":0,"pics":[{"width":1080,"height":1197,"originUrl":"https://p1.music.126.net/V5Bi3gh_ATm7kBUYDzgBWA==/109951164066539112.jpg","squareUrl":"https://p1.music.126.net/ipCdRYHTkIffqVHeOZiAHg==/109951164066539622.jpg","rectangleUrl":"https://p1.music.126.net/jV6o5_bwm3aEPJdbxgfVzQ==/109951164066536206.jpg","pcSquareUrl":"https://p1.music.126.net/A2oH5JFQUSMF9gauZMp0yg==/109951164066537623.jpg","pcRectangleUrl":"https://p1.music.126.net/jbiimSxpnP7GKr6hceREpg==/109951164066531323.jpg","format":"jpg"},{"width":1000,"height":1126,"originUrl":"https://p1.music.126.net/_onkrHDHeMjJnnwmOtsR5Q==/109951164066539120.jpg","squareUrl":"https://p1.music.126.net/NOV_dQx2UVbVkul9moF8QA==/109951164066538112.jpg","rectangleUrl":"https://p1.music.126.net/xcY-9OqwueAU2443jXV0cQ==/109951164066531324.jpg","pcSquareUrl":"https://p1.music.126.net/Upx0uTjyxLgkoYaJNqU6YA==/109951164066537158.jpg","pcRectangleUrl":"https://p1.music.126.net/r_b2ccedn627ICqjypAqtw==/109951164066539122.jpg","format":"jpg"},{"width":1361,"height":1080,"originUrl":"https://p1.music.126.net/kjoY2o5eWBmOutliNadIRw==/109951164066532328.jpg","squareUrl":"https://p1.music.126.net/7wFtpuQKlravTegO04bOcA==/109951164066539124.jpg","rectangleUrl":"https://p1.music.126.net/qGiN3qoKtHMHdvUbHQ4XnA==/109951164066535234.jpg","pcSquareUrl":"https://p1.music.126.net/s5dHzZyLTzbH4JgbHLrqyw==/109951164066536700.jpg","pcRectangleUrl":"https://p1.music.126.net/2UcQNQcXNxvQDmLS7ROAzQ==/109951164066540593.jpg","format":"jpg"}],"showTime":*************,"tmplId":0,"id":**********,"type":18,"topEvent":false,"insiteForwardCount":0,"info":{"commentThread":{"id":"A_EV_2_**********_*********","resourceInfo":{"id":**********,"userId":*********,"name":"分享单曲：「仮面ライダー平成ジェネレーションズ FOREVER_タイトル」","imgUrl":null,"creator":null,"eventType":18},"resourceType":2,"commentCount":32,"likedCount":106,"shareCount":0,"hotCount":16,"latestLikedUsers":[{"s":1536026891,"t":1563935703233},{"s":125480200,"t":1563779076280},{"s":1316129489,"t":1563495717217},{"s":410116068,"t":1563262525424},{"s":100359268,"t":1563180767126},{"s":590859641,"t":1563062552404},{"s":583922871,"t":1562682449358},{"s":1875755515,"t":1562405109320},{"s":1789698440,"t":1562214752382},{"s":45804882,"t":1562155518898}],"resourceOwnerId":*********,"resourceTitle":"分享单曲：「仮面ライダー平成ジェネレーションズ FOREVER_タイトル」","resourceId":**********},"latestLikedUsers":null,"liked":false,"comments":null,"resourceType":2,"resourceId":**********,"shareCount":0,"commentCount":32,"likedCount":106,"threadId":"A_EV_2_**********_*********"}},{"actName":null,"forwardCount":0,"lotteryEventData":null,"user":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/P_GOWHWAqFIjN6oOqYsdbw==/109951164225662722.jpg","accountStatus":0,"gender":1,"city":1005500,"birthday":************,"userId":*********,"userType":0,"nickname":"璀璨的幻夢星光","signature":"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]","description":"","detailDescription":"","avatarImgId":109951164225662720,"backgroundImgId":109951163528584620,"backgroundUrl":"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"109951163528584619","avatarImgIdStr":"109951164225662722","urlAnalyze":false,"avatarImgId_str":"109951164225662722","followeds":5226},"eventTime":*************,"json":"{\"msg\":\"那啥\u2026\u2026首页还没看forever的，应该只有我一个了吧[多多捂脸]\",\"song\":{\"name\":\"仮面ライダー平成ジェネレーションズ FOREVER メドレー (D.A. RE-BUILD MIX)\",\"id\":**********,\"position\":0,\"alias\":[],\"status\":0,\"fee\":8,\"copyrightId\":457010,\"disc\":\"01\",\"no\":45,\"artists\":[{\"name\":\"仮面ライダー平成ジェネレーションズ\",\"id\":31062899,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"album\":{\"name\":\"仮面ライダー平成ジェネレーションズFOREVER オリジナル サウンドトラック\",\"id\":74939049,\"type\":\"专辑\",\"size\":45,\"picId\":109951163735349277,\"blurPicUrl\":\"http://p1.music.126.net/t84rDHyXmOidH8uS2obnPg==/109951163735349277.jpg\",\"companyId\":0,\"pic\":109951163735349277,\"picUrl\":\"http://p1.music.126.net/t84rDHyXmOidH8uS2obnPg==/109951163735349277.jpg\",\"publishTime\":1545408000000,\"description\":\"\",\"tags\":\"\",\"company\":\"(P)2018 AVEX ENTERTAINMENT INC.\",\"briefDesc\":\"\",\"artist\":{\"name\":\"\",\"id\":0,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0},\"songs\":[],\"alias\":[\"剧场版《假面骑士平成世代 FOREVER》原声带\"],\"status\":0,\"copyrightId\":457010,\"commentThreadId\":\"R_AL_3_74939049\",\"artists\":[{\"name\":\"佐橋俊彦\",\"id\":15291,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0},{\"name\":\"川井憲次\",\"id\":14437,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0}],\"img80x80\":\"https://p1.music.126.net/t84rDHyXmOidH8uS2obnPg==/109951163735349277.jpg?param=80x80x1\"},\"starred\":false,\"popularity\":100.0,\"score\":100,\"starredNum\":0,\"duration\":183866,\"playedNum\":0,\"dayPlays\":0,\"hearTime\":0,\"ringtone\":\"\",\"crbt\":null,\"audition\":null,\"copyFrom\":\"\",\"commentThreadId\":\"R_SO_4_**********\",\"rtUrl\":null,\"ftype\":0,\"rtUrls\":[],\"copyright\":0,\"hMusic\":{\"name\":null,\"id\":3539673120,\"size\":7357170,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":320000,\"playTime\":183866,\"volumeDelta\":-1.0},\"mMusic\":{\"name\":null,\"id\":3539673121,\"size\":4414320,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":192000,\"playTime\":183866,\"volumeDelta\":-1.0},\"lMusic\":{\"name\":null,\"id\":3539673122,\"size\":2942894,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":128000,\"playTime\":183866,\"volumeDelta\":-1.0},\"bMusic\":{\"name\":null,\"id\":3539673122,\"size\":2942894,\"extension\":\"mp3\",\"sr\":44100,\"dfsId\":0,\"bitrate\":128000,\"playTime\":183866,\"volumeDelta\":-1.0},\"rtype\":0,\"rurl\":null,\"mvid\":0,\"mp3Url\":null,\"transNames\":[\"假面骑士 平成GENERATIONS FOREVER 组曲 D.A. RE-BUILD MIX\"]}}","expireTime":0,"uuid":null,"rcmdInfo":null,"actId":0,"pics":[{"width":1920,"height":1080,"originUrl":"https://p1.music.126.net/BaIM3_wjh3icPdGHKQ_4ig==/109951164066218642.jpg","squareUrl":"https://p1.music.126.net/UxF8PqMUjPg00_Xk2ou68g==/109951164066212321.jpg","rectangleUrl":"https://p1.music.126.net/vACo6wqgRlbNyq78y-XH9w==/109951164066208493.jpg","pcSquareUrl":"https://p1.music.126.net/ZTIjZFugJdkmuka5KI7rXw==/109951164066210831.jpg","pcRectangleUrl":"https://p1.music.126.net/5ah6mA4EdceEE9ZT_9pqkw==/109951164066216725.jpg","format":"jpg"},{"width":1071,"height":1410,"originUrl":"https://p1.music.126.net/GfI59LKp2TfIded2JjOVcA==/109951164066214744.jpg","squareUrl":"https://p1.music.126.net/hr_rxFuvkKl-bY1hxoB5fg==/109951164066211344.jpg","rectangleUrl":"https://p1.music.126.net/UxK241DvmfPkXEqqBt-s3Q==/109951164066210834.jpg","pcSquareUrl":"https://p1.music.126.net/cwiA7o_e3JnkpbCfQnnFQw==/109951164066210346.jpg","pcRectangleUrl":"https://p1.music.126.net/Nq-i6oPNVgXIcJC244mHxQ==/109951164066217673.jpg","format":"jpg"},{"width":1080,"height":1086,"originUrl":"https://p1.music.126.net/SD09B_T-n9PLCUi5tVmg7Q==/109951164066209415.jpg","squareUrl":"https://p1.music.126.net/qOsZdaTTMt9rwk5PU_tMgA==/109951164066216233.jpg","rectangleUrl":"https://p1.music.126.net/NB8o1X6jqX5fEEend_jgew==/109951164066209418.jpg","pcSquareUrl":"https://p1.music.126.net/gs2Rb-bTHYwZ2h1SvUlWvg==/109951164066208498.jpg","pcRectangleUrl":"https://p1.music.126.net/nOeVhTfe8QSgIVCyj5J6XQ==/109951164066210839.jpg","format":"jpg"}],"showTime":*************,"tmplId":0,"id":6981149604,"type":18,"topEvent":false,"insiteForwardCount":0,"info":{"commentThread":{"id":"A_EV_2_6981149604_*********","resourceInfo":{"id":6981149604,"userId":*********,"name":"分享单曲：「仮面ライダー平成ジェネレーションズ FOREVER メドレー (D.A. RE-BUILD MIX)」","imgUrl":null,"creator":null,"eventType":18},"resourceType":2,"commentCount":43,"likedCount":77,"shareCount":0,"hotCount":18,"latestLikedUsers":[{"s":125480200,"t":1563779080382},{"s":1316129489,"t":1563495720100},{"s":410116068,"t":1563262527321},{"s":426680525,"t":1562569481232},{"s":1875755515,"t":1562405110895},{"s":136642223,"t":1562393928825},{"s":368883635,"t":1562125074666},{"s":129526900,"t":1561810105984},{"s":1772445458,"t":1561674960064},{"s":608535450,"t":1561378505146}],"resourceOwnerId":*********,"resourceTitle":"分享单曲：「仮面ライダー平成ジェネレーションズ FOREVER メドレー (D.A. RE-BUILD MIX)」","resourceId":6981149604},"latestLikedUsers":null,"liked":false,"comments":null,"resourceType":2,"resourceId":6981149604,"shareCount":0,"commentCount":43,"likedCount":77,"threadId":"A_EV_2_6981149604_*********"}}]
     * code : 200
     */

    private long lasttime;
    private boolean more;
    private int size;
    private int code;
    private List<EventsBean> events;

    public long getLasttime() {
        return lasttime;
    }

    public void setLasttime(long lasttime) {
        this.lasttime = lasttime;
    }

    public boolean isMore() {
        return more;
    }

    public void setMore(boolean more) {
        this.more = more;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public List<EventsBean> getEvents() {
        return events;
    }

    public void setEvents(List<EventsBean> events) {
        this.events = events;
    }

    public static class EventsBean {
        /**
         * actName : null
         * forwardCount : 3
         * lotteryEventData : null
         * user : {"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/P_GOWHWAqFIjN6oOqYsdbw==/109951164225662722.jpg","accountStatus":0,"gender":1,"city":1005500,"birthday":************,"userId":*********,"userType":0,"nickname":"璀璨的幻夢星光","signature":"一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]","description":"","detailDescription":"","avatarImgId":109951164225662720,"backgroundImgId":109951163528584620,"backgroundUrl":"http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"109951163528584619","avatarImgIdStr":"109951164225662722","urlAnalyze":false,"avatarImgId_str":"109951164225662722","followeds":5226}
         * eventTime : *************
         * json : {"msg":"继续接上一条动态：\n7月10日除了是奥特曼之日外，同时也是《银河奥特曼》的首播日。是继梦比优斯7年之后首部以TV形式正式登场的奥特英雄。\n故事讲述了一群年轻人追寻着梦想与奥特英雄们相偶一起冒险的热血故事。\n从此刻起奥特曼系列再次复活！他们又回归在人们的眼前，也从此开启了新世代奥特曼的第1页","song":{"name":"Legend of Galaxy ~銀河の覇者","id":27630046,"position":4,"alias":["特摄《银河奥特曼特别剧场版》片头曲 "," 特撮「ウルトラマンギンガ 劇場スペシャル」OPテーマ"],"status":0,"fee":0,"copyrightId":663018,"disc":"1","no":4,"artists":[{"name":"高見沢俊彦","id":15653,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"雷神 ","id":2646122,"type":"专辑","size":11,"picId":1984618488161533,"blurPicUrl":"http://p2.music.126.net/JD5bmx2AUOPga7qSIx-sUQ==/1984618488161533.jpg","companyId":0,"pic":1984618488161533,"picUrl":"http://p2.music.126.net/JD5bmx2AUOPga7qSIx-sUQ==/1984618488161533.jpg","publishTime":1375200000007,"description":"","tags":"","company":"EMI Records Japan","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_2646122","artists":[{"name":"高見沢俊彦","id":15653,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"img80x80":"https://p1.music.126.net/JD5bmx2AUOPga7qSIx-sUQ==/1984618488161533.jpg?param=80x80x1"},"starred":false,"popularity":85.0,"score":85,"starredNum":0,"duration":354000,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_27630046","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"rtype":0,"rurl":null,"mvid":0,"bMusic":{"name":null,"id":65141836,"size":5668740,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":354000,"volumeDelta":-43300.0},"mp3Url":null,"hMusic":{"name":null,"id":65141835,"size":14171284,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":354000,"volumeDelta":-46400.0},"mMusic":{"name":null,"id":92109324,"size":8502921,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":354000,"volumeDelta":-44000.0},"lMusic":{"name":null,"id":65141836,"size":5668740,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":354000,"volumeDelta":-43300.0}}}
         * expireTime : 0
         * uuid : null
         * rcmdInfo : null
         * actId : 0
         * pics : [{"width":560,"height":792,"originUrl":"https://p1.music.126.net/JidENtRyj8PdSK3kYn8shQ==/109951164205011321.jpg","squareUrl":"https://p1.music.126.net/qu3jn-FjIQ4RlPey7b0kSw==/109951164205013214.jpg","rectangleUrl":"https://p1.music.126.net/tPv7c0rcNov9epWEqlZJDA==/109951164205016150.jpg","pcSquareUrl":"https://p1.music.126.net/SsjDOgM_kCcbzBqDBTo28Q==/109951164205012730.jpg","pcRectangleUrl":"https://p1.music.126.net/faNh4O7vHSxKM3alojw7VA==/109951164205017123.jpg","format":"jpg"},{"width":560,"height":792,"originUrl":"https://p1.music.126.net/yEddOoZm9TFIoYLBHP5Ihw==/109951164205016662.jpg","squareUrl":"https://p1.music.126.net/pMHA9A_BKrTYBOVQ1GVDbQ==/109951164205012240.jpg","rectangleUrl":"https://p1.music.126.net/iv0e_2UIl1DC7Ii5Q5ovsg==/109951164205014172.jpg","pcSquareUrl":"https://p1.music.126.net/vefi2m_MrxKr4Ib9asRUVg==/109951164205007494.jpg","pcRectangleUrl":"https://p1.music.126.net/Y_pXaPqsPgcZdgLiQu9XfA==/109951164205007495.jpg","format":"jpg"},{"width":560,"height":793,"originUrl":"https://p1.music.126.net/OTs8ascSl6KMR1XCbwQ2vg==/109951164205016153.jpg","squareUrl":"https://p1.music.126.net/J8J4YO82bsabUihKizmFmA==/109951164205007996.jpg","rectangleUrl":"https://p1.music.126.net/W042Z33HUwlp98MqV1R0Fw==/109951164205014173.jpg","pcSquareUrl":"https://p1.music.126.net/cJAzJu1kX2rj71c9l1l3lA==/109951164205009419.jpg","pcRectangleUrl":"https://p1.music.126.net/RTwj1_gjU6qdb4Vs7JC_vw==/109951164205008973.jpg","format":"jpg"},{"width":798,"height":635,"originUrl":"https://p1.music.126.net/vS5xKiI2ocauyMT_2ulMrQ==/109951164205010785.jpg","squareUrl":"https://p1.music.126.net/2hLSOWfB5eDJ4mj28pCtrw==/109951164205014731.jpg","rectangleUrl":"https://p1.music.126.net/WBgdnR7yhh4FkNrz2koTtQ==/109951164205017631.jpg","pcSquareUrl":"https://p1.music.126.net/0K11NdI7vFNsHYeHh-qv3w==/109951164205008976.jpg","pcRectangleUrl":"https://p1.music.126.net/DvPq6w6OMaYl_xiM-cAr1Q==/109951164205010370.jpg","format":"jpg"},{"width":960,"height":640,"originUrl":"https://p1.music.126.net/4vWh8WjCU8Hc_C6w3URZcg==/109951164205019545.jpg","squareUrl":"https://p1.music.126.net/9s-Q1yv3fltvVaMoMEiylw==/109951164205010371.jpg","rectangleUrl":"https://p1.music.126.net/jaMiYmgiO9wD2kYmTJ4ANQ==/109951164205016666.jpg","pcSquareUrl":"https://p1.music.126.net/dzdHRV7nOdW7Gu3gv1d5Nw==/109951164205009916.jpg","pcRectangleUrl":"https://p1.music.126.net/jiaoXPW6bt2NgLr5hR2m0w==/109951164205017634.jpg","format":"jpg"},{"width":436,"height":306,"originUrl":"https://p1.music.126.net/tvJvZ7tNckDLN8E-ZsqMvA==/109951164205018111.jpg","squareUrl":"https://p1.music.126.net/wMX2JzxgYykkMUL0-fjI3g==/109951164205012739.jpg","rectangleUrl":"https://p1.music.126.net/zJuct5f8QbHlXCkb6aOqPA==/109951164205018584.jpg","pcSquareUrl":"https://p1.music.126.net/N61OLlsSXiOQF64dmTn10w==/109951164205017636.jpg","pcRectangleUrl":"https://p1.music.126.net/h2DRtUjDZco0btmu5uz56Q==/109951164205010373.jpg","format":"jpg"},{"width":649,"height":376,"originUrl":"https://p1.music.126.net/7lPvvfhWqJzY7mXQULrY6w==/109951164205016161.jpg","squareUrl":"https://p1.music.126.net/bGggc2sghj2kCxBm0N4fnQ==/109951164205016669.jpg","rectangleUrl":"https://p1.music.126.net/NMydE0akVC4318HBz_EAuQ==/109951164205008462.jpg","pcSquareUrl":"https://p1.music.126.net/qrh4da4_vJS-tM3kugk6UQ==/109951164205020541.jpg","pcRectangleUrl":"https://p1.music.126.net/kYjdw_YNCp6wQYrJA3Igbg==/109951164205008463.jpg","format":"jpg"},{"width":900,"height":1200,"originUrl":"https://p1.music.126.net/qDvh-uALAoTkVx8-lujNSA==/109951164205013227.jpg","squareUrl":"https://p1.music.126.net/oXKAjOV9wkRfRD_AjFtOfA==/109951164205015189.jpg","rectangleUrl":"https://p1.music.126.net/62wO_US0VV36VU78VQsIqQ==/109951164205012741.jpg","pcSquareUrl":"https://p1.music.126.net/7wRuM5r5HNqzU3uZNS25qQ==/109951164205016164.jpg","pcRectangleUrl":"https://p1.music.126.net/xnyHdqU5a6OLAQZsh_9YgA==/109951164205010378.jpg","format":"jpg"},{"width":1920,"height":1079,"originUrl":"https://p1.music.126.net/CbIsJmSvxDPhnvZXtlyJYg==/109951164205014192.jpg","squareUrl":"https://p1.music.126.net/A5inFiJmf4nhEj2tFPFjlA==/109951164205011774.jpg","rectangleUrl":"https://p1.music.126.net/Nux7CKRVK7XE_2mpwSG8aA==/109951164205010797.jpg","pcSquareUrl":"https://p1.music.126.net/nFT-iGGqLHFKQYrs7-NoeA==/109951164205011337.jpg","pcRectangleUrl":"https://p1.music.126.net/FmvCh3umZSjI9gqO6zEHjQ==/109951164205019554.jpg","format":"jpg"}]
         * showTime : *************
         * tmplId : 0
         * id : **********
         * type : 18
         * topEvent : false
         * insiteForwardCount : 3
         * info : {"commentThread":{"id":"A_EV_2_**********_*********","resourceInfo":{"id":**********,"userId":*********,"name":"分享单曲：「Legend of Galaxy ~銀河の覇者 (特摄《银河奥特曼特别剧场版》片头曲 )」","imgUrl":null,"creator":null,"eventType":18},"resourceType":2,"commentCount":13,"likedCount":81,"shareCount":3,"hotCount":6,"latestLikedUsers":[{"s":125480200,"t":1563778997011},{"s":562373716,"t":1563699815075},{"s":136573487,"t":1563631631945},{"s":255876719,"t":1563595442198},{"s":253528058,"t":1563594284815},{"s":1316129489,"t":1563495689051},{"s":1628836123,"t":1563438018553},{"s":135427249,"t":1563416164439},{"s":1352058031,"t":1563359988652},{"s":1772445458,"t":1563357333159}],"resourceOwnerId":*********,"resourceTitle":"分享单曲：「Legend of Galaxy ~銀河の覇者 (特摄《银河奥特曼特别剧场版》片头曲 )」","resourceId":**********},"latestLikedUsers":null,"liked":false,"comments":null,"resourceType":2,"resourceId":**********,"shareCount":3,"commentCount":13,"likedCount":81,"threadId":"A_EV_2_**********_*********"}
         */

        private Object actName;
        private int forwardCount;
        private Object lotteryEventData;
        private UserBean user;
        private long eventTime;
        private String json;
        private long expireTime;
        private Object uuid;
        private Object rcmdInfo;
        private long actId;
        private long showTime;
        private long tmplId;
        private long id;
        private int type;
        private boolean topEvent;
        private long insiteForwardCount;
        private InfoBean info;
        private List<PicsBean> pics;

        public Object getActName() {
            return actName;
        }

        public void setActName(Object actName) {
            this.actName = actName;
        }

        public int getForwardCount() {
            return forwardCount;
        }

        public void setForwardCount(int forwardCount) {
            this.forwardCount = forwardCount;
        }

        public Object getLotteryEventData() {
            return lotteryEventData;
        }

        public void setLotteryEventData(Object lotteryEventData) {
            this.lotteryEventData = lotteryEventData;
        }

        public UserBean getUser() {
            return user;
        }

        public void setUser(UserBean user) {
            this.user = user;
        }

        public long getEventTime() {
            return eventTime;
        }

        public void setEventTime(long eventTime) {
            this.eventTime = eventTime;
        }

        public String getJson() {
            return json;
        }

        public void setJson(String json) {
            this.json = json;
        }

        public long getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(long expireTime) {
            this.expireTime = expireTime;
        }

        public Object getUuid() {
            return uuid;
        }

        public void setUuid(Object uuid) {
            this.uuid = uuid;
        }

        public Object getRcmdInfo() {
            return rcmdInfo;
        }

        public void setRcmdInfo(Object rcmdInfo) {
            this.rcmdInfo = rcmdInfo;
        }

        public long getActId() {
            return actId;
        }

        public void setActId(long actId) {
            this.actId = actId;
        }

        public long getShowTime() {
            return showTime;
        }

        public void setShowTime(long showTime) {
            this.showTime = showTime;
        }

        public long getTmplId() {
            return tmplId;
        }

        public void setTmplId(long tmplId) {
            this.tmplId = tmplId;
        }

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public boolean isTopEvent() {
            return topEvent;
        }

        public void setTopEvent(boolean topEvent) {
            this.topEvent = topEvent;
        }

        public long getInsiteForwardCount() {
            return insiteForwardCount;
        }

        public void setInsiteForwardCount(long insiteForwardCount) {
            this.insiteForwardCount = insiteForwardCount;
        }

        public InfoBean getInfo() {
            return info;
        }

        public void setInfo(InfoBean info) {
            this.info = info;
        }

        public List<PicsBean> getPics() {
            return pics;
        }

        public void setPics(List<PicsBean> pics) {
            this.pics = pics;
        }

        public static class UserBean {
            /**
             * defaultAvatar : false
             * province : 1000000
             * authStatus : 0
             * followed : false
             * avatarUrl : http://p1.music.126.net/P_GOWHWAqFIjN6oOqYsdbw==/109951164225662722.jpg
             * accountStatus : 0
             * gender : 1
             * city : 1005500
             * birthday : ************
             * userId : *********
             * userType : 0
             * name : 璀璨的幻夢星光
             * signature : 一个喜欢拟人少女和皮套人的自闭+中二的令和废宅(:3[____]
             * description :
             * detailDescription :
             * avatarImgId : 109951164225662720
             * backgroundImgId : 109951163528584620
             * backgroundUrl : http://p1.music.126.net/jcLv8q71lkIptIVmnEFnMw==/109951163528584619.jpg
             * authority : 0
             * mutual : false
             * expertTags : null
             * experts : null
             * djStatus : 10
             * vipType : 0
             * remarkName : null
             * backgroundImgIdStr : 109951163528584619
             * avatarImgIdStr : 109951164225662722
             * urlAnalyze : false
             * avatarImgId_str : 109951164225662722
             * followeds : 5226
             */

            private boolean defaultAvatar;
            private int province;
            private int authStatus;
            private boolean followed;
            private String avatarUrl;
            private int accountStatus;
            private int gender;
            private int city;
            private long birthday;
            private String userId;
            private int userType;
            private String nickname;
            private String signature;
            private String description;
            private String detailDescription;
            private long avatarImgId;
            private long backgroundImgId;
            private String backgroundUrl;
            private int authority;
            private boolean mutual;
            private Object expertTags;
            private Object experts;
            private int djStatus;
            private int vipType;
            private Object remarkName;
            private String backgroundImgIdStr;
            private String avatarImgIdStr;
            private boolean urlAnalyze;
            private String avatarImgId_str;
            private int followeds;

            public boolean isDefaultAvatar() {
                return defaultAvatar;
            }

            public void setDefaultAvatar(boolean defaultAvatar) {
                this.defaultAvatar = defaultAvatar;
            }

            public int getProvince() {
                return province;
            }

            public void setProvince(int province) {
                this.province = province;
            }

            public int getAuthStatus() {
                return authStatus;
            }

            public void setAuthStatus(int authStatus) {
                this.authStatus = authStatus;
            }

            public boolean isFollowed() {
                return followed;
            }

            public void setFollowed(boolean followed) {
                this.followed = followed;
            }

            public String getAvatarUrl() {
                return avatarUrl;
            }

            public void setAvatarUrl(String avatarUrl) {
                this.avatarUrl = avatarUrl;
            }

            public int getAccountStatus() {
                return accountStatus;
            }

            public void setAccountStatus(int accountStatus) {
                this.accountStatus = accountStatus;
            }

            public int getGender() {
                return gender;
            }

            public void setGender(int gender) {
                this.gender = gender;
            }

            public int getCity() {
                return city;
            }

            public void setCity(int city) {
                this.city = city;
            }

            public long getBirthday() {
                return birthday;
            }

            public void setBirthday(long birthday) {
                this.birthday = birthday;
            }

            public String getUserId() {
                return userId;
            }

            public void setUserId(String userId) {
                this.userId = userId;
            }

            public int getUserType() {
                return userType;
            }

            public void setUserType(int userType) {
                this.userType = userType;
            }

            public String getNickname() {
                return nickname;
            }

            public void setNickname(String nickname) {
                this.nickname = nickname;
            }

            public String getSignature() {
                return signature;
            }

            public void setSignature(String signature) {
                this.signature = signature;
            }

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public String getDetailDescription() {
                return detailDescription;
            }

            public void setDetailDescription(String detailDescription) {
                this.detailDescription = detailDescription;
            }

            public long getAvatarImgId() {
                return avatarImgId;
            }

            public void setAvatarImgId(long avatarImgId) {
                this.avatarImgId = avatarImgId;
            }

            public long getBackgroundImgId() {
                return backgroundImgId;
            }

            public void setBackgroundImgId(long backgroundImgId) {
                this.backgroundImgId = backgroundImgId;
            }

            public String getBackgroundUrl() {
                return backgroundUrl;
            }

            public void setBackgroundUrl(String backgroundUrl) {
                this.backgroundUrl = backgroundUrl;
            }

            public int getAuthority() {
                return authority;
            }

            public void setAuthority(int authority) {
                this.authority = authority;
            }

            public boolean isMutual() {
                return mutual;
            }

            public void setMutual(boolean mutual) {
                this.mutual = mutual;
            }

            public Object getExpertTags() {
                return expertTags;
            }

            public void setExpertTags(Object expertTags) {
                this.expertTags = expertTags;
            }

            public Object getExperts() {
                return experts;
            }

            public void setExperts(Object experts) {
                this.experts = experts;
            }

            public int getDjStatus() {
                return djStatus;
            }

            public void setDjStatus(int djStatus) {
                this.djStatus = djStatus;
            }

            public int getVipType() {
                return vipType;
            }

            public void setVipType(int vipType) {
                this.vipType = vipType;
            }

            public Object getRemarkName() {
                return remarkName;
            }

            public void setRemarkName(Object remarkName) {
                this.remarkName = remarkName;
            }

            public String getBackgroundImgIdStr() {
                return backgroundImgIdStr;
            }

            public void setBackgroundImgIdStr(String backgroundImgIdStr) {
                this.backgroundImgIdStr = backgroundImgIdStr;
            }

            public String getAvatarImgIdStr() {
                return avatarImgIdStr;
            }

            public void setAvatarImgIdStr(String avatarImgIdStr) {
                this.avatarImgIdStr = avatarImgIdStr;
            }

            public boolean isUrlAnalyze() {
                return urlAnalyze;
            }

            public void setUrlAnalyze(boolean urlAnalyze) {
                this.urlAnalyze = urlAnalyze;
            }

            public String getAvatarImgId_str() {
                return avatarImgId_str;
            }

            public void setAvatarImgId_str(String avatarImgId_str) {
                this.avatarImgId_str = avatarImgId_str;
            }

            public int getFolloweds() {
                return followeds;
            }

            public void setFolloweds(int followeds) {
                this.followeds = followeds;
            }
        }

        public static class InfoBean {
            /**
             * commentThread : {"id":"A_EV_2_**********_*********","resourceInfo":{"id":**********,"userId":*********,"name":"分享单曲：「Legend of Galaxy ~銀河の覇者 (特摄《银河奥特曼特别剧场版》片头曲 )」","imgUrl":null,"creator":null,"eventType":18},"resourceType":2,"commentCount":13,"likedCount":81,"shareCount":3,"hotCount":6,"latestLikedUsers":[{"s":125480200,"t":1563778997011},{"s":562373716,"t":1563699815075},{"s":136573487,"t":1563631631945},{"s":255876719,"t":1563595442198},{"s":253528058,"t":1563594284815},{"s":1316129489,"t":1563495689051},{"s":1628836123,"t":1563438018553},{"s":135427249,"t":1563416164439},{"s":1352058031,"t":1563359988652},{"s":1772445458,"t":1563357333159}],"resourceOwnerId":*********,"resourceTitle":"分享单曲：「Legend of Galaxy ~銀河の覇者 (特摄《银河奥特曼特别剧场版》片头曲 )」","resourceId":**********}
             * latestLikedUsers : null
             * liked : false
             * comments : null
             * resourceType : 2
             * resourceId : **********
             * shareCount : 3
             * commentCount : 13
             * likedCount : 81
             * threadId : A_EV_2_**********_*********
             */

            private CommentThreadBean commentThread;
            private Object latestLikedUsers;
            private boolean liked;
            private Object comments;
            private int resourceType;
            private long resourceId;
            private int shareCount;
            private int commentCount;
            private int likedCount;
            private String threadId;

            public CommentThreadBean getCommentThread() {
                return commentThread;
            }

            public void setCommentThread(CommentThreadBean commentThread) {
                this.commentThread = commentThread;
            }

            public Object getLatestLikedUsers() {
                return latestLikedUsers;
            }

            public void setLatestLikedUsers(Object latestLikedUsers) {
                this.latestLikedUsers = latestLikedUsers;
            }

            public boolean isLiked() {
                return liked;
            }

            public void setLiked(boolean liked) {
                this.liked = liked;
            }

            public Object getComments() {
                return comments;
            }

            public void setComments(Object comments) {
                this.comments = comments;
            }

            public int getResourceType() {
                return resourceType;
            }

            public void setResourceType(int resourceType) {
                this.resourceType = resourceType;
            }

            public long getResourceId() {
                return resourceId;
            }

            public void setResourceId(long resourceId) {
                this.resourceId = resourceId;
            }

            public int getShareCount() {
                return shareCount;
            }

            public void setShareCount(int shareCount) {
                this.shareCount = shareCount;
            }

            public int getCommentCount() {
                return commentCount;
            }

            public void setCommentCount(int commentCount) {
                this.commentCount = commentCount;
            }

            public int getLikedCount() {
                return likedCount;
            }

            public void setLikedCount(int likedCount) {
                this.likedCount = likedCount;
            }

            public String getThreadId() {
                return threadId;
            }

            public void setThreadId(String threadId) {
                this.threadId = threadId;
            }

            public static class CommentThreadBean {
                /**
                 * id : A_EV_2_**********_*********
                 * resourceInfo : {"id":**********,"userId":*********,"name":"分享单曲：「Legend of Galaxy ~銀河の覇者 (特摄《银河奥特曼特别剧场版》片头曲 )」","imgUrl":null,"creator":null,"eventType":18}
                 * resourceType : 2
                 * commentCount : 13
                 * likedCount : 81
                 * shareCount : 3
                 * hotCount : 6
                 * latestLikedUsers : [{"s":125480200,"t":1563778997011},{"s":562373716,"t":1563699815075},{"s":136573487,"t":1563631631945},{"s":255876719,"t":1563595442198},{"s":253528058,"t":1563594284815},{"s":1316129489,"t":1563495689051},{"s":1628836123,"t":1563438018553},{"s":135427249,"t":1563416164439},{"s":1352058031,"t":1563359988652},{"s":1772445458,"t":1563357333159}]
                 * resourceOwnerId : *********
                 * resourceTitle : 分享单曲：「Legend of Galaxy ~銀河の覇者 (特摄《银河奥特曼特别剧场版》片头曲 )」
                 * resourceId : **********
                 */

                private String id;
                private ResourceInfoBean resourceInfo;
                private int resourceType;
                private int commentCount;
                private int likedCount;
                private int shareCount;
                private int hotCount;
                private int resourceOwnerId;
                private String resourceTitle;
                private long resourceId;
                private List<LatestLikedUsersBean> latestLikedUsers;

                public String getId() {
                    return id;
                }

                public void setId(String id) {
                    this.id = id;
                }

                public ResourceInfoBean getResourceInfo() {
                    return resourceInfo;
                }

                public void setResourceInfo(ResourceInfoBean resourceInfo) {
                    this.resourceInfo = resourceInfo;
                }

                public int getResourceType() {
                    return resourceType;
                }

                public void setResourceType(int resourceType) {
                    this.resourceType = resourceType;
                }

                public int getCommentCount() {
                    return commentCount;
                }

                public void setCommentCount(int commentCount) {
                    this.commentCount = commentCount;
                }

                public int getLikedCount() {
                    return likedCount;
                }

                public void setLikedCount(int likedCount) {
                    this.likedCount = likedCount;
                }

                public int getShareCount() {
                    return shareCount;
                }

                public void setShareCount(int shareCount) {
                    this.shareCount = shareCount;
                }

                public int getHotCount() {
                    return hotCount;
                }

                public void setHotCount(int hotCount) {
                    this.hotCount = hotCount;
                }

                public int getResourceOwnerId() {
                    return resourceOwnerId;
                }

                public void setResourceOwnerId(int resourceOwnerId) {
                    this.resourceOwnerId = resourceOwnerId;
                }

                public String getResourceTitle() {
                    return resourceTitle;
                }

                public void setResourceTitle(String resourceTitle) {
                    this.resourceTitle = resourceTitle;
                }

                public long getResourceId() {
                    return resourceId;
                }

                public void setResourceId(long resourceId) {
                    this.resourceId = resourceId;
                }

                public List<LatestLikedUsersBean> getLatestLikedUsers() {
                    return latestLikedUsers;
                }

                public void setLatestLikedUsers(List<LatestLikedUsersBean> latestLikedUsers) {
                    this.latestLikedUsers = latestLikedUsers;
                }

                public static class ResourceInfoBean {
                    /**
                     * id : **********
                     * userId : *********
                     * name : 分享单曲：「Legend of Galaxy ~銀河の覇者 (特摄《银河奥特曼特别剧场版》片头曲 )」
                     * imgUrl : null
                     * creator : null
                     * eventType : 18
                     */

                    private long id;
                    private int userId;
                    private String name;
                    private Object imgUrl;
                    private Object creator;
                    private int eventType;

                    public long getId() {
                        return id;
                    }

                    public void setId(long id) {
                        this.id = id;
                    }

                    public int getUserId() {
                        return userId;
                    }

                    public void setUserId(int userId) {
                        this.userId = userId;
                    }

                    public String getName() {
                        return name;
                    }

                    public void setName(String name) {
                        this.name = name;
                    }

                    public Object getImgUrl() {
                        return imgUrl;
                    }

                    public void setImgUrl(Object imgUrl) {
                        this.imgUrl = imgUrl;
                    }

                    public Object getCreator() {
                        return creator;
                    }

                    public void setCreator(Object creator) {
                        this.creator = creator;
                    }

                    public int getEventType() {
                        return eventType;
                    }

                    public void setEventType(int eventType) {
                        this.eventType = eventType;
                    }
                }

                public static class LatestLikedUsersBean {
                    /**
                     * s : 125480200
                     * t : 1563778997011
                     */

                    private String s;
                    private long t;

                    public String getS() {
                        return s;
                    }

                    public void setS(String s) {
                        this.s = s;
                    }

                    public long getT() {
                        return t;
                    }

                    public void setT(long t) {
                        this.t = t;
                    }
                }
            }
        }

        public static class PicsBean {
            /**
             * width : 560
             * height : 792
             * originUrl : https://p1.music.126.net/JidENtRyj8PdSK3kYn8shQ==/109951164205011321.jpg
             * squareUrl : https://p1.music.126.net/qu3jn-FjIQ4RlPey7b0kSw==/109951164205013214.jpg
             * rectangleUrl : https://p1.music.126.net/tPv7c0rcNov9epWEqlZJDA==/109951164205016150.jpg
             * pcSquareUrl : https://p1.music.126.net/SsjDOgM_kCcbzBqDBTo28Q==/109951164205012730.jpg
             * pcRectangleUrl : https://p1.music.126.net/faNh4O7vHSxKM3alojw7VA==/109951164205017123.jpg
             * format : jpg
             */

            private int width;
            private int height;
            private String originUrl;
            private String squareUrl;
            private String rectangleUrl;
            private String pcSquareUrl;
            private String pcRectangleUrl;
            private String format;

            public int getWidth() {
                return width;
            }

            public void setWidth(int width) {
                this.width = width;
            }

            public int getHeight() {
                return height;
            }

            public void setHeight(int height) {
                this.height = height;
            }

            public String getOriginUrl() {
                return originUrl;
            }

            public void setOriginUrl(String originUrl) {
                this.originUrl = originUrl;
            }

            public String getSquareUrl() {
                return squareUrl;
            }

            public void setSquareUrl(String squareUrl) {
                this.squareUrl = squareUrl;
            }

            public String getRectangleUrl() {
                return rectangleUrl;
            }

            public void setRectangleUrl(String rectangleUrl) {
                this.rectangleUrl = rectangleUrl;
            }

            public String getPcSquareUrl() {
                return pcSquareUrl;
            }

            public void setPcSquareUrl(String pcSquareUrl) {
                this.pcSquareUrl = pcSquareUrl;
            }

            public String getPcRectangleUrl() {
                return pcRectangleUrl;
            }

            public void setPcRectangleUrl(String pcRectangleUrl) {
                this.pcRectangleUrl = pcRectangleUrl;
            }

            public String getFormat() {
                return format;
            }

            public void setFormat(String format) {
                this.format = format;
            }
        }

        @Override
        public String toString() {
            return "EventsBean{" +
                    "actName=" + actName +
                    ", forwardCount=" + forwardCount +
                    ", lotteryEventData=" + lotteryEventData +
                    ", user=" + user +
                    ", eventTime=" + eventTime +
                    ", json='" + json + '\'' +
                    ", expireTime=" + expireTime +
                    ", uuid=" + uuid +
                    ", rcmdInfo=" + rcmdInfo +
                    ", actId=" + actId +
                    ", showTime=" + showTime +
                    ", tmplId=" + tmplId +
                    ", id=" + id +
                    ", type=" + type +
                    ", topEvent=" + topEvent +
                    ", insiteForwardCount=" + insiteForwardCount +
                    ", info=" + info +
                    ", pics=" + pics +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "UserEventBean{" +
                "lasttime=" + lasttime +
                ", more=" + more +
                ", size=" + size +
                ", code=" + code +
                ", events=" + events +
                '}';
    }
}
