package com.imooc.lib_api.model.dj;

import java.util.List;

public class DjCategoryRecommendBean {

    /**
     * code : 200
     * msg : null
     * data : [{"categoryId":2001,"categoryName":"创作|翻唱","radios":[{"id":792424363,"name":"FAFA的电台","rcmdText":"只想唱歌给你听","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/8rKCtMIkRPZIW4nHX21M9A==/109951164355969490.jpg","programCount":55,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"不得不爱"},{"id":527394590,"name":"爱抽烟屁的彦祖","rcmdText":"让小哥哥的迷人低音炮治愈你","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/7fsiPQ8f3MfJVprSeaPbmg==/109951163391639863.jpg","programCount":44,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"年轮说"},{"id":791718404,"name":"是某猪呀","rcmdText":"心情不好的时候，听他唱歌就对了","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/28iEiuwnYvKMG9Tn4vUsJw==/109951163535930582.jpg","programCount":90,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"南屏晚钟（cover）"}]},{"categoryId":10002,"categoryName":"3D|电子","radios":[{"id":791842381,"name":"DJAbduMiJiT | Remix |","rcmdText":"DJAbduMiJiT官方电台","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/Rp9_0jw_2PP0z2F08zBS7A==/109951164386274540.jpg","programCount":165,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"Leylim Bolgin"},{"id":1899014,"name":"3D环绕，刺激每一个听神经♪","rcmdText":"带你体验更加性感真实的的声音","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/ILz3J2f8G6ER1uD0-_wK2w==/3391993374482773.jpg","programCount":365,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"【Cydian】Tribute"},{"id":341609066,"name":"PurpleBattery's VVIP Session","rcmdText":"VVIP Session官方电台","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/xTfw_Q7oqG56n-D1dNpj0g==/109951164306151507.jpg","programCount":42,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"VVIP Session 042: Guest Kozoro"}]},{"categoryId":3,"categoryName":"情感调频","radios":[{"id":101009,"name":"程一电台","rcmdText":"夜晚的声音会发光","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/YfsRFHpQZv2FyXK7RRHnQw==/109951163894320715.jpg","programCount":1087,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"没在一起的，都是错的人。"},{"id":526835598,"name":"大白电台❤","rcmdText":"陪你度过难熬的日子 愿你睡安心","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/CqY_3i2xrLJxcY1knWJ-ZQ==/109951164134175221.jpg","programCount":92,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"读书回放《我们内心的冲突》"},{"id":792169404,"name":"南木大叔","rcmdText":"放下防备，治愈你的不安与疲惫","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/uLQr1yCfmXk7NVBjbl0Ucw==/109951163684952890.jpg","programCount":78,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"一个女人老去的标志"}]},{"categoryId":2,"categoryName":"音乐故事","radios":[{"id":793339521,"name":"刺猬电台HBe","rcmdText":"听最潮最酷的rapper\u201c说真话\u201d","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/A14EGdE1eEu_81K-E1u9oA==/109951164244504931.jpg","programCount":32,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"capper在线模仿新说唱各位rapper 冷笑话西安话freestyle轮番秀"},{"id":644019,"name":"陪你听一首歌","rcmdText":"每晚陪你听完一首歌","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/Ywi1dH30EPixc_ANskbe6A==/7791139394581828.jpg","programCount":139,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"他乡故乡"},{"id":349592352,"name":"犀利先生 舞动人生","rcmdText":"犀利先生带你舞动人生","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/cGd5qJvPc4Uq36Ml4zn6kg==/19224960812304395.jpg","programCount":605,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"乐山金丝木小龙赠送宜宾中都阿超兄弟(Mr.Sharp Set.)"}]},{"categoryId":3001,"categoryName":"二次元","radios":[{"id":272,"name":"一万光年动漫电台","rcmdText":"听得见的有声动画","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/yVYhOt6u1ujJjNICYrRJ9Q==/3265549569907044.jpg","programCount":217,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"【有声-魁拔同人】帝国边缘第十五集（vol.217)"},{"id":792166506,"name":"佳佳小狮子的元气屋","rcmdText":"萝莉、御姐、元气少女一键拥有","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/xP6GZLJFWTloJpLWFdv5hw==/109951163692714617.jpg","programCount":11,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"【声优A计划决赛作品2】漫画《尚善》配音"},{"id":1017,"name":"凌霄广播剧团","rcmdText":"听剧听歌听八卦 欢迎洗耳恭听","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/nrAWpU4stMnPs3QM0RPokA==/2531075768963502.jpg","programCount":108,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"【凌霄剧团】万里雪歌.第三期.全年龄.广播剧"}]},{"categoryId":10001,"categoryName":"有声书","radios":[{"id":350766052,"name":"千古枭雄朱元璋","rcmdText":"既有人性又有狼性的千古袅雄","radioFeeType":1,"feeScope":0,"picUrl":"http://p2.music.126.net/igLE9bSBjK1ScK4Fpnzqdg==/109951163059147126.jpg","programCount":102,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"千古枭雄朱元璋103（完结）"},{"id":526870589,"name":"LOL之英雄战纪","rcmdText":"神探苍首度发声演绎英雄传奇","radioFeeType":1,"feeScope":0,"picUrl":"http://p2.music.126.net/qMs7qpobH7YbjnbTZRiZfw==/109951163209867341.jpg","programCount":265,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"【伊泽瑞尔篇72】傲慢的偶像"},{"id":349487098,"name":"闲得没事儿读书","rcmdText":"闲得没事儿读书\n","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/AIt4QHlCv-SVJNxeK4KqwQ==/18898405858341011.jpg","programCount":81,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"怎么修摩托车"}]},{"categoryId":7,"categoryName":"广播剧","radios":[{"id":340240066,"name":"阑语天成配音社","rcmdText":"精品原创短片广播剧集合","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/Mk3ZDy-BOGRTuevP_fIZAQ==/18797250790209188.jpg","programCount":38,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"【阑语天成】《闹喜》第二期ED《所困》"},{"id":7400018,"name":"尔说网络电台（【橘生淮南·暗恋】 广播剧 授权电台）","rcmdText":"关于暗恋的青春故事","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/7qPY_a-oUXeCoWlFRjpvuQ==/1364493979927807.jpg","programCount":18,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"【橘生淮南·暗恋】广播剧 第七期（下）"},{"id":347399093,"name":"咖啡不加糖也甜","rcmdText":"古风武侠故事","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/R92J0R12enZwgper-Lbr2g==/19029247742552094.jpg","programCount":65,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"【填翻】明月不照还-林曳"}]},{"categoryId":6,"categoryName":"美文读物","radios":[{"id":347903064,"name":"边江de恋声馆","rcmdText":"配音演员边江静静地读给你听","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/yxPVKhnPau3Y81d85QoyfA==/18557557255384267.jpg","programCount":19,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"学会享受"},{"id":342359051,"name":"夜半叔声","rcmdText":"走心的文字与暖心的声音伴你入眠","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/_tSl2seTZuH-wFg_b853uQ==/18597139674134424.jpg","programCount":487,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"成年人之间最好的社交状态\u2026\u2026"},{"id":526248576,"name":"Bobo的悦读时光","rcmdText":"好声音为你分享好书好文好音乐","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/fsatWq97rJR4hXBOWZmEXg==/109951163600454272.jpg","programCount":388,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"那时我们有那么多梦想"}]},{"categoryId":8,"categoryName":"相声曲艺","radios":[{"id":569027,"name":"拾味电台：中华戏曲","rcmdText":"国粹经典：全本戏曲欣赏\t","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/bKLatc-NgbRuWXGkeZPdDQ==/19242552997768794.jpg","programCount":102,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"【河南坠子】《吹牛》-王文仲"},{"id":350245083,"name":"嘻哈壹笑堂","rcmdText":"坏心情治愈良方","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/xg6n5lQEbEStfylaQfGnhA==/18520173860273527.jpg","programCount":43,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"四平利平张解《南弹北弦》"},{"id":349580258,"name":"中国古典音乐和戏剧","rcmdText":"中国古典音乐和戏剧欣赏\t","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/MisfZDgfuXfWeJ7_k3H9YA==/18780758115918630.jpg","programCount":23,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"第二十一期 虞美人·秋雾莲池＋天宫＋游园惊梦＋菊花醉"}]},{"categoryId":11,"categoryName":"人文历史","radios":[{"id":526929671,"name":"惟物论FM","rcmdText":"一人，一物，一故事，一名人","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/l2YyBw_wGi7fHKdoIDSGiA==/109951164415929765.jpg","programCount":80,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"惟物论079~严明：艺术的表达，不是复制与粘贴。"},{"id":7540004,"name":"黎安|安姑娘的花房","rcmdText":"漫长黑夜，一盏暖灯陪你入眠","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/LCk0BF02MXgc9uFyUNJGUw==/19090820393705484.jpg","programCount":10,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"我和我的祖国/群体记忆是值得贩卖的"},{"id":526745578,"name":"网易声音图书馆","rcmdText":"动听的声音有温度","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/UtuIv04N9kNdahFmpYuCAg==/109951163783626171.jpg","programCount":61,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"【特辑】跟白百何、简媜一起去七堇年的私人博物馆看春远秋长"}]},{"categoryId":5,"categoryName":"脱口秀","radios":[{"id":1291002,"name":"优斯迪吧USDB","rcmdText":"满足你对播客的美好向往","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/QYByRiMLSIsLDnWD6K3Dqw==/2908208255945367.jpg","programCount":188,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"Kick My 囊踹 - 优斯迪吧 vol.193（试听）"},{"id":526655665,"name":"别的电波","rcmdText":"拯救无聊现实！","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/wgAcKlKpO4kc7j9t7szJKg==/109951163463498644.jpg","programCount":94,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"Vol.95 争 当 乐 评 人【4】"},{"id":471025,"name":"二货一箩筐","rcmdText":"二货一箩筐，糗事集装箱\t","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/Y5whi2HR1bi17loJeNFg9g==/3296335868538454.jpg","programCount":2130,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"鸡笼原先有六只鸡"}]},{"categoryId":4,"categoryName":"娱乐|影视","radios":[{"id":350082183,"name":"半斤八两抡电影","rcmdText":"无节操有逼格的影片吐槽","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/dS7EakZ4_whTyr6cc1OMdg==/18500382650952493.jpg","programCount":198,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"Vol.195 《祖与占》的无需选择\u2014\u2014特吕弗导演专题3"},{"id":792127533,"name":"Adam Radio_范丞丞0616","rcmdText":"丞星集聚地，范丞丞官方粉丝电台","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/EQhgWjrFqNLdRGnMOiBQzA==/109951164150899241.jpg","programCount":11,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"Vol.11【中秋快乐】你的名字，我们的故事"},{"id":791836370,"name":"黄明昊的电台情书","rcmdText":"Justin17岁生日快乐！","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/UkC8JF4tM3CKEaIQkmqFlw==/109951163655090453.jpg","programCount":7,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"【第七期】十八岁的留声机"}]},{"categoryId":13,"categoryName":"外语世界","radios":[{"id":259020,"name":"英语美文朗读","rcmdText":"","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/wwEMwO-YIVD9qoBAkSndgQ==/1422768054051074.jpg","programCount":244,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"英语美文朗读《我们都是渴望被关注的孩子》"},{"id":2091019,"name":"日语里丨零基础学日语","rcmdText":"小白也可以听的日语课","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/u3m7S14hTlcITl-7XQ4uyw==/109951164187674785.jpg","programCount":79,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"078日语朗读：卒業前日（Ⅱ）"},{"id":334619056,"name":"为你读英语美文","rcmdText":"在浮躁和忙碌中获得宁静和美好","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/UUtEP0jHJonOKp7PiCp6dQ==/1374389544240928.jpg","programCount":468,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"鲁滨逊漂流记 Robinson Crusoe"}]},{"categoryId":453050,"categoryName":"知识技能","radios":[{"id":334040058,"name":"营销方法论","rcmdText":"传播最新、实用的营销方法和策略","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/Q246nN72RM5MxNDJwDwMqg==/109951164030610897.jpg","programCount":76,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"原来医生也需要用户画像"},{"id":1292021,"name":"野史","rcmdText":"不为人熟知的小道历史","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/GazgWMVlnhqoVjY8uNeW1w==/109951163923167734.jpg","programCount":104,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"亮堂堂的中国"},{"id":5537031,"name":"书店的灯光","rcmdText":"一盏灯光照亮你的书房","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/2vwZb3kWtW6bRSMLUD0WFQ==/109951163276315431.jpg","programCount":43,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"\u201c在死亡和生活之间\u201d"}]},{"categoryId":14,"categoryName":"亲子宝贝","radios":[{"id":344997051,"name":"婷婷唱古文","rcmdText":"把古诗唱成歌，江湖人称\u201c背诗神器\u201d","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/Z7QiPj365_ezk7D27vgwlg==/18662010859616535.jpg","programCount":149,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"一句叹息打动千古多少人？《乐游原》- 李商隐"},{"id":333056061,"name":"甄姐姐讲故事","rcmdText":"宝宝的故事机，妈妈的好帮手","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/x6FOIyQdsdPJNY0m6wQtbw==/3265549601648767.jpg","programCount":644,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"《丢车的卡车司机》"},{"id":337612052,"name":"经典绘本故事","rcmdText":"国外优秀绘本选辑","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/XbVpucbUt60WLgU1q7y-2w==/109951163251897822.jpg","programCount":259,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"皇子（下）"}]},{"categoryId":4001,"categoryName":"校园|教育","radios":[{"id":344875085,"name":"人大之声·中国人民大学广播台","rcmdText":"新学期人大之声，继续为你发声","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/-S1IuLJjE-ZnRSC6zlNNxg==/18760966906188818.jpg","programCount":228,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"【turn up the music】影视音像馆"},{"id":1100014,"name":"大学在路上","rcmdText":"北方妹子的南方大学生活","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/6c-eSyfN5TQFglm0xGDqIA==/18787355185534562.jpg","programCount":111,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"vol108.我应该在小城市工作还是去北漂？"},{"id":347300056,"name":"文科生睡前催眠🌙","rcmdText":"听听文科怎样去学习","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/bitg0Zdabd9TCEyFMSZiMA==/18599338697182078.jpg","programCount":19,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"勿忘你"}]},{"categoryId":453051,"categoryName":"商业财经","radios":[{"id":349584174,"name":"零成本创业秘籍","rcmdText":"网赚实战家吴澜蛟教你赚钱","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/sFPQmdlo_AbO_HZLJwKWfg==/18828037115917224.jpg","programCount":481,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"真正优秀的人都\u201c自带鸡血\u201d"},{"id":791893491,"name":"猩猩时间：超级财智养成记","rcmdText":"1分钟学会的理财小知识","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/7Bs6opV5lw6SlHAfHnF22A==/109951163585505142.jpg","programCount":217,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"不作不死，守住硬核资产 [关键词： 出清]"},{"id":349329242,"name":"5分钟职场商学院","rcmdText":"解锁商业实战新技能","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/BsQXTWuEJ7h0eyXZyIRYFQ==/19113910137277817.jpg","programCount":12,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":""}]},{"categoryId":453052,"categoryName":"科技科学","radios":[{"id":349463188,"name":"星缘星语","rcmdText":"畅谈商业科技，共享创新生活","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/siXmSPX4VvZuRfehQ38iOA==/18972073137327825.jpg","programCount":263,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"【星缘星语】No.263-广州天文组织2"},{"id":336355127,"name":"代码时间","rcmdText":"程序员的中文播客节目","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/BBK9kY31geOOdnkaxiwCdw==/3442570909917251.jpg","programCount":36,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"Visual Studio Code - 吕鹏"},{"id":4969001,"name":"Anyway.FM 设计杂谈","rcmdText":"听他们讲述自己对设计的热情","radioFeeType":0,"feeScope":0,"picUrl":"http://p2.music.126.net/Dusdswcu4LBKMccz61P_3g==/18795051766966813.jpg","programCount":104,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"№95: 长假刚过，让我们先来看看片吧~"}]},{"categoryId":12,"categoryName":"旅途|城市","radios":[{"id":347191192,"name":"Japan Style 日本放送\u2014\u20142011~2012年的声音记忆","rcmdText":"关于日本的点滴精彩","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/1xo9r6dB89cB40FFgBdeVg==/18577348464629533.jpg","programCount":11,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"国语-真夏歌谣祭「音乐特集#1」"},{"id":154,"name":"MINEFM","rcmdText":"为城市生活提供年轻态度","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/jfg3JQP3WW2Vq35DqxW9hg==/109951163682558479.jpg","programCount":105,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"「听见广州」和羊城通谈恋爱"},{"id":5532057,"name":"苑旅行\u2014\u2014《跟着电影去旅行》","rcmdText":"电影里那些值得去的景点","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/2M28sYkriBLfbfcD3Y9WuA==/1365593458342980.jpg","programCount":36,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"《无名之辈》"}]}]
     */

    private int code;
    private Object msg;
    private List<DataBean> data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public Object getMsg() {
        return msg;
    }

    public void setMsg(Object msg) {
        this.msg = msg;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * categoryId : 2001
         * categoryName : 创作|翻唱
         * radios : [{"id":792424363,"name":"FAFA的电台","rcmdText":"只想唱歌给你听","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/8rKCtMIkRPZIW4nHX21M9A==/109951164355969490.jpg","programCount":55,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"不得不爱"},{"id":527394590,"name":"爱抽烟屁的彦祖","rcmdText":"让小哥哥的迷人低音炮治愈你","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/7fsiPQ8f3MfJVprSeaPbmg==/109951163391639863.jpg","programCount":44,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"年轮说"},{"id":791718404,"name":"是某猪呀","rcmdText":"心情不好的时候，听他唱歌就对了","radioFeeType":0,"feeScope":0,"picUrl":"http://p1.music.126.net/28iEiuwnYvKMG9Tn4vUsJw==/109951163535930582.jpg","programCount":90,"playCount":null,"alg":"alg_hot","originalPrice":null,"discountPrice":null,"lastProgramName":"南屏晚钟（cover）"}]
         */

        private int categoryId;
        private String categoryName;
        private List<RadiosBean> radios;

        public int getCategoryId() {
            return categoryId;
        }

        public void setCategoryId(int categoryId) {
            this.categoryId = categoryId;
        }

        public String getCategoryName() {
            return categoryName;
        }

        public void setCategoryName(String categoryName) {
            this.categoryName = categoryName;
        }

        public List<RadiosBean> getRadios() {
            return radios;
        }

        public void setRadios(List<RadiosBean> radios) {
            this.radios = radios;
        }

        public static class RadiosBean {
            /**
             * id : 792424363
             * name : FAFA的电台
             * rcmdText : 只想唱歌给你听
             * radioFeeType : 0
             * feeScope : 0
             * picUrl : http://p1.music.126.net/8rKCtMIkRPZIW4nHX21M9A==/109951164355969490.jpg
             * programCount : 55
             * playCount : null
             * alg : alg_hot
             * originalPrice : null
             * discountPrice : null
             * lastProgramName : 不得不爱
             */

            private long id;
            private String name;
            private String rcmdText;
            private int radioFeeType;
            private int feeScope;
            private String picUrl;
            private int programCount;
            private Object playCount;
            private String alg;
            private Object originalPrice;
            private Object discountPrice;
            private String lastProgramName;

            public long getId() {
                return id;
            }

            public void setId(long id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getRcmdText() {
                return rcmdText;
            }

            public void setRcmdText(String rcmdText) {
                this.rcmdText = rcmdText;
            }

            public int getRadioFeeType() {
                return radioFeeType;
            }

            public void setRadioFeeType(int radioFeeType) {
                this.radioFeeType = radioFeeType;
            }

            public int getFeeScope() {
                return feeScope;
            }

            public void setFeeScope(int feeScope) {
                this.feeScope = feeScope;
            }

            public String getPicUrl() {
                return picUrl;
            }

            public void setPicUrl(String picUrl) {
                this.picUrl = picUrl;
            }

            public int getProgramCount() {
                return programCount;
            }

            public void setProgramCount(int programCount) {
                this.programCount = programCount;
            }

            public Object getPlayCount() {
                return playCount;
            }

            public void setPlayCount(Object playCount) {
                this.playCount = playCount;
            }

            public String getAlg() {
                return alg;
            }

            public void setAlg(String alg) {
                this.alg = alg;
            }

            public Object getOriginalPrice() {
                return originalPrice;
            }

            public void setOriginalPrice(Object originalPrice) {
                this.originalPrice = originalPrice;
            }

            public Object getDiscountPrice() {
                return discountPrice;
            }

            public void setDiscountPrice(Object discountPrice) {
                this.discountPrice = discountPrice;
            }

            public String getLastProgramName() {
                return lastProgramName;
            }

            public void setLastProgramName(String lastProgramName) {
                this.lastProgramName = lastProgramName;
            }
        }
    }
}
