<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingLeft="10dp"
    android:paddingTop="10dp"
    android:paddingBottom="5dp"
    android:background="@color/white">

    <TextView
        android:id="@+id/tv_radio_program_header_count"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:text="共111期"
        android:textColor="@color/black"
        android:textStyle="bold" />

    <ImageView
        android:id="@+id/iv_radio_program_sort"
        android:layout_width="15dp"
        android:layout_height="20dp"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="5dp"
        android:src="@drawable/ic_down_black" />

    <TextView
        android:id="@+id/tv_radio_program_sort"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="20dp"
        android:text="排序"
        android:textColor="@color/black" />

    <ImageView
        android:id="@+id/select"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_gravity="center|clip_vertical"
        android:layout_marginRight="5dp"
        android:background="?android:attr/selectableItemBackground"
        android:focusable="false"
        android:gravity="end"
        android:src="@drawable/ic_menu_black" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="20dp"
        android:text="多选"
        android:textColor="@color/black" />
</LinearLayout>