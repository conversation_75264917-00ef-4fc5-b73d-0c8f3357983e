<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="SheetDialogStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/white</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@style/ActionSheetDialogAnimation</item>
    </style>

    <style name="ActionSheetDialogAnimation" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/actionsheet_in</item>
        <item name="android:windowExitAnimation">@anim/actionsheet_out</item>
    </style>
</resources>