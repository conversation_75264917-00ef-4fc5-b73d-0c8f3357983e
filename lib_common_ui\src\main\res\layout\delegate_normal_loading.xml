<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_centerHorizontal="true"
        android:layout_marginTop="30dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/anim_image2"
            android:layout_width="25dp"
            android:layout_height="20dp"
            android:layout_centerInParent="true"
            android:layout_marginTop="60dp"
            android:layout_weight="1"
            android:padding="1dp"
            android:src="@drawable/list_loading" />

        <ImageView
            android:id="@+id/anim_image"
            android:layout_centerInParent="true"
            android:layout_marginTop="60dp"
            android:layout_width="25dp"
            android:layout_height="20dp"
            android:padding="1dp"
            android:src="@drawable/list_loading" />

        <TextView
            android:id="@+id/anim_text"
            android:layout_marginTop="60dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:text="努力加载中..."
            android:textColor="#000000"
            android:textSize="14sp" />
    </LinearLayout>


</RelativeLayout>