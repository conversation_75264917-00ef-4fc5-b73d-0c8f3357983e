package com.imooc.lib_api.model.search;

import java.util.List;

public class RadioSearchBean {

    /**
     * result : {"djRadios":[{"id":*********,"dj":{"defaultAvatar":false,"province":130000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/PO8TsjfLTdbG-igdGFSBLw==/********3602792360.jpg","accountStatus":0,"gender":1,"city":130200,"birthday":************,"userId":********,"userType":0,"nickname":"郭-小嘉","signature":"名士文可公","description":"","detailDescription":"","avatarImgId":********3602792350,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"********3602792360","avatarImgId_str":"********3602792360"},"name":"JOJO的奇妙冒险·天堂之眼bgm集","picUrl":"http://p1.music.126.net/1clU9O807xMbCTKHUarG9w==/********3120085182.jpg","desc":"因为抽风和补档，顺序可能有点问题大家见谅","subCount":1029,"programCount":57,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"EOH·角色选择","lastProgramId":**********,"picId":********3120085180,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":320000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/fwtcAlNpp8YmGYXiD4HQ1w==/********4244991635.jpg","accountStatus":0,"gender":1,"city":320500,"birthday":************,"userId":*********,"userType":0,"nickname":"MINt超","signature":"想了解一个人的品味，就去看他的歌单","description":"","detailDescription":"","avatarImgId":********4244991630,"backgroundImgId":********3585672110,"backgroundUrl":"http://p1.music.126.net/RLEkGL2lCvOjdRu7pz7q1A==/********3585672107.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********3585672107","avatarImgIdStr":"********4244991635","avatarImgId_str":"********4244991635"},"name":"JOJO（含《岸边露伴一动不动》完整ED《FINDING THE TRUTH》）","picUrl":"http://p1.music.126.net/76U4UcSTnJtFkNSq0I7Zbw==/********3567009738.jpg","desc":"荒木的审美是不会差的","subCount":181,"programCount":2,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Finding The Truth(OLD VERSION)-Coda","lastProgramId":**********,"picId":********3567009740,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":440000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/UP5BrMTQhWNsgxXTCIGULw==/********2926607016.jpg","accountStatus":0,"gender":0,"city":440100,"birthday":-*************,"userId":*********,"userType":0,"nickname":"炎拳","signature":"","description":"","detailDescription":"","avatarImgId":********2926607000,"backgroundImgId":********2926603680,"backgroundUrl":"http://p1.music.126.net/57AOIizlutV62A6y1kLk-A==/********2926603680.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********2926603680","avatarImgIdStr":"********2926607016","avatarImgId_str":"********2926607016"},"name":"JOJO！我不做人了！","picUrl":"http://p1.music.126.net/fUNHIGFKEHQRjRghJF_84Q==/*****************.jpg","desc":"间歇性XJBC综合征","subCount":8,"programCount":3,"createTime":*************,"categoryId":5,"category":"脱口秀","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"方法论\u2014\u2014在这个急剧右转的世界","lastProgramId":*********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":340000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/ydYDOWZgsrG6St12xm6yEQ==/********3925654761.jpg","accountStatus":0,"gender":0,"city":340300,"birthday":*************,"userId":**********,"userType":0,"nickname":"玲阳致阴","signature":"","description":"","detailDescription":"","avatarImgId":********3925654770,"backgroundImgId":********2868128400,"backgroundUrl":"http://p1.music.126.net/2zSNIqTcpHL2jIvU6hG0EA==/********2868128395.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********2868128395","avatarImgIdStr":"********3925654761","avatarImgId_str":"********3925654761"},"name":"JOJO的奇妙冒险","picUrl":"http://p1.music.126.net/FVR5ycB94W7x8bcgsGDLsQ==/********3933246804.jpg","desc":"《JOJO的奇妙冒险》，荒木飞吕彦所著漫画。漫画于1987年至2004年在集英社的少年漫画杂志少年JUMP上连载（1987年1·2号刊-2004年47号刊），2005年后在集英社青年漫画杂志UltraJump上长期连载。\n我当初在16年看到它时就有了这个想法，但是没什么时间现在升入大学了才开始慢慢圆了这个梦想。漫画用嘴说出来的话趣味可能会大减但是我会努力给大家做出来的。\n音质可能会很差，毕竟能力有限设备也不是很好。请大家见谅！\n之后一定会做出改变的。","subCount":26,"programCount":3,"createTime":1552829823421,"categoryId":10001,"category":"有声书","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"jojo第一部第二卷第一部分","lastProgramId":**********,"picId":********3933246800,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/nUSjlAHtuIZ3veWF14XK7Q==/********3275800590.jpg","accountStatus":0,"gender":0,"city":1010000,"birthday":-*************,"userId":*********,"userType":0,"nickname":"tosayama","signature":"","description":"","detailDescription":"","avatarImgId":********3275800590,"backgroundImgId":********3558157420,"backgroundUrl":"http://p1.music.126.net/gXZmntPIUayQmPMWHyXy2Q==/********3558157417.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********3558157417","avatarImgIdStr":"********3275800590","avatarImgId_str":"********3275800590"},"name":"看JOJO黄金之风（闲聊吐槽）","picUrl":"http://p1.music.126.net/djPvc_WGWnp5NDTqQGffwQ==/********3731584736.jpg","desc":"心血来潮产物，还在制作中，尽量每周末更新...\n是和小伙伴一起看番的无聊产物（冇的营养）","subCount":30,"programCount":27,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"第36集reaction","lastProgramId":**********,"picId":********3731584740,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":320000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/sE6_gT3aNywE4-PXLHphcw==/********3643561581.jpg","accountStatus":0,"gender":1,"city":320500,"birthday":************,"userId":*********,"userType":201,"nickname":"SLTIEOVNE","signature":"哇，我事二刺螈视频达人欸","description":"","detailDescription":"","avatarImgId":********3643561580,"backgroundImgId":********3643544160,"backgroundUrl":"http://p1.music.126.net/gohZo97pLqFeu8DAvbORDw==/********3643544152.jpg","authority":0,"mutual":false,"expertTags":null,"experts":{"1":"二次元视频达人"},"djStatus":10,"vipType":11,"remarkName":null,"backgroundImgIdStr":"********3643544152","avatarImgIdStr":"********3643561581","avatarImgId_str":"********3643561581"},"name":"JOJO的奇妙电音OST~ジョジョremix","picUrl":"http://p1.music.126.net/moVe8-MUnDryofFFNwuvdg==/*****************.jpg","desc":"一些JOJO动画/游戏BGM的remix","subCount":717,"programCount":33,"createTime":*************,"categoryId":10002,"category":"3D|电子","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"【300订阅】（不务正业）Wish In The Dark_8bits","lastProgramId":**********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":4,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/5hte_mCXFJhnnq_KYm18jA==/********3252027515.jpg","accountStatus":0,"gender":1,"city":1004400,"birthday":************,"userId":********,"userType":0,"nickname":"我的流川没有疯","signature":"众筹500粉！！感谢关注我的137个小伙伴 感谢支持！我会努力更新高质量动漫视频的 相信我！","description":"","detailDescription":"","avatarImgId":********3252027520,"backgroundImgId":********4098076960,"backgroundUrl":"http://p1.music.126.net/jA59elzsBtvO82CTJ_VkFA==/********4098076953.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********4098076953","avatarImgIdStr":"********3252027515","avatarImgId_str":"********3252027515"},"name":"JOJO的奇妙冒险Fandub","picUrl":"http://p1.music.126.net/oJmQUVDq1mdrA17A_Ri8MA==/*****************.jpg","desc":"分享关于JOJO的一切bgm","subCount":484,"programCount":7,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"我乔鲁诺有一个梦想","lastProgramId":**********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":1,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":510000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/2jJoegbREbkVSC60vLVwmw==/********3950534920.jpg","accountStatus":0,"gender":2,"city":510100,"birthday":************,"userId":*********,"userType":0,"nickname":"Jojo的琵琶","signature":"Jojo，业余琵琶演奏者，琵琶助教，7岁学习琵琶，师从四川音乐学院韩淑德教授，后来也得到了琵琶大师王范地教授的指点。目前长期在自媒体平台更新翻奏曲目以及教学小视频。希望为弘扬民族乐器贡献一份力量！","description":"","detailDescription":"","avatarImgId":********3950534910,"backgroundImgId":********2868128400,"backgroundUrl":"http://p1.music.126.net/2zSNIqTcpHL2jIvU6hG0EA==/********2868128395.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********2868128395","avatarImgIdStr":"********3950534920","avatarImgId_str":"********3950534920"},"name":"Jojo的琵琶","picUrl":"http://p1.music.126.net/1SY1hft3PMLZ3hfrDSpmzw==/********3952499840.jpg","desc":"Jojo，业余琵琶演奏者，琵琶助教，7岁学习琵琶，师从四川音乐学院韩淑德教授，后来也得到了琵琶大师王范地教授的指点。目前长期在自媒体平台更新翻奏曲目以及教学小视频。希望为弘扬民族乐器贡献一份力量！","subCount":93,"programCount":23,"createTime":1553563662037,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Jojo。琵琶 昔言","lastProgramId":**********,"picId":********3952499840,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":1383018,"dj":{"defaultAvatar":false,"province":510000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/7x5G2tMB4sYa9P69vY5Big==/********3754637418.jpg","accountStatus":0,"gender":0,"city":510100,"birthday":-*************,"userId":********,"userType":0,"nickname":"云云云10","signature":"这个人枯了。","description":"","detailDescription":"","avatarImgId":********3754637420,"backgroundImgId":*****************,"backgroundUrl":"http://p1.music.126.net/6INiiaad0QeLK6vl1KJIsA==/*****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"*****************","avatarImgIdStr":"********3754637418","avatarImgId_str":"********3754637418"},"name":"JOJO★MMD曲","picUrl":"http://p1.music.126.net/s7YZz8F34Kg1qzTqeDNQ_g==/****************.jpg","desc":"b站JOJO相关的MMDBGM推荐(●'◡'●)ﾉ\nav号在描述里，不定期添加歌词","subCount":104,"programCount":13,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"【二部师徒组】Masked bitcH","lastProgramId":*********,"picId":****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":5817003,"dj":{"defaultAvatar":true,"province":340000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/VnZiScyynLG7atLIZ2YPkw==/*****************.jpg","accountStatus":0,"gender":0,"city":341500,"birthday":-*************,"userId":*********,"userType":0,"nickname":"是谁乱了节奏","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/5L9yqWa_UnlHtlp7li5PAg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"*****************","avatarImgId_str":"*****************"},"name":"JOJO","picUrl":"http://p1.music.126.net/HJV2oef1ViqhzpQlwuUZkw==/****************.jpg","desc":"我不是离不开你，我只是离不开我的心","subCount":2,"programCount":1,"createTime":*************,"categoryId":3,"category":"情感调频","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"输入法","lastProgramId":*********,"picId":****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":2981029,"dj":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/N-A-Aq9uLWEUEJ5cASakVw==/********4182882524.jpg","accountStatus":0,"gender":2,"city":1010000,"birthday":*************,"userId":********,"userType":0,"nickname":"青羅-","signature":"アヤカシ","description":"","detailDescription":"","avatarImgId":********4182882530,"backgroundImgId":********4182880540,"backgroundUrl":"http://p1.music.126.net/wQe7Z8XkNJXbW0IEfU7MVw==/********4182880546.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"backgroundImgIdStr":"********4182880546","avatarImgIdStr":"********4182882524","avatarImgId_str":"********4182882524"},"name":"JOJOの奇妙MMD选曲★","picUrl":"http://p1.music.126.net/WuJneG6GwKwD5W9UB-OG-g==/****************.jpg","desc":"b站上热门JOJOMMD的BGM歌集~","subCount":37,"programCount":3,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Paranoid","lastProgramId":********,"picId":****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":110000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/qEHoL3rfzSJmD4DYBJQkOA==/*****************.jpg","accountStatus":0,"gender":0,"city":110102,"birthday":-*************,"userId":*********,"userType":0,"nickname":"玖玖JoJo_","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":********2902228700,"backgroundUrl":"http://p1.music.126.net/J9KDDJFrX2mPv2NVVVeMtQ==/********2902228705.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********2902228705","avatarImgIdStr":"*****************","avatarImgId_str":"*****************"},"name":"JoJo的音乐盒","picUrl":"http://p1.music.126.net/mhdNaHRs4CVSEPZKDfroGQ==/*****************.jpg","desc":"唱一些喜欢的歌~","subCount":9,"programCount":3,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"填翻 | 春日","lastProgramId":*********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":530000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/-syqxTF9fJSB5XiEiW7x0w==/****************.jpg","accountStatus":0,"gender":2,"city":530800,"birthday":************,"userId":********,"userType":0,"nickname":"倔强的小红军jojo","signature":"为君拾莲子，清妖亦可生","description":"","detailDescription":"","avatarImgId":****************,"backgroundImgId":*****************,"backgroundUrl":"http://p1.music.126.net/zNRh9BrbvbW5REK9VGw_-A==/*****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"*****************","avatarImgIdStr":"****************"},"name":"from外星倔强的小红军jojo","picUrl":"http://p1.music.126.net/zXLJm6vro6EC6G4JJs02VQ==/*****************.jpg","desc":"呃\u2026\u2026","subCount":1,"programCount":1,"createTime":*************,"categoryId":6,"category":"美文读物","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"围城","lastProgramId":*********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":120000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/jgQawQfMl3KOjL8ySvdBiQ==/*****************.jpg","accountStatus":0,"gender":1,"city":120101,"birthday":************,"userId":*********,"userType":0,"nickname":"今天你磕糖了吗","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":*****************,"backgroundUrl":"http://p1.music.126.net/Y47D3XcySabhiadlZaDKPg==/*****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"*****************","avatarImgIdStr":"*****************","avatarImgId_str":"*****************"},"name":"jojo英语","picUrl":"http://p1.music.126.net/ozFZ990IqwTVBhV0ejPSZw==/****************.jpg","desc":"加油！","subCount":0,"programCount":1,"createTime":*************,"categoryId":13,"category":"外语世界","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"英语单词","lastProgramId":*********,"picId":****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":610000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/mRDNZkCgJMIMq5hINgm9oQ==/********4152597603.jpg","accountStatus":0,"gender":2,"city":610100,"birthday":************,"userId":*********,"userType":0,"nickname":"JoooQueen","signature":"underground电子","description":"","detailDescription":"","avatarImgId":********4152597600,"backgroundImgId":********2801531380,"backgroundUrl":"http://p1.music.126.net/4KelA7qNHa9fwBu-2RhXOQ==/********2801531371.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********2801531371","avatarImgIdStr":"********4152597603","avatarImgId_str":"********4152597603"},"name":"Jojo demo","picUrl":"http://p1.music.126.net/fyauej5Jf8OYIvMhW39EHA==/****************.jpg","desc":"recording","subCount":2,"programCount":5,"createTime":*************,"categoryId":10002,"category":"3D|电子","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"心如止水（Cover：Ice Paper）","lastProgramId":**********,"picId":****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":330000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/Ib2wkjT_Nq7C5WnzInMd4A==/********3104139060.jpg","accountStatus":0,"gender":1,"city":330100,"birthday":-*************,"userId":*********,"userType":0,"nickname":"朱侨侨","signature":"朱侨侨","description":"","detailDescription":"","avatarImgId":********3104139060,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"********3104139060","avatarImgId_str":"********3104139060"},"name":"朱侨侨JOJO粤语精选","picUrl":"http://p1.music.126.net/YjTSq29g9VlBYyQ__tu2UQ==/********3104459868.jpg","desc":"朱侨侨JOJO粤语精选","subCount":0,"programCount":27,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"大海(粤)A","lastProgramId":**********,"picId":********3104459870,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":310000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/ATepZVgjdmcVi5uGQz935Q==/********3040276187.jpg","accountStatus":0,"gender":2,"city":310107,"birthday":************,"userId":*********,"userType":0,"nickname":"JoJoChen_Miraesol","signature":"【Miraesol·蜜芮斯】#纯韩打造\u2022皮肤管理私人定制##瘦身塑形，光学美肤，韩式半永久，日韩风美甲等#","description":"","detailDescription":"","avatarImgId":********3040276200,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/45Nu4EqvFqK_kQj6BkPwcw==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":10,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"********3040276187","avatarImgId_str":"********3040276187"},"name":"JoJo MiraeSol","picUrl":"http://p1.music.126.net/mDsVkVl8lZp0UAt6Oge8tA==/********3518831979.jpg","desc":"JoJo MiraeSol Original Mix, Deep House/Techno/Future Bass，EDM爱好者","subCount":1,"programCount":1,"createTime":*************,"categoryId":2,"category":"音乐故事","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"JoJo Track01","lastProgramId":**********,"picId":********3518831980,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":330000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/Ib2wkjT_Nq7C5WnzInMd4A==/********3104139060.jpg","accountStatus":0,"gender":1,"city":330100,"birthday":-*************,"userId":*********,"userType":0,"nickname":"朱侨侨","signature":"朱侨侨","description":"","detailDescription":"","avatarImgId":********3104139060,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"********3104139060","avatarImgId_str":"********3104139060"},"name":"朱侨侨JOJO英文歌","picUrl":"http://p1.music.126.net/7WOAp4oUQfToqsFRtyzelQ==/********3108230045.jpg","desc":"朱侨侨JOJO英文歌","subCount":0,"programCount":8,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"take me to your heart","lastProgramId":**********,"picId":********3108230050,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":430000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/qaLiTMRpL0gWd7y1YE55eQ==/********2977710400.jpg","accountStatus":0,"gender":0,"city":430100,"birthday":-*************,"userId":*********,"userType":0,"nickname":"戌戌戌戌","signature":"","description":"","detailDescription":"","avatarImgId":********2977710400,"backgroundImgId":********2868128400,"backgroundUrl":"http://p1.music.126.net/2zSNIqTcpHL2jIvU6hG0EA==/********2868128395.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********2868128395","avatarImgIdStr":"********2977710400","avatarImgId_str":"********2977710400"},"name":"好梦喔jojo~","picUrl":"http://p1.music.126.net/yJukjCAY3iwUZZO_V8hUFw==/*****************.jpg","desc":"私人电台","subCount":1,"programCount":4,"createTime":*************,"categoryId":10001,"category":"有声书","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"看海的人 partlV","lastProgramId":*********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":130000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/r2ekCQFf7GmzAJv7sqKhqQ==/********4146053446.jpg","accountStatus":0,"gender":1,"city":130700,"birthday":************,"userId":*********,"userType":0,"nickname":"乔壹-Jojo","signature":"\"   我的成熟都是假正经 我的幼稚才是真性情         \"","description":"","detailDescription":"","avatarImgId":********4146053440,"backgroundImgId":********4146055120,"backgroundUrl":"http://p1.music.126.net/MVGRJ53DW5D68k8eWZKY7A==/********4146055119.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"backgroundImgIdStr":"********4146055119","avatarImgIdStr":"********4146053446","avatarImgId_str":"********4146053446"},"name":"乔壹-Jojo.","picUrl":"http://p1.music.126.net/WvUcsVXL-v-FolQI25UCOg==/********4113158079.jpg","desc":"只在夜深人静时 独自欢言 独自悲伤。","subCount":2,"programCount":1,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"残酷月光.mp3","lastProgramId":**********,"picId":********4113158080,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":310000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/cPLbIJrs-FsR439NAhaylQ==/********3230882525.jpg","accountStatus":0,"gender":1,"city":310101,"birthday":************,"userId":*********,"userType":0,"nickname":"中科院网络回复研究所教授","signature":"Drop database","description":"","detailDescription":"","avatarImgId":********3230882530,"backgroundImgId":********2879219090,"backgroundUrl":"http://p1.music.126.net/c2ec2TeixKn7R70DJNztYQ==/********2879219085.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"backgroundImgIdStr":"********2879219085","avatarImgIdStr":"********3230882525","avatarImgId_str":"********3230882525"},"name":"DJ JoJo not in your house","picUrl":"http://p1.music.126.net/v54OY4t6SrLfFuxuCauxwQ==/********3751954965.jpg","desc":"Where is JoJo","subCount":4,"programCount":2,"createTime":*************,"categoryId":10002,"category":"3D|电子","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Viral Hip Hop 2019 1 5, 2:47 PM","lastProgramId":**********,"picId":********3751954960,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":330000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/PquOjv5vBCX-4OL4BVzFYw==/********3427992784.jpg","accountStatus":0,"gender":0,"city":330100,"birthday":-*************,"userId":*********,"userType":0,"nickname":"JoJo-MIEMIE","signature":"","description":"","detailDescription":"","avatarImgId":********3427992780,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/bmA_ablsXpq3Tk9HlEg9sA==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"********3427992784","avatarImgId_str":"********3427992784"},"name":"《解忧杂货铺》\u2014\u2014JoJo","picUrl":"http://p1.music.126.net/Epd9V9w62HCNHBkmPJ6L3Q==/*****************.jpg","desc":"念念不忘，必有回响","subCount":1,"programCount":1,"createTime":*************,"categoryId":10001,"category":"有声书","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"解忧杂货铺","lastProgramId":*********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":310000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/91YOtypJ9GSJ5CA5IF_yeQ==/********3094247631.jpg","accountStatus":0,"gender":2,"city":310101,"birthday":-*************,"userId":*********,"userType":4,"nickname":"Jojo卓嬌","signature":"","description":"","detailDescription":"","avatarImgId":********3094247630,"backgroundImgId":********4087201490,"backgroundUrl":"http://p1.music.126.net/L5FFpu8JPxfzaW5OK7YvIg==/********4087201485.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********4087201485","avatarImgIdStr":"********3094247631","avatarImgId_str":"********3094247631"},"name":"我的demo作品和早期作品","picUrl":"http://p1.music.126.net/4cIAVFCmBABTnanCsILDgw==/********4103321056.jpg","desc":"早期作品 2015~2016\n自写自编自弹唱\n啊哈哈","subCount":2,"programCount":10,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"想说爱你xxxxxxxxx","lastProgramId":**********,"picId":********4103321060,"rcmdText":null,"composeVideo":false,"shareCount":2,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":640000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/HDXqUcWvKP_LsiVBIqyRCg==/********3985759766.jpg","accountStatus":0,"gender":1,"city":640100,"birthday":************,"userId":*********,"userType":0,"nickname":"JOJO718","signature":"I wish you hell","description":"","detailDescription":"","avatarImgId":********3985759760,"backgroundImgId":********3944340420,"backgroundUrl":"http://p1.music.126.net/pGHzsWnI8NmRlO5omTcE5Q==/********3944340413.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********3944340413","avatarImgIdStr":"********3985759766","avatarImgId_str":"********3985759766"},"name":"/null","picUrl":"http://p1.music.126.net/6_l_yQNpBA73w0CeIKK9aw==/*****************.jpg","desc":"网易云没有的","subCount":4,"programCount":6,"createTime":*************,"categoryId":2,"category":"音乐故事","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Not For U","lastProgramId":**********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":310000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/91YOtypJ9GSJ5CA5IF_yeQ==/********3094247631.jpg","accountStatus":0,"gender":2,"city":310101,"birthday":-*************,"userId":*********,"userType":4,"nickname":"Jojo卓嬌","signature":"","description":"","detailDescription":"","avatarImgId":********3094247630,"backgroundImgId":********4087201490,"backgroundUrl":"http://p1.music.126.net/L5FFpu8JPxfzaW5OK7YvIg==/********4087201485.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********4087201485","avatarImgIdStr":"********3094247631","avatarImgId_str":"********3094247631"},"name":"我的cover翻唱","picUrl":"http://p1.music.126.net/3yYCB4EFIc6RrpVpAoGnZg==/********3079604074.jpg","desc":"cover cover 改编翻唱！ yay! \n@JoJo卓嬌","subCount":3,"programCount":1,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"有一个姑娘 cover","lastProgramId":*********,"picId":********3079604080,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":310000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/ayLz8rDZHxwqUpTSMsxteg==/********3464243249.jpg","accountStatus":0,"gender":2,"city":310101,"birthday":************,"userId":**********,"userType":4,"nickname":"阿舟JoJo","signature":"深存感恩之心，又独自远行。","description":"","detailDescription":"","avatarImgId":********3464243250,"backgroundImgId":********3365635400,"backgroundUrl":"http://p1.music.126.net/tW-2G1xXJmDtDDcQvAZaIg==/********3365635399.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********3365635399","avatarImgIdStr":"********3464243249","avatarImgId_str":"********3464243249"},"name":"阿舟爱唱歌","picUrl":"http://p1.music.126.net/g9IsJ0cec64jQ8i7536iXQ==/********3464233628.jpg","desc":"与君歌一曲，请君为我亲耳听。","subCount":2,"programCount":2,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"永不失联的爱（cover.胖胖胖&陈以桐）","lastProgramId":**********,"picId":********3464233630,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":520000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/p8aeGIUWzJA_qO7bx9TAYg==/****************.jpg","accountStatus":0,"gender":2,"city":522700,"birthday":-*************,"userId":*********,"userType":0,"nickname":"JOJO于终","signature":"明天会更好。。。。。","description":"","detailDescription":"","avatarImgId":****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/45Nu4EqvFqK_kQj6BkPwcw==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"****************"},"name":"不改了℃","picUrl":"http://p1.music.126.net/hJPPYQplvat8USXsLtvU7Q==/*****************.jpg","desc":"每个人都有一个怀念，而你是我最怀念的。。","subCount":0,"programCount":1,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":0,"lastProgramName":null,"lastProgramId":0,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":320000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/bSGjyGXZ6WLG2tEPjnT3OQ==/*****************.jpg","accountStatus":0,"gender":2,"city":320500,"birthday":-*************,"userId":*********,"userType":0,"nickname":"COCOMJOJO","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":*****************,"backgroundUrl":"http://p1.music.126.net/963gREPWXv0Y4_MXZhWh7g==/*****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"backgroundImgIdStr":"*****************","avatarImgIdStr":"*****************","avatarImgId_str":"*****************"},"name":"living history","picUrl":"http://p1.music.126.net/WjA-z3-p5xAyeZf3ovu3sQ==/*****************.jpg","desc":"练习口语，娱乐大家","subCount":1,"programCount":11,"createTime":*************,"categoryId":13,"category":"外语世界","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Waiting For My Guitar","lastProgramId":**********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":440000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/4vELljDrRWFL3yWEEWpRIQ==/****************.jpg","accountStatus":0,"gender":1,"city":442000,"birthday":************,"userId":*********,"userType":0,"nickname":"Jojo23333","signature":"LIVE for LIFE. HUSTLE EVERYDAY.  | 📍GZC | 公众号 / 网易云：Jojo23333","description":"","detailDescription":"","avatarImgId":****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/ZWBIFRgBLNPYwjaFy0V3yw==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"****************"},"name":"各种各样的歌。","picUrl":"http://p1.music.126.net/t9JRIJc1vzibo5fFNNC7tg==/****************.jpg","desc":"#慢慢更新\n点进去可以找到节目包含的歌曲\n只是想分享那些歌再加自己的想法","subCount":3,"programCount":1,"createTime":*************,"categoryId":2,"category":"音乐故事","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Ronald Jenkees 即兴电音鬼才","lastProgramId":*********,"picId":****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":210000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/7-loMVI1FsyL6sDskcY7aw==/********4132416692.jpg","accountStatus":0,"gender":1,"city":210100,"birthday":************,"userId":*********,"userType":0,"nickname":"佳男JoJo","signature":"唱作歌手","description":"","detailDescription":"","avatarImgId":********4132416690,"backgroundImgId":********4132416720,"backgroundUrl":"http://p1.music.126.net/T9hMnuzWTbWChpqNex1h8g==/********4132416718.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********4132416718","avatarImgIdStr":"********4132416692","avatarImgId_str":"********4132416692"},"name":"佳男","picUrl":"http://p1.music.126.net/cglhK3acv0AP4rRuiyMO6A==/********3568877202.jpg","desc":"我的翻唱曲库","subCount":63,"programCount":1,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":**********648,"lastProgramName":"只要有你的地方(cover林俊杰) cr ladyttt","lastProgramId":2056691901,"picId":********3568877200,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0}],"djRadiosCount":30}
     * code : 200
     */

    private ResultBean result;
    private int code;

    public ResultBean getResult() {
        return result;
    }

    public void setResult(ResultBean result) {
        this.result = result;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public static class ResultBean {
        /**
         * djRadios : [{"id":*********,"dj":{"defaultAvatar":false,"province":130000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/PO8TsjfLTdbG-igdGFSBLw==/********3602792360.jpg","accountStatus":0,"gender":1,"city":130200,"birthday":************,"userId":********,"userType":0,"nickname":"郭-小嘉","signature":"名士文可公","description":"","detailDescription":"","avatarImgId":********3602792350,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"********3602792360","avatarImgId_str":"********3602792360"},"name":"JOJO的奇妙冒险·天堂之眼bgm集","picUrl":"http://p1.music.126.net/1clU9O807xMbCTKHUarG9w==/********3120085182.jpg","desc":"因为抽风和补档，顺序可能有点问题大家见谅","subCount":1029,"programCount":57,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"EOH·角色选择","lastProgramId":**********,"picId":********3120085180,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":320000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/fwtcAlNpp8YmGYXiD4HQ1w==/********4244991635.jpg","accountStatus":0,"gender":1,"city":320500,"birthday":************,"userId":*********,"userType":0,"nickname":"MINt超","signature":"想了解一个人的品味，就去看他的歌单","description":"","detailDescription":"","avatarImgId":********4244991630,"backgroundImgId":********3585672110,"backgroundUrl":"http://p1.music.126.net/RLEkGL2lCvOjdRu7pz7q1A==/********3585672107.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********3585672107","avatarImgIdStr":"********4244991635","avatarImgId_str":"********4244991635"},"name":"JOJO（含《岸边露伴一动不动》完整ED《FINDING THE TRUTH》）","picUrl":"http://p1.music.126.net/76U4UcSTnJtFkNSq0I7Zbw==/********3567009738.jpg","desc":"荒木的审美是不会差的","subCount":181,"programCount":2,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Finding The Truth(OLD VERSION)-Coda","lastProgramId":**********,"picId":********3567009740,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":440000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/UP5BrMTQhWNsgxXTCIGULw==/********2926607016.jpg","accountStatus":0,"gender":0,"city":440100,"birthday":-*************,"userId":*********,"userType":0,"nickname":"炎拳","signature":"","description":"","detailDescription":"","avatarImgId":********2926607000,"backgroundImgId":********2926603680,"backgroundUrl":"http://p1.music.126.net/57AOIizlutV62A6y1kLk-A==/********2926603680.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********2926603680","avatarImgIdStr":"********2926607016","avatarImgId_str":"********2926607016"},"name":"JOJO！我不做人了！","picUrl":"http://p1.music.126.net/fUNHIGFKEHQRjRghJF_84Q==/*****************.jpg","desc":"间歇性XJBC综合征","subCount":8,"programCount":3,"createTime":*************,"categoryId":5,"category":"脱口秀","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"方法论\u2014\u2014在这个急剧右转的世界","lastProgramId":*********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":340000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/ydYDOWZgsrG6St12xm6yEQ==/********3925654761.jpg","accountStatus":0,"gender":0,"city":340300,"birthday":*************,"userId":**********,"userType":0,"nickname":"玲阳致阴","signature":"","description":"","detailDescription":"","avatarImgId":********3925654770,"backgroundImgId":********2868128400,"backgroundUrl":"http://p1.music.126.net/2zSNIqTcpHL2jIvU6hG0EA==/********2868128395.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********2868128395","avatarImgIdStr":"********3925654761","avatarImgId_str":"********3925654761"},"name":"JOJO的奇妙冒险","picUrl":"http://p1.music.126.net/FVR5ycB94W7x8bcgsGDLsQ==/********3933246804.jpg","desc":"《JOJO的奇妙冒险》，荒木飞吕彦所著漫画。漫画于1987年至2004年在集英社的少年漫画杂志少年JUMP上连载（1987年1·2号刊-2004年47号刊），2005年后在集英社青年漫画杂志UltraJump上长期连载。\n我当初在16年看到它时就有了这个想法，但是没什么时间现在升入大学了才开始慢慢圆了这个梦想。漫画用嘴说出来的话趣味可能会大减但是我会努力给大家做出来的。\n音质可能会很差，毕竟能力有限设备也不是很好。请大家见谅！\n之后一定会做出改变的。","subCount":26,"programCount":3,"createTime":1552829823421,"categoryId":10001,"category":"有声书","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"jojo第一部第二卷第一部分","lastProgramId":**********,"picId":********3933246800,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/nUSjlAHtuIZ3veWF14XK7Q==/********3275800590.jpg","accountStatus":0,"gender":0,"city":1010000,"birthday":-*************,"userId":*********,"userType":0,"nickname":"tosayama","signature":"","description":"","detailDescription":"","avatarImgId":********3275800590,"backgroundImgId":********3558157420,"backgroundUrl":"http://p1.music.126.net/gXZmntPIUayQmPMWHyXy2Q==/********3558157417.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********3558157417","avatarImgIdStr":"********3275800590","avatarImgId_str":"********3275800590"},"name":"看JOJO黄金之风（闲聊吐槽）","picUrl":"http://p1.music.126.net/djPvc_WGWnp5NDTqQGffwQ==/********3731584736.jpg","desc":"心血来潮产物，还在制作中，尽量每周末更新...\n是和小伙伴一起看番的无聊产物（冇的营养）","subCount":30,"programCount":27,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"第36集reaction","lastProgramId":**********,"picId":********3731584740,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":320000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/sE6_gT3aNywE4-PXLHphcw==/********3643561581.jpg","accountStatus":0,"gender":1,"city":320500,"birthday":************,"userId":*********,"userType":201,"nickname":"SLTIEOVNE","signature":"哇，我事二刺螈视频达人欸","description":"","detailDescription":"","avatarImgId":********3643561580,"backgroundImgId":********3643544160,"backgroundUrl":"http://p1.music.126.net/gohZo97pLqFeu8DAvbORDw==/********3643544152.jpg","authority":0,"mutual":false,"expertTags":null,"experts":{"1":"二次元视频达人"},"djStatus":10,"vipType":11,"remarkName":null,"backgroundImgIdStr":"********3643544152","avatarImgIdStr":"********3643561581","avatarImgId_str":"********3643561581"},"name":"JOJO的奇妙电音OST~ジョジョremix","picUrl":"http://p1.music.126.net/moVe8-MUnDryofFFNwuvdg==/*****************.jpg","desc":"一些JOJO动画/游戏BGM的remix","subCount":717,"programCount":33,"createTime":*************,"categoryId":10002,"category":"3D|电子","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"【300订阅】（不务正业）Wish In The Dark_8bits","lastProgramId":**********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":4,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/5hte_mCXFJhnnq_KYm18jA==/********3252027515.jpg","accountStatus":0,"gender":1,"city":1004400,"birthday":************,"userId":********,"userType":0,"nickname":"我的流川没有疯","signature":"众筹500粉！！感谢关注我的137个小伙伴 感谢支持！我会努力更新高质量动漫视频的 相信我！","description":"","detailDescription":"","avatarImgId":********3252027520,"backgroundImgId":********4098076960,"backgroundUrl":"http://p1.music.126.net/jA59elzsBtvO82CTJ_VkFA==/********4098076953.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********4098076953","avatarImgIdStr":"********3252027515","avatarImgId_str":"********3252027515"},"name":"JOJO的奇妙冒险Fandub","picUrl":"http://p1.music.126.net/oJmQUVDq1mdrA17A_Ri8MA==/*****************.jpg","desc":"分享关于JOJO的一切bgm","subCount":484,"programCount":7,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"我乔鲁诺有一个梦想","lastProgramId":**********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":1,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":510000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/2jJoegbREbkVSC60vLVwmw==/********3950534920.jpg","accountStatus":0,"gender":2,"city":510100,"birthday":************,"userId":*********,"userType":0,"nickname":"Jojo的琵琶","signature":"Jojo，业余琵琶演奏者，琵琶助教，7岁学习琵琶，师从四川音乐学院韩淑德教授，后来也得到了琵琶大师王范地教授的指点。目前长期在自媒体平台更新翻奏曲目以及教学小视频。希望为弘扬民族乐器贡献一份力量！","description":"","detailDescription":"","avatarImgId":********3950534910,"backgroundImgId":********2868128400,"backgroundUrl":"http://p1.music.126.net/2zSNIqTcpHL2jIvU6hG0EA==/********2868128395.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********2868128395","avatarImgIdStr":"********3950534920","avatarImgId_str":"********3950534920"},"name":"Jojo的琵琶","picUrl":"http://p1.music.126.net/1SY1hft3PMLZ3hfrDSpmzw==/********3952499840.jpg","desc":"Jojo，业余琵琶演奏者，琵琶助教，7岁学习琵琶，师从四川音乐学院韩淑德教授，后来也得到了琵琶大师王范地教授的指点。目前长期在自媒体平台更新翻奏曲目以及教学小视频。希望为弘扬民族乐器贡献一份力量！","subCount":93,"programCount":23,"createTime":1553563662037,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Jojo。琵琶 昔言","lastProgramId":**********,"picId":********3952499840,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":1383018,"dj":{"defaultAvatar":false,"province":510000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/7x5G2tMB4sYa9P69vY5Big==/********3754637418.jpg","accountStatus":0,"gender":0,"city":510100,"birthday":-*************,"userId":********,"userType":0,"nickname":"云云云10","signature":"这个人枯了。","description":"","detailDescription":"","avatarImgId":********3754637420,"backgroundImgId":*****************,"backgroundUrl":"http://p1.music.126.net/6INiiaad0QeLK6vl1KJIsA==/*****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"*****************","avatarImgIdStr":"********3754637418","avatarImgId_str":"********3754637418"},"name":"JOJO★MMD曲","picUrl":"http://p1.music.126.net/s7YZz8F34Kg1qzTqeDNQ_g==/****************.jpg","desc":"b站JOJO相关的MMDBGM推荐(●'◡'●)ﾉ\nav号在描述里，不定期添加歌词","subCount":104,"programCount":13,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"【二部师徒组】Masked bitcH","lastProgramId":*********,"picId":****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":5817003,"dj":{"defaultAvatar":true,"province":340000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/VnZiScyynLG7atLIZ2YPkw==/*****************.jpg","accountStatus":0,"gender":0,"city":341500,"birthday":-*************,"userId":*********,"userType":0,"nickname":"是谁乱了节奏","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/5L9yqWa_UnlHtlp7li5PAg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"*****************","avatarImgId_str":"*****************"},"name":"JOJO","picUrl":"http://p1.music.126.net/HJV2oef1ViqhzpQlwuUZkw==/****************.jpg","desc":"我不是离不开你，我只是离不开我的心","subCount":2,"programCount":1,"createTime":*************,"categoryId":3,"category":"情感调频","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"输入法","lastProgramId":*********,"picId":****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":2981029,"dj":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/N-A-Aq9uLWEUEJ5cASakVw==/********4182882524.jpg","accountStatus":0,"gender":2,"city":1010000,"birthday":*************,"userId":********,"userType":0,"nickname":"青羅-","signature":"アヤカシ","description":"","detailDescription":"","avatarImgId":********4182882530,"backgroundImgId":********4182880540,"backgroundUrl":"http://p1.music.126.net/wQe7Z8XkNJXbW0IEfU7MVw==/********4182880546.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"backgroundImgIdStr":"********4182880546","avatarImgIdStr":"********4182882524","avatarImgId_str":"********4182882524"},"name":"JOJOの奇妙MMD选曲★","picUrl":"http://p1.music.126.net/WuJneG6GwKwD5W9UB-OG-g==/****************.jpg","desc":"b站上热门JOJOMMD的BGM歌集~","subCount":37,"programCount":3,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Paranoid","lastProgramId":********,"picId":****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":110000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/qEHoL3rfzSJmD4DYBJQkOA==/*****************.jpg","accountStatus":0,"gender":0,"city":110102,"birthday":-*************,"userId":*********,"userType":0,"nickname":"玖玖JoJo_","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":********2902228700,"backgroundUrl":"http://p1.music.126.net/J9KDDJFrX2mPv2NVVVeMtQ==/********2902228705.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********2902228705","avatarImgIdStr":"*****************","avatarImgId_str":"*****************"},"name":"JoJo的音乐盒","picUrl":"http://p1.music.126.net/mhdNaHRs4CVSEPZKDfroGQ==/*****************.jpg","desc":"唱一些喜欢的歌~","subCount":9,"programCount":3,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"填翻 | 春日","lastProgramId":*********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":530000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/-syqxTF9fJSB5XiEiW7x0w==/****************.jpg","accountStatus":0,"gender":2,"city":530800,"birthday":************,"userId":********,"userType":0,"nickname":"倔强的小红军jojo","signature":"为君拾莲子，清妖亦可生","description":"","detailDescription":"","avatarImgId":****************,"backgroundImgId":*****************,"backgroundUrl":"http://p1.music.126.net/zNRh9BrbvbW5REK9VGw_-A==/*****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"*****************","avatarImgIdStr":"****************"},"name":"from外星倔强的小红军jojo","picUrl":"http://p1.music.126.net/zXLJm6vro6EC6G4JJs02VQ==/*****************.jpg","desc":"呃\u2026\u2026","subCount":1,"programCount":1,"createTime":*************,"categoryId":6,"category":"美文读物","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"围城","lastProgramId":*********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":120000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/jgQawQfMl3KOjL8ySvdBiQ==/*****************.jpg","accountStatus":0,"gender":1,"city":120101,"birthday":************,"userId":*********,"userType":0,"nickname":"今天你磕糖了吗","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":*****************,"backgroundUrl":"http://p1.music.126.net/Y47D3XcySabhiadlZaDKPg==/*****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"*****************","avatarImgIdStr":"*****************","avatarImgId_str":"*****************"},"name":"jojo英语","picUrl":"http://p1.music.126.net/ozFZ990IqwTVBhV0ejPSZw==/****************.jpg","desc":"加油！","subCount":0,"programCount":1,"createTime":*************,"categoryId":13,"category":"外语世界","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"英语单词","lastProgramId":*********,"picId":****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":610000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/mRDNZkCgJMIMq5hINgm9oQ==/********4152597603.jpg","accountStatus":0,"gender":2,"city":610100,"birthday":************,"userId":*********,"userType":0,"nickname":"JoooQueen","signature":"underground电子","description":"","detailDescription":"","avatarImgId":********4152597600,"backgroundImgId":********2801531380,"backgroundUrl":"http://p1.music.126.net/4KelA7qNHa9fwBu-2RhXOQ==/********2801531371.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********2801531371","avatarImgIdStr":"********4152597603","avatarImgId_str":"********4152597603"},"name":"Jojo demo","picUrl":"http://p1.music.126.net/fyauej5Jf8OYIvMhW39EHA==/****************.jpg","desc":"recording","subCount":2,"programCount":5,"createTime":*************,"categoryId":10002,"category":"3D|电子","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"心如止水（Cover：Ice Paper）","lastProgramId":**********,"picId":****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":330000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/Ib2wkjT_Nq7C5WnzInMd4A==/********3104139060.jpg","accountStatus":0,"gender":1,"city":330100,"birthday":-*************,"userId":*********,"userType":0,"nickname":"朱侨侨","signature":"朱侨侨","description":"","detailDescription":"","avatarImgId":********3104139060,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"********3104139060","avatarImgId_str":"********3104139060"},"name":"朱侨侨JOJO粤语精选","picUrl":"http://p1.music.126.net/YjTSq29g9VlBYyQ__tu2UQ==/********3104459868.jpg","desc":"朱侨侨JOJO粤语精选","subCount":0,"programCount":27,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"大海(粤)A","lastProgramId":**********,"picId":********3104459870,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":310000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/ATepZVgjdmcVi5uGQz935Q==/********3040276187.jpg","accountStatus":0,"gender":2,"city":310107,"birthday":************,"userId":*********,"userType":0,"nickname":"JoJoChen_Miraesol","signature":"【Miraesol·蜜芮斯】#纯韩打造\u2022皮肤管理私人定制##瘦身塑形，光学美肤，韩式半永久，日韩风美甲等#","description":"","detailDescription":"","avatarImgId":********3040276200,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/45Nu4EqvFqK_kQj6BkPwcw==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":10,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"********3040276187","avatarImgId_str":"********3040276187"},"name":"JoJo MiraeSol","picUrl":"http://p1.music.126.net/mDsVkVl8lZp0UAt6Oge8tA==/********3518831979.jpg","desc":"JoJo MiraeSol Original Mix, Deep House/Techno/Future Bass，EDM爱好者","subCount":1,"programCount":1,"createTime":*************,"categoryId":2,"category":"音乐故事","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"JoJo Track01","lastProgramId":**********,"picId":********3518831980,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":330000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/Ib2wkjT_Nq7C5WnzInMd4A==/********3104139060.jpg","accountStatus":0,"gender":1,"city":330100,"birthday":-*************,"userId":*********,"userType":0,"nickname":"朱侨侨","signature":"朱侨侨","description":"","detailDescription":"","avatarImgId":********3104139060,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"********3104139060","avatarImgId_str":"********3104139060"},"name":"朱侨侨JOJO英文歌","picUrl":"http://p1.music.126.net/7WOAp4oUQfToqsFRtyzelQ==/********3108230045.jpg","desc":"朱侨侨JOJO英文歌","subCount":0,"programCount":8,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"take me to your heart","lastProgramId":**********,"picId":********3108230050,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":430000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/qaLiTMRpL0gWd7y1YE55eQ==/********2977710400.jpg","accountStatus":0,"gender":0,"city":430100,"birthday":-*************,"userId":*********,"userType":0,"nickname":"戌戌戌戌","signature":"","description":"","detailDescription":"","avatarImgId":********2977710400,"backgroundImgId":********2868128400,"backgroundUrl":"http://p1.music.126.net/2zSNIqTcpHL2jIvU6hG0EA==/********2868128395.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********2868128395","avatarImgIdStr":"********2977710400","avatarImgId_str":"********2977710400"},"name":"好梦喔jojo~","picUrl":"http://p1.music.126.net/yJukjCAY3iwUZZO_V8hUFw==/*****************.jpg","desc":"私人电台","subCount":1,"programCount":4,"createTime":*************,"categoryId":10001,"category":"有声书","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"看海的人 partlV","lastProgramId":*********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":130000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/r2ekCQFf7GmzAJv7sqKhqQ==/********4146053446.jpg","accountStatus":0,"gender":1,"city":130700,"birthday":************,"userId":*********,"userType":0,"nickname":"乔壹-Jojo","signature":"\"   我的成熟都是假正经 我的幼稚才是真性情         \"","description":"","detailDescription":"","avatarImgId":********4146053440,"backgroundImgId":********4146055120,"backgroundUrl":"http://p1.music.126.net/MVGRJ53DW5D68k8eWZKY7A==/********4146055119.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"backgroundImgIdStr":"********4146055119","avatarImgIdStr":"********4146053446","avatarImgId_str":"********4146053446"},"name":"乔壹-Jojo.","picUrl":"http://p1.music.126.net/WvUcsVXL-v-FolQI25UCOg==/********4113158079.jpg","desc":"只在夜深人静时 独自欢言 独自悲伤。","subCount":2,"programCount":1,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"残酷月光.mp3","lastProgramId":**********,"picId":********4113158080,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":310000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/cPLbIJrs-FsR439NAhaylQ==/********3230882525.jpg","accountStatus":0,"gender":1,"city":310101,"birthday":************,"userId":*********,"userType":0,"nickname":"中科院网络回复研究所教授","signature":"Drop database","description":"","detailDescription":"","avatarImgId":********3230882530,"backgroundImgId":********2879219090,"backgroundUrl":"http://p1.music.126.net/c2ec2TeixKn7R70DJNztYQ==/********2879219085.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"backgroundImgIdStr":"********2879219085","avatarImgIdStr":"********3230882525","avatarImgId_str":"********3230882525"},"name":"DJ JoJo not in your house","picUrl":"http://p1.music.126.net/v54OY4t6SrLfFuxuCauxwQ==/********3751954965.jpg","desc":"Where is JoJo","subCount":4,"programCount":2,"createTime":*************,"categoryId":10002,"category":"3D|电子","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Viral Hip Hop 2019 1 5, 2:47 PM","lastProgramId":**********,"picId":********3751954960,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":330000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/PquOjv5vBCX-4OL4BVzFYw==/********3427992784.jpg","accountStatus":0,"gender":0,"city":330100,"birthday":-*************,"userId":*********,"userType":0,"nickname":"JoJo-MIEMIE","signature":"","description":"","detailDescription":"","avatarImgId":********3427992780,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/bmA_ablsXpq3Tk9HlEg9sA==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"********3427992784","avatarImgId_str":"********3427992784"},"name":"《解忧杂货铺》\u2014\u2014JoJo","picUrl":"http://p1.music.126.net/Epd9V9w62HCNHBkmPJ6L3Q==/*****************.jpg","desc":"念念不忘，必有回响","subCount":1,"programCount":1,"createTime":*************,"categoryId":10001,"category":"有声书","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"解忧杂货铺","lastProgramId":*********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":310000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/91YOtypJ9GSJ5CA5IF_yeQ==/********3094247631.jpg","accountStatus":0,"gender":2,"city":310101,"birthday":-*************,"userId":*********,"userType":4,"nickname":"Jojo卓嬌","signature":"","description":"","detailDescription":"","avatarImgId":********3094247630,"backgroundImgId":********4087201490,"backgroundUrl":"http://p1.music.126.net/L5FFpu8JPxfzaW5OK7YvIg==/********4087201485.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********4087201485","avatarImgIdStr":"********3094247631","avatarImgId_str":"********3094247631"},"name":"我的demo作品和早期作品","picUrl":"http://p1.music.126.net/4cIAVFCmBABTnanCsILDgw==/********4103321056.jpg","desc":"早期作品 2015~2016\n自写自编自弹唱\n啊哈哈","subCount":2,"programCount":10,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"想说爱你xxxxxxxxx","lastProgramId":**********,"picId":********4103321060,"rcmdText":null,"composeVideo":false,"shareCount":2,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":640000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/HDXqUcWvKP_LsiVBIqyRCg==/********3985759766.jpg","accountStatus":0,"gender":1,"city":640100,"birthday":************,"userId":*********,"userType":0,"nickname":"JOJO718","signature":"I wish you hell","description":"","detailDescription":"","avatarImgId":********3985759760,"backgroundImgId":********3944340420,"backgroundUrl":"http://p1.music.126.net/pGHzsWnI8NmRlO5omTcE5Q==/********3944340413.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********3944340413","avatarImgIdStr":"********3985759766","avatarImgId_str":"********3985759766"},"name":"/null","picUrl":"http://p1.music.126.net/6_l_yQNpBA73w0CeIKK9aw==/*****************.jpg","desc":"网易云没有的","subCount":4,"programCount":6,"createTime":*************,"categoryId":2,"category":"音乐故事","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Not For U","lastProgramId":**********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":310000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/91YOtypJ9GSJ5CA5IF_yeQ==/********3094247631.jpg","accountStatus":0,"gender":2,"city":310101,"birthday":-*************,"userId":*********,"userType":4,"nickname":"Jojo卓嬌","signature":"","description":"","detailDescription":"","avatarImgId":********3094247630,"backgroundImgId":********4087201490,"backgroundUrl":"http://p1.music.126.net/L5FFpu8JPxfzaW5OK7YvIg==/********4087201485.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********4087201485","avatarImgIdStr":"********3094247631","avatarImgId_str":"********3094247631"},"name":"我的cover翻唱","picUrl":"http://p1.music.126.net/3yYCB4EFIc6RrpVpAoGnZg==/********3079604074.jpg","desc":"cover cover 改编翻唱！ yay! \n@JoJo卓嬌","subCount":3,"programCount":1,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"有一个姑娘 cover","lastProgramId":*********,"picId":********3079604080,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":310000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/ayLz8rDZHxwqUpTSMsxteg==/********3464243249.jpg","accountStatus":0,"gender":2,"city":310101,"birthday":************,"userId":**********,"userType":4,"nickname":"阿舟JoJo","signature":"深存感恩之心，又独自远行。","description":"","detailDescription":"","avatarImgId":********3464243250,"backgroundImgId":********3365635400,"backgroundUrl":"http://p1.music.126.net/tW-2G1xXJmDtDDcQvAZaIg==/********3365635399.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********3365635399","avatarImgIdStr":"********3464243249","avatarImgId_str":"********3464243249"},"name":"阿舟爱唱歌","picUrl":"http://p1.music.126.net/g9IsJ0cec64jQ8i7536iXQ==/********3464233628.jpg","desc":"与君歌一曲，请君为我亲耳听。","subCount":2,"programCount":2,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"永不失联的爱（cover.胖胖胖&陈以桐）","lastProgramId":**********,"picId":********3464233630,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":520000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/p8aeGIUWzJA_qO7bx9TAYg==/****************.jpg","accountStatus":0,"gender":2,"city":522700,"birthday":-*************,"userId":*********,"userType":0,"nickname":"JOJO于终","signature":"明天会更好。。。。。","description":"","detailDescription":"","avatarImgId":****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/45Nu4EqvFqK_kQj6BkPwcw==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"****************"},"name":"不改了℃","picUrl":"http://p1.music.126.net/hJPPYQplvat8USXsLtvU7Q==/*****************.jpg","desc":"每个人都有一个怀念，而你是我最怀念的。。","subCount":0,"programCount":1,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":0,"lastProgramName":null,"lastProgramId":0,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":320000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/bSGjyGXZ6WLG2tEPjnT3OQ==/*****************.jpg","accountStatus":0,"gender":2,"city":320500,"birthday":-*************,"userId":*********,"userType":0,"nickname":"COCOMJOJO","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":*****************,"backgroundUrl":"http://p1.music.126.net/963gREPWXv0Y4_MXZhWh7g==/*****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"backgroundImgIdStr":"*****************","avatarImgIdStr":"*****************","avatarImgId_str":"*****************"},"name":"living history","picUrl":"http://p1.music.126.net/WjA-z3-p5xAyeZf3ovu3sQ==/*****************.jpg","desc":"练习口语，娱乐大家","subCount":1,"programCount":11,"createTime":*************,"categoryId":13,"category":"外语世界","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Waiting For My Guitar","lastProgramId":**********,"picId":*****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":440000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/4vELljDrRWFL3yWEEWpRIQ==/****************.jpg","accountStatus":0,"gender":1,"city":442000,"birthday":************,"userId":*********,"userType":0,"nickname":"Jojo23333","signature":"LIVE for LIFE. HUSTLE EVERYDAY.  | 📍GZC | 公众号 / 网易云：Jojo23333","description":"","detailDescription":"","avatarImgId":****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/ZWBIFRgBLNPYwjaFy0V3yw==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"****************"},"name":"各种各样的歌。","picUrl":"http://p1.music.126.net/t9JRIJc1vzibo5fFNNC7tg==/****************.jpg","desc":"#慢慢更新\n点进去可以找到节目包含的歌曲\n只是想分享那些歌再加自己的想法","subCount":3,"programCount":1,"createTime":*************,"categoryId":2,"category":"音乐故事","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":*************,"lastProgramName":"Ronald Jenkees 即兴电音鬼才","lastProgramId":*********,"picId":****************,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0},{"id":*********,"dj":{"defaultAvatar":false,"province":210000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/7-loMVI1FsyL6sDskcY7aw==/********4132416692.jpg","accountStatus":0,"gender":1,"city":210100,"birthday":************,"userId":*********,"userType":0,"nickname":"佳男JoJo","signature":"唱作歌手","description":"","detailDescription":"","avatarImgId":********4132416690,"backgroundImgId":********4132416720,"backgroundUrl":"http://p1.music.126.net/T9hMnuzWTbWChpqNex1h8g==/********4132416718.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"********4132416718","avatarImgIdStr":"********4132416692","avatarImgId_str":"********4132416692"},"name":"佳男","picUrl":"http://p1.music.126.net/cglhK3acv0AP4rRuiyMO6A==/********3568877202.jpg","desc":"我的翻唱曲库","subCount":63,"programCount":1,"createTime":*************,"categoryId":2001,"category":"创作|翻唱","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":**********648,"lastProgramName":"只要有你的地方(cover林俊杰) cr ladyttt","lastProgramId":2056691901,"picId":********3568877200,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic","commentCount":0}]
         * djRadiosCount : 30
         */

        private int djRadiosCount;
        private List<DjRadiosBean> djRadios;

        public int getDjRadiosCount() {
            return djRadiosCount;
        }

        public void setDjRadiosCount(int djRadiosCount) {
            this.djRadiosCount = djRadiosCount;
        }

        public List<DjRadiosBean> getDjRadios() {
            return djRadios;
        }

        public void setDjRadios(List<DjRadiosBean> djRadios) {
            this.djRadios = djRadios;
        }

        public static class DjRadiosBean {
            /**
             * id : *********
             * dj : {"defaultAvatar":false,"province":130000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/PO8TsjfLTdbG-igdGFSBLw==/********3602792360.jpg","accountStatus":0,"gender":1,"city":130200,"birthday":************,"userId":********,"userType":0,"nickname":"郭-小嘉","signature":"名士文可公","description":"","detailDescription":"","avatarImgId":********3602792350,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"backgroundImgIdStr":"****************","avatarImgIdStr":"********3602792360","avatarImgId_str":"********3602792360"}
             * name : JOJO的奇妙冒险·天堂之眼bgm集
             * picUrl : http://p1.music.126.net/1clU9O807xMbCTKHUarG9w==/********3120085182.jpg
             * desc : 因为抽风和补档，顺序可能有点问题大家见谅
             * subCount : 1029
             * programCount : 57
             * createTime : *************
             * categoryId : 3001
             * category : 二次元
             * radioFeeType : 0
             * feeScope : 0
             * buyed : false
             * videos : null
             * finished : false
             * underShelf : false
             * purchaseCount : 0
             * price : 0
             * originalPrice : 0
             * discountPrice : null
             * lastProgramCreateTime : *************
             * lastProgramName : EOH·角色选择
             * lastProgramId : **********
             * picId : ********3120085180
             * rcmdText : null
             * composeVideo : false
             * shareCount : 0
             * rcmdtext : null
             * likedCount : 0
             * alg : alg_djradio_basic
             * commentCount : 0
             */

            private long id;
            private DjBean dj;
            private String name;
            private String picUrl;
            private String desc;
            private int subCount;
            private int programCount;
            private long createTime;
            private int categoryId;
            private String category;
            private int radioFeeType;
            private int feeScope;
            private boolean buyed;
            private Object videos;
            private boolean finished;
            private boolean underShelf;
            private int purchaseCount;
            private int price;
            private int originalPrice;
            private Object discountPrice;
            private long lastProgramCreateTime;
            private String lastProgramName;
            private int lastProgramId;
            private long picId;
            private Object rcmdText;
            private boolean composeVideo;
            private int shareCount;
            private Object rcmdtext;
            private int likedCount;
            private String alg;
            private int commentCount;

            public long getId() {
                return id;
            }

            public void setId(long id) {
                this.id = id;
            }

            public DjBean getDj() {
                return dj;
            }

            public void setDj(DjBean dj) {
                this.dj = dj;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getPicUrl() {
                return picUrl;
            }

            public void setPicUrl(String picUrl) {
                this.picUrl = picUrl;
            }

            public String getDesc() {
                return desc;
            }

            public void setDesc(String desc) {
                this.desc = desc;
            }

            public int getSubCount() {
                return subCount;
            }

            public void setSubCount(int subCount) {
                this.subCount = subCount;
            }

            public int getProgramCount() {
                return programCount;
            }

            public void setProgramCount(int programCount) {
                this.programCount = programCount;
            }

            public long getCreateTime() {
                return createTime;
            }

            public void setCreateTime(long createTime) {
                this.createTime = createTime;
            }

            public int getCategoryId() {
                return categoryId;
            }

            public void setCategoryId(int categoryId) {
                this.categoryId = categoryId;
            }

            public String getCategory() {
                return category;
            }

            public void setCategory(String category) {
                this.category = category;
            }

            public int getRadioFeeType() {
                return radioFeeType;
            }

            public void setRadioFeeType(int radioFeeType) {
                this.radioFeeType = radioFeeType;
            }

            public int getFeeScope() {
                return feeScope;
            }

            public void setFeeScope(int feeScope) {
                this.feeScope = feeScope;
            }

            public boolean isBuyed() {
                return buyed;
            }

            public void setBuyed(boolean buyed) {
                this.buyed = buyed;
            }

            public Object getVideos() {
                return videos;
            }

            public void setVideos(Object videos) {
                this.videos = videos;
            }

            public boolean isFinished() {
                return finished;
            }

            public void setFinished(boolean finished) {
                this.finished = finished;
            }

            public boolean isUnderShelf() {
                return underShelf;
            }

            public void setUnderShelf(boolean underShelf) {
                this.underShelf = underShelf;
            }

            public int getPurchaseCount() {
                return purchaseCount;
            }

            public void setPurchaseCount(int purchaseCount) {
                this.purchaseCount = purchaseCount;
            }

            public int getPrice() {
                return price;
            }

            public void setPrice(int price) {
                this.price = price;
            }

            public int getOriginalPrice() {
                return originalPrice;
            }

            public void setOriginalPrice(int originalPrice) {
                this.originalPrice = originalPrice;
            }

            public Object getDiscountPrice() {
                return discountPrice;
            }

            public void setDiscountPrice(Object discountPrice) {
                this.discountPrice = discountPrice;
            }

            public long getLastProgramCreateTime() {
                return lastProgramCreateTime;
            }

            public void setLastProgramCreateTime(long lastProgramCreateTime) {
                this.lastProgramCreateTime = lastProgramCreateTime;
            }

            public String getLastProgramName() {
                return lastProgramName;
            }

            public void setLastProgramName(String lastProgramName) {
                this.lastProgramName = lastProgramName;
            }

            public int getLastProgramId() {
                return lastProgramId;
            }

            public void setLastProgramId(int lastProgramId) {
                this.lastProgramId = lastProgramId;
            }

            public long getPicId() {
                return picId;
            }

            public void setPicId(long picId) {
                this.picId = picId;
            }

            public Object getRcmdText() {
                return rcmdText;
            }

            public void setRcmdText(Object rcmdText) {
                this.rcmdText = rcmdText;
            }

            public boolean isComposeVideo() {
                return composeVideo;
            }

            public void setComposeVideo(boolean composeVideo) {
                this.composeVideo = composeVideo;
            }

            public int getShareCount() {
                return shareCount;
            }

            public void setShareCount(int shareCount) {
                this.shareCount = shareCount;
            }

            public Object getRcmdtext() {
                return rcmdtext;
            }

            public void setRcmdtext(Object rcmdtext) {
                this.rcmdtext = rcmdtext;
            }

            public int getLikedCount() {
                return likedCount;
            }

            public void setLikedCount(int likedCount) {
                this.likedCount = likedCount;
            }

            public String getAlg() {
                return alg;
            }

            public void setAlg(String alg) {
                this.alg = alg;
            }

            public int getCommentCount() {
                return commentCount;
            }

            public void setCommentCount(int commentCount) {
                this.commentCount = commentCount;
            }

            public static class DjBean {
                /**
                 * defaultAvatar : false
                 * province : 130000
                 * authStatus : 0
                 * followed : false
                 * avatarUrl : http://p1.music.126.net/PO8TsjfLTdbG-igdGFSBLw==/********3602792360.jpg
                 * accountStatus : 0
                 * gender : 1
                 * city : 130200
                 * birthday : ************
                 * userId : ********
                 * userType : 0
                 * nickname : 郭-小嘉
                 * signature : 名士文可公
                 * description :
                 * detailDescription :
                 * avatarImgId : ********3602792350
                 * backgroundImgId : ****************
                 * backgroundUrl : http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg
                 * authority : 0
                 * mutual : false
                 * expertTags : null
                 * experts : null
                 * djStatus : 10
                 * vipType : 0
                 * remarkName : null
                 * backgroundImgIdStr : ****************
                 * avatarImgIdStr : ********3602792360
                 * avatarImgId_str : ********3602792360
                 */

                private boolean defaultAvatar;
                private int province;
                private int authStatus;
                private boolean followed;
                private String avatarUrl;
                private int accountStatus;
                private int gender;
                private int city;
                private long birthday;
                private int userId;
                private int userType;
                private String nickname;
                private String signature;
                private String description;
                private String detailDescription;
                private long avatarImgId;
                private long backgroundImgId;
                private String backgroundUrl;
                private int authority;
                private boolean mutual;
                private Object expertTags;
                private Object experts;
                private int djStatus;
                private int vipType;
                private Object remarkName;
                private String backgroundImgIdStr;
                private String avatarImgIdStr;
                private String avatarImgId_str;

                public boolean isDefaultAvatar() {
                    return defaultAvatar;
                }

                public void setDefaultAvatar(boolean defaultAvatar) {
                    this.defaultAvatar = defaultAvatar;
                }

                public int getProvince() {
                    return province;
                }

                public void setProvince(int province) {
                    this.province = province;
                }

                public int getAuthStatus() {
                    return authStatus;
                }

                public void setAuthStatus(int authStatus) {
                    this.authStatus = authStatus;
                }

                public boolean isFollowed() {
                    return followed;
                }

                public void setFollowed(boolean followed) {
                    this.followed = followed;
                }

                public String getAvatarUrl() {
                    return avatarUrl;
                }

                public void setAvatarUrl(String avatarUrl) {
                    this.avatarUrl = avatarUrl;
                }

                public int getAccountStatus() {
                    return accountStatus;
                }

                public void setAccountStatus(int accountStatus) {
                    this.accountStatus = accountStatus;
                }

                public int getGender() {
                    return gender;
                }

                public void setGender(int gender) {
                    this.gender = gender;
                }

                public int getCity() {
                    return city;
                }

                public void setCity(int city) {
                    this.city = city;
                }

                public long getBirthday() {
                    return birthday;
                }

                public void setBirthday(long birthday) {
                    this.birthday = birthday;
                }

                public int getUserId() {
                    return userId;
                }

                public void setUserId(int userId) {
                    this.userId = userId;
                }

                public int getUserType() {
                    return userType;
                }

                public void setUserType(int userType) {
                    this.userType = userType;
                }

                public String getNickname() {
                    return nickname;
                }

                public void setNickname(String nickname) {
                    this.nickname = nickname;
                }

                public String getSignature() {
                    return signature;
                }

                public void setSignature(String signature) {
                    this.signature = signature;
                }

                public String getDescription() {
                    return description;
                }

                public void setDescription(String description) {
                    this.description = description;
                }

                public String getDetailDescription() {
                    return detailDescription;
                }

                public void setDetailDescription(String detailDescription) {
                    this.detailDescription = detailDescription;
                }

                public long getAvatarImgId() {
                    return avatarImgId;
                }

                public void setAvatarImgId(long avatarImgId) {
                    this.avatarImgId = avatarImgId;
                }

                public long getBackgroundImgId() {
                    return backgroundImgId;
                }

                public void setBackgroundImgId(long backgroundImgId) {
                    this.backgroundImgId = backgroundImgId;
                }

                public String getBackgroundUrl() {
                    return backgroundUrl;
                }

                public void setBackgroundUrl(String backgroundUrl) {
                    this.backgroundUrl = backgroundUrl;
                }

                public int getAuthority() {
                    return authority;
                }

                public void setAuthority(int authority) {
                    this.authority = authority;
                }

                public boolean isMutual() {
                    return mutual;
                }

                public void setMutual(boolean mutual) {
                    this.mutual = mutual;
                }

                public Object getExpertTags() {
                    return expertTags;
                }

                public void setExpertTags(Object expertTags) {
                    this.expertTags = expertTags;
                }

                public Object getExperts() {
                    return experts;
                }

                public void setExperts(Object experts) {
                    this.experts = experts;
                }

                public int getDjStatus() {
                    return djStatus;
                }

                public void setDjStatus(int djStatus) {
                    this.djStatus = djStatus;
                }

                public int getVipType() {
                    return vipType;
                }

                public void setVipType(int vipType) {
                    this.vipType = vipType;
                }

                public Object getRemarkName() {
                    return remarkName;
                }

                public void setRemarkName(Object remarkName) {
                    this.remarkName = remarkName;
                }

                public String getBackgroundImgIdStr() {
                    return backgroundImgIdStr;
                }

                public void setBackgroundImgIdStr(String backgroundImgIdStr) {
                    this.backgroundImgIdStr = backgroundImgIdStr;
                }

                public String getAvatarImgIdStr() {
                    return avatarImgIdStr;
                }

                public void setAvatarImgIdStr(String avatarImgIdStr) {
                    this.avatarImgIdStr = avatarImgIdStr;
                }

                public String getAvatarImgId_str() {
                    return avatarImgId_str;
                }

                public void setAvatarImgId_str(String avatarImgId_str) {
                    this.avatarImgId_str = avatarImgId_str;
                }
            }
        }
    }
}
