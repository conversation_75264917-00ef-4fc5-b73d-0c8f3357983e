<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/base_drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_white"
    android:orientation="vertical">


    <RelativeLayout
        android:id="@+id/content_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_white">

        <FrameLayout
            android:id="@+id/frame_base"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <!--<com.imooc.lib_audio.mediaplayer.view.BottomMusicView
             android:id="@+id/bottom_view"
             android:layout_width="match_parent"
             android:layout_height="wrap_content"
             android:layout_alignParentBottom="true" />-->

    </RelativeLayout>


</LinearLayout>