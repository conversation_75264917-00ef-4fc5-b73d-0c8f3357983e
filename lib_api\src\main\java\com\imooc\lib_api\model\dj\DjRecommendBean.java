package com.imooc.lib_api.model.dj;

import java.util.List;

/**
 * 推荐电台Bean
 */
public class DjRecommendBean {

    /**
     * djRadios : [{"id":*********,"name":"人大之声·中国人民大学广播台","picUrl":"https://p2.music.126.net/-S1IuLJjE-ZnRSC6zlNNxg==/*****************.jpg","programCount":0,"subCount":5292,"createTime":*************,"categoryId":4001,"category":"校园|教育","rcmdtext":"新学期人大之声，继续为你发声","radioFeeType":0,"feeScope":0,"subed":false,"dj":{"defaultAvatar":false,"province":110000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/IOxEY62eNu_8N9rF_QG7yQ==/****************.jpg","accountStatus":0,"gender":2,"city":110101,"birthday":************,"userId":*********,"userType":10,"nickname":"中国人民大学广播台","signature":"中国人民大学·人大之声广播台，首都高校知名有声媒体","description":"中国人民大学·人大之声广播台","detailDescription":"中国人民大学·人大之声广播台","avatarImgId":****************,"backgroundImgId":109951164351777780,"backgroundUrl":"http://p1.music.126.net/0fD9Aivt2M3ov-3HbtW-iQ==/109951164351777775.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"****************","backgroundImgIdStr":"109951164351777775"},"copywriter":"中国人民大学广播电台","buyed":false},{"id":243,"name":"米莉.听见花开","picUrl":"https://p2.music.126.net/N2KPJFDoELdsHmJodMdVTg==/*****************.jpg","programCount":0,"subCount":386786,"createTime":*************,"categoryId":3,"category":"情感调频","rcmdtext":"一个让你念念不忘的声音","radioFeeType":0,"feeScope":0,"subed":false,"dj":{"defaultAvatar":false,"province":330000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/tqsAJuniUY8u-Ggye-yGbg==/109951162872872904.jpg","accountStatus":0,"gender":2,"city":330100,"birthday":*************,"userId":3099052,"userType":1,"nickname":"米莉姑娘","signature":"","description":"悠米音悦台《越夜越美丽》主持人","detailDescription":"悠米音悦台《越夜越美丽》主持人","avatarImgId":109951162872872900,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/PONIr-Fe9dYpsAYNDsLQnQ==/****************.jpg","authority":3,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"avatarImgIdStr":"109951162872872904","backgroundImgIdStr":"****************","avatarImgId_str":"109951162872872904"},"copywriter":"一个让你念念不忘的人","buyed":false},{"id":*********,"name":"观复嘟嘟","picUrl":"https://p2.music.126.net/4mwC7MujiFHqmQozCKHDtg==/109951163068582793.jpg","programCount":0,"subCount":58498,"createTime":*************,"categoryId":5,"category":"脱口秀","rcmdtext":"和马未都一起把酒话乾坤","radioFeeType":0,"feeScope":0,"subed":false,"dj":{"defaultAvatar":false,"province":110000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/kt28NzM9nyoGOiHuCNqFYg==/109951163066672956.jpg","accountStatus":0,"gender":1,"city":110101,"birthday":-************,"userId":**********,"userType":0,"nickname":"马未都","signature":"文化学者，观复博物馆创办人，脱口秀《观复嘟嘟》主讲人。","description":"","detailDescription":"","avatarImgId":109951163066672960,"backgroundImgId":109951162868126480,"backgroundUrl":"http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951163066672956","backgroundImgIdStr":"109951162868126486","avatarImgId_str":"109951163066672956"},"copywriter":"和马未都一起把酒话乾坤","buyed":false},{"id":*********,"name":"曹云金相声大全","picUrl":"https://p2.music.126.net/t8RzyUCVu5kszDzqewe_BA==/*****************.jpg","programCount":0,"subCount":40738,"createTime":*************,"categoryId":8,"category":"相声曲艺","rcmdtext":"曹云金为你表演不一样的说学逗唱","radioFeeType":0,"feeScope":0,"subed":false,"dj":{"defaultAvatar":false,"province":110000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/hAkSR8UYmvgVRK_LMUbO2w==/*****************.jpg","accountStatus":0,"gender":1,"city":110101,"birthday":-*************,"userId":*********,"userType":2,"nickname":"听云轩演员曹云金","signature":"曹云金，原名曹金，1986年1月26日出生于天津，相声演员。","description":"听云轩著名相声演员曹云金","detailDescription":"听云轩著名相声演员曹云金","avatarImgId":*****************,"backgroundImgId":109951162868126480,"backgroundUrl":"http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"*****************","backgroundImgIdStr":"109951162868126486","avatarImgId_str":"*****************"},"copywriter":"曹云金为你表演不一样的说学逗唱","buyed":false},{"id":*********,"name":"跟seven一起听歌学单词","picUrl":"https://p2.music.126.net/IB-9kHDvUWo7DqdBVjRz2w==/*****************.jpg","programCount":0,"subCount":66340,"createTime":*************,"categoryId":13,"category":"外语世界","rcmdtext":"听着歌学英文","radioFeeType":0,"feeScope":0,"subed":false,"dj":{"defaultAvatar":false,"province":110000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/qkA4We8bbXgWas4i77n90Q==/109951164042674441.jpg","accountStatus":0,"gender":2,"city":110101,"birthday":************,"userId":********,"userType":0,"nickname":"se7en娜娜酱","signature":"云音乐电台：跟seven一起听歌学单词 b站：seven娜娜酱","description":"","detailDescription":"","avatarImgId":109951164042674450,"backgroundImgId":109951163822800110,"backgroundUrl":"http://p1.music.126.net/7BV0-TwlX_swgNhxHXXAAg==/109951163822800112.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"avatarImgIdStr":"109951164042674441","backgroundImgIdStr":"109951163822800112","avatarImgId_str":"109951164042674441"},"copywriter":"逗趣主播带你一起听歌学单词","buyed":false},{"id":*********,"name":"5分钟职场商学院","picUrl":"https://p2.music.126.net/BsQXTWuEJ7h0eyXZyIRYFQ==/*****************.jpg","programCount":0,"subCount":25594,"createTime":*************,"categoryId":453051,"category":"商业财经","rcmdtext":"解锁商业实战新技能","radioFeeType":0,"feeScope":0,"subed":false,"dj":{"defaultAvatar":false,"province":110000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/0jqOeq0jmez42cXMEIorUw==/109951164065780042.jpg","accountStatus":0,"gender":1,"city":110101,"birthday":-*************,"userId":*********,"userType":0,"nickname":"胡海平","signature":"","description":"","detailDescription":"","avatarImgId":109951164065780050,"backgroundImgId":109951162868128400,"backgroundUrl":"http://p1.music.126.net/2zSNIqTcpHL2jIvU6hG0EA==/109951162868128395.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951164065780042","backgroundImgIdStr":"109951162868128395","avatarImgId_str":"109951164065780042"},"copywriter":"解锁商业实战新技能","buyed":false},{"id":*********,"name":"斑鸠学院","picUrl":"https://p2.music.126.net/-maBHYpDRbDhrkAWC5xweA==/*****************.jpg","programCount":0,"subCount":27937,"createTime":*************,"categoryId":453050,"category":"知识技能","rcmdtext":"趣听职场，天天向上","radioFeeType":0,"feeScope":0,"subed":false,"dj":{"defaultAvatar":false,"province":350000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/4w-AqtxLWdqat8HEc2paMg==/*****************.jpg","accountStatus":0,"gender":1,"city":350200,"birthday":-*************,"userId":*********,"userType":10,"nickname":"斑鸠职业APP","signature":"关注公众号：【斑鸠课堂】 百位职场大咖助你擦亮梦想的翅膀，发现另一个更好的自己。","description":"斑鸠学院官方账号","detailDescription":"斑鸠学院官方账号","avatarImgId":*****************,"backgroundImgId":109951162868126480,"backgroundUrl":"http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"*****************","backgroundImgIdStr":"109951162868126486","avatarImgId_str":"*****************"},"copywriter":"趣听职场，天天线上","buyed":false},{"id":*********,"name":"李光斗观察","picUrl":"https://p2.music.126.net/vnzkBrAYl2bStTDRdD7nXw==/*****************.jpg","programCount":0,"subCount":28693,"createTime":*************,"categoryId":453051,"category":"商业财经","rcmdtext":"央视品牌顾问的财经观察","radioFeeType":0,"feeScope":0,"subed":false,"dj":{"defaultAvatar":false,"province":110000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/nUEK7yjJZmzkRUMjsyH5dA==/*****************.jpg","accountStatus":0,"gender":1,"city":110101,"birthday":-*************,"userId":*********,"userType":10,"nickname":"李光斗","signature":"财经的娱乐解读，娱乐的财经观察，历史的财富新知。李光斗，中国品牌第一人、中央电视台品牌顾问以轻松的氛围，淋漓发挥其麻辣风格，撕财经的面纱，脱娱乐的内衣。","description":"中央电视台品牌顾问","detailDescription":"中央电视台品牌顾问","avatarImgId":*****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/45Nu4EqvFqK_kQj6BkPwcw==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"*****************","backgroundImgIdStr":"****************","avatarImgId_str":"*****************"},"copywriter":"央视品牌顾问的财经解读","buyed":false},{"id":*********,"name":"郭德纲精选相声","picUrl":"https://p2.music.126.net/dEsD-6e9zh2auRlBOQ6-Bg==/*****************.jpg","programCount":0,"subCount":238261,"createTime":*************,"categoryId":8,"category":"相声曲艺","rcmdtext":"床前明月光，我是郭德纲\t","radioFeeType":0,"feeScope":0,"subed":false,"dj":{"defaultAvatar":false,"province":110000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/-c5KO12D7sN4wNAYL5Hxtg==/*****************.jpg","accountStatus":0,"gender":1,"city":110101,"birthday":-*************,"userId":*********,"userType":0,"nickname":"郭德纲对口相声","signature":"郭德纲，男，出生于1973年1月18日，天津人，相声表演艺术家，电影、电视剧演员，电视脱口秀主持人。1979年投身艺坛，先拜评书前辈高庆海学习评书，后跟随相声名家常宝丰学相声，又师从相声大师侯耀文。其间又学习了京剧、评剧、河北梆子等剧种，辗转梨园多年。","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":109951162868126480,"backgroundUrl":"http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"*****************","backgroundImgIdStr":"109951162868126486","avatarImgId_str":"*****************"},"copywriter":"筛选郭德纲最经典的相声作品","buyed":false},{"id":*********,"name":"围炉夜话","picUrl":"https://p2.music.126.net/Zv8yTmOF5ugLybRLEkQJeQ==/*****************.jpg","programCount":0,"subCount":195430,"createTime":*************,"categoryId":11,"category":"人文历史","rcmdtext":"美食家陈立带你品美食、说历史","radioFeeType":0,"feeScope":0,"subed":false,"dj":{"defaultAvatar":false,"province":330000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/H3QxWdf0eUiwmhJvA4vrMQ==/****************.jpg","accountStatus":0,"gender":1,"city":330100,"birthday":-*************,"userId":*********,"userType":1,"nickname":"陈立","signature":"美食家；《舌尖上的中国》顾问之一；情感性精神疾病的教授。80年代中期到香港大学社会系攻读社会心理学专业，毕业后回杭州大学心理系教精神分析学，后来在历史系研究心理考古学。90年代起先后出任杭大和浙大台港澳研究机构的负责人。","description":"心理学家、美食家陈立教授","detailDescription":"心理学家、美食家陈立教授","avatarImgId":****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":3,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"avatarImgIdStr":"****************","backgroundImgIdStr":"****************"},"copywriter":"吃货的美食品鉴","buyed":false}]
     * name : 精选电台 - 订阅更精彩
     * code : 200
     */

    private String name;
    private int code;
    private List<DjRadiosBean> djRadios;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public List<DjRadiosBean> getDjRadios() {
        return djRadios;
    }

    public void setDjRadios(List<DjRadiosBean> djRadios) {
        this.djRadios = djRadios;
    }

    public static class DjRadiosBean {
        /**
         * id : *********
         * name : 人大之声·中国人民大学广播台
         * picUrl : https://p2.music.126.net/-S1IuLJjE-ZnRSC6zlNNxg==/*****************.jpg
         * programCount : 0
         * subCount : 5292
         * createTime : *************
         * categoryId : 4001
         * category : 校园|教育
         * rcmdtext : 新学期人大之声，继续为你发声
         * radioFeeType : 0
         * feeScope : 0
         * subed : false
         * dj : {"defaultAvatar":false,"province":110000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/IOxEY62eNu_8N9rF_QG7yQ==/****************.jpg","accountStatus":0,"gender":2,"city":110101,"birthday":************,"userId":*********,"userType":10,"nickname":"中国人民大学广播台","signature":"中国人民大学·人大之声广播台，首都高校知名有声媒体","description":"中国人民大学·人大之声广播台","detailDescription":"中国人民大学·人大之声广播台","avatarImgId":****************,"backgroundImgId":109951164351777780,"backgroundUrl":"http://p1.music.126.net/0fD9Aivt2M3ov-3HbtW-iQ==/109951164351777775.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"****************","backgroundImgIdStr":"109951164351777775"}
         * copywriter : 中国人民大学广播电台
         * buyed : false
         */

        private long id;
        private String name;
        private String picUrl;
        private int programCount;
        private int subCount;
        private long createTime;
        private int categoryId;
        private String category;
        private String rcmdtext;
        private int radioFeeType;
        private int feeScope;
        private boolean subed;
        private DjBean dj;
        private String copywriter;
        private boolean buyed;
        private int orginalPrice;

        @Override
        public String toString() {
            return "DjRadiosBean{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    ", picUrl='" + picUrl + '\'' +
                    ", programCount=" + programCount +
                    ", subCount=" + subCount +
                    ", createTime=" + createTime +
                    ", categoryId=" + categoryId +
                    ", category='" + category + '\'' +
                    ", rcmdtext='" + rcmdtext + '\'' +
                    ", radioFeeType=" + radioFeeType +
                    ", feeScope=" + feeScope +
                    ", subed=" + subed +
                    ", dj=" + dj +
                    ", copywriter='" + copywriter + '\'' +
                    ", buyed=" + buyed +
                    ", orginalPrice=" + orginalPrice +
                    '}';
        }

        public int getOrginalPrice() {
            return orginalPrice;
        }

        public void setOrginalPrice(int orginalPrice) {
            this.orginalPrice = orginalPrice;
        }

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPicUrl() {
            return picUrl;
        }

        public void setPicUrl(String picUrl) {
            this.picUrl = picUrl;
        }

        public int getProgramCount() {
            return programCount;
        }

        public void setProgramCount(int programCount) {
            this.programCount = programCount;
        }

        public int getSubCount() {
            return subCount;
        }

        public void setSubCount(int subCount) {
            this.subCount = subCount;
        }

        public long getCreateTime() {
            return createTime;
        }

        public void setCreateTime(long createTime) {
            this.createTime = createTime;
        }

        public int getCategoryId() {
            return categoryId;
        }

        public void setCategoryId(int categoryId) {
            this.categoryId = categoryId;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public String getRcmdtext() {
            return rcmdtext;
        }

        public void setRcmdtext(String rcmdtext) {
            this.rcmdtext = rcmdtext;
        }

        public int getRadioFeeType() {
            return radioFeeType;
        }

        public void setRadioFeeType(int radioFeeType) {
            this.radioFeeType = radioFeeType;
        }

        public int getFeeScope() {
            return feeScope;
        }

        public void setFeeScope(int feeScope) {
            this.feeScope = feeScope;
        }

        public boolean isSubed() {
            return subed;
        }

        public void setSubed(boolean subed) {
            this.subed = subed;
        }

        public DjBean getDj() {
            return dj;
        }

        public void setDj(DjBean dj) {
            this.dj = dj;
        }

        public String getCopywriter() {
            return copywriter;
        }

        public void setCopywriter(String copywriter) {
            this.copywriter = copywriter;
        }

        public boolean isBuyed() {
            return buyed;
        }

        public void setBuyed(boolean buyed) {
            this.buyed = buyed;
        }

        public static class DjBean {
            /**
             * defaultAvatar : false
             * province : 110000
             * authStatus : 1
             * followed : false
             * avatarUrl : http://p1.music.126.net/IOxEY62eNu_8N9rF_QG7yQ==/****************.jpg
             * accountStatus : 0
             * gender : 2
             * city : 110101
             * birthday : ************
             * userId : *********
             * userType : 10
             * nickname : 中国人民大学广播台
             * signature : 中国人民大学·人大之声广播台，首都高校知名有声媒体
             * description : 中国人民大学·人大之声广播台
             * detailDescription : 中国人民大学·人大之声广播台
             * avatarImgId : ****************
             * backgroundImgId : 109951164351777780
             * backgroundUrl : http://p1.music.126.net/0fD9Aivt2M3ov-3HbtW-iQ==/109951164351777775.jpg
             * authority : 0
             * mutual : false
             * expertTags : null
             * experts : null
             * djStatus : 10
             * vipType : 0
             * remarkName : null
             * avatarImgIdStr : ****************
             * backgroundImgIdStr : 109951164351777775
             */

            private boolean defaultAvatar;
            private int province;
            private int authStatus;
            private boolean followed;
            private String avatarUrl;
            private int accountStatus;
            private int gender;
            private int city;
            private long birthday;
            private int userId;
            private int userType;
            private String nickname;
            private String signature;
            private String description;
            private String detailDescription;
            private long avatarImgId;
            private long backgroundImgId;
            private String backgroundUrl;
            private int authority;
            private boolean mutual;
            private Object expertTags;
            private Object experts;
            private int djStatus;
            private int vipType;
            private Object remarkName;
            private String avatarImgIdStr;
            private String backgroundImgIdStr;

            public boolean isDefaultAvatar() {
                return defaultAvatar;
            }

            public void setDefaultAvatar(boolean defaultAvatar) {
                this.defaultAvatar = defaultAvatar;
            }

            public int getProvince() {
                return province;
            }

            public void setProvince(int province) {
                this.province = province;
            }

            public int getAuthStatus() {
                return authStatus;
            }

            public void setAuthStatus(int authStatus) {
                this.authStatus = authStatus;
            }

            public boolean isFollowed() {
                return followed;
            }

            public void setFollowed(boolean followed) {
                this.followed = followed;
            }

            public String getAvatarUrl() {
                return avatarUrl;
            }

            public void setAvatarUrl(String avatarUrl) {
                this.avatarUrl = avatarUrl;
            }

            public int getAccountStatus() {
                return accountStatus;
            }

            public void setAccountStatus(int accountStatus) {
                this.accountStatus = accountStatus;
            }

            public int getGender() {
                return gender;
            }

            public void setGender(int gender) {
                this.gender = gender;
            }

            public int getCity() {
                return city;
            }

            public void setCity(int city) {
                this.city = city;
            }

            public long getBirthday() {
                return birthday;
            }

            public void setBirthday(long birthday) {
                this.birthday = birthday;
            }

            public int getUserId() {
                return userId;
            }

            public void setUserId(int userId) {
                this.userId = userId;
            }

            public int getUserType() {
                return userType;
            }

            public void setUserType(int userType) {
                this.userType = userType;
            }

            public String getNickname() {
                return nickname;
            }

            public void setNickname(String nickname) {
                this.nickname = nickname;
            }

            public String getSignature() {
                return signature;
            }

            public void setSignature(String signature) {
                this.signature = signature;
            }

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public String getDetailDescription() {
                return detailDescription;
            }

            public void setDetailDescription(String detailDescription) {
                this.detailDescription = detailDescription;
            }

            public long getAvatarImgId() {
                return avatarImgId;
            }

            public void setAvatarImgId(long avatarImgId) {
                this.avatarImgId = avatarImgId;
            }

            public long getBackgroundImgId() {
                return backgroundImgId;
            }

            public void setBackgroundImgId(long backgroundImgId) {
                this.backgroundImgId = backgroundImgId;
            }

            public String getBackgroundUrl() {
                return backgroundUrl;
            }

            public void setBackgroundUrl(String backgroundUrl) {
                this.backgroundUrl = backgroundUrl;
            }

            public int getAuthority() {
                return authority;
            }

            public void setAuthority(int authority) {
                this.authority = authority;
            }

            public boolean isMutual() {
                return mutual;
            }

            public void setMutual(boolean mutual) {
                this.mutual = mutual;
            }

            public Object getExpertTags() {
                return expertTags;
            }

            public void setExpertTags(Object expertTags) {
                this.expertTags = expertTags;
            }

            public Object getExperts() {
                return experts;
            }

            public void setExperts(Object experts) {
                this.experts = experts;
            }

            public int getDjStatus() {
                return djStatus;
            }

            public void setDjStatus(int djStatus) {
                this.djStatus = djStatus;
            }

            public int getVipType() {
                return vipType;
            }

            public void setVipType(int vipType) {
                this.vipType = vipType;
            }

            public Object getRemarkName() {
                return remarkName;
            }

            public void setRemarkName(Object remarkName) {
                this.remarkName = remarkName;
            }

            public String getAvatarImgIdStr() {
                return avatarImgIdStr;
            }

            public void setAvatarImgIdStr(String avatarImgIdStr) {
                this.avatarImgIdStr = avatarImgIdStr;
            }

            public String getBackgroundImgIdStr() {
                return backgroundImgIdStr;
            }

            public void setBackgroundImgIdStr(String backgroundImgIdStr) {
                this.backgroundImgIdStr = backgroundImgIdStr;
            }
        }

    }
}
