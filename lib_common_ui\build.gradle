apply plugin: 'com.android.library'
apply plugin: 'com.jakewharton.butterknife'
android {
    compileSdkVersion rootProject.android.compileSdkVersion
    namespace 'com.imooc.lib_common_ui'

    defaultConfig {
        minSdkVersion rootProject.android.minSdkVersion
        targetSdkVersion rootProject.android.targetSdkVersion
        versionCode rootProject.android.versionCode
        versionName rootProject.android.versionName
        multiDexEnabled rootProject.android.multiDexEnabled

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    buildFeatures {
        viewBinding true
        dataBinding true
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api this.rootProject.depsLibs.butterknife
    api this.rootProject.depsLibs.support
    api this.rootProject.depsLibs.cvbanner
    api this.rootProject.depsLibs.fragmentation

    compileOnly this.rootProject.depsLibs.appcompat
    implementation this.rootProject.depsLibs.eventbus
    implementation this.rootProject.depsLibs.gson
    implementation this.rootProject.depsLibs.recyclerview
    implementation this.rootProject.depsLibs.magicindicator
    implementation this.rootProject.depsLibs.GroupedRecyclerViewAdapter
    implementation this.rootProject.depsLibs.BaseRecyclerViewAdapterHelper
    implementation this.rootProject.depsLibs.design
    implementation this.rootProject.depsLibs.multidex
    implementation this.rootProject.depsLibs.xpopup
    implementation this.rootProject.depsLibs.vptransforms
    implementation rootProject.depsLibs.glide
    annotationProcessor this.rootProject.depsLibs.butterknife_compiler

    compileOnly project(':lib_image_loader')
    implementation project(path: ':lib_api')
}
