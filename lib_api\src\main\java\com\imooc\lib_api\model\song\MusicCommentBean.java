package com.imooc.lib_api.model.song;

import java.util.List;

public class MusicCommentBean {


    /**
     * isMusician : false
     * cnum : 0
     * userId : 1908056098
     * topComments : []
     * moreHot : true
     * hotComments : [{"user":{"locationInfo":null,"liveInfo":null,"userId":548345,"nickname":"蛋蛋是圆D","authStatus":0,"avatarUrl":"https://p4.music.126.net/FwTFLzZFZOZO5WFL6-JRBg==/2755376139227068.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[],"pendantData":null,"showFloorComment":null,"status":0,"commentId":4956438,"content":"高一听的，那时候遇到了孩儿他妈，然后就这么幸福下来了","time":1413530574252,"likedCount":705997,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":16838326,"nickname":"-荒灰黄-","authStatus":0,"avatarUrl":"https://p3.music.126.net/stdTZ6aLNTal0sXbyjCNlw==/109951164226420286.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[],"pendantData":null,"showFloorComment":null,"status":0,"commentId":5453734,"content":"老子要听一辈子周杰伦！！！","time":1415092759779,"likedCount":340198,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":48794963,"nickname":"全球单身狗反秀恩爱联盟荣誉会长","authStatus":0,"avatarUrl":"https://p4.music.126.net/OuRKD7ET0qHSJWsI-_0upA==/109951162930818823.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[],"pendantData":null,"showFloorComment":null,"status":0,"commentId":129011021,"content":"现在想来 我们这波第一批老去的90后还是挺幸运的 在我们最好的年龄遇到了最好的华语乐坛（周杰伦巅峰 林俊杰 SHE  潘玮柏 蔡依林\u2026）遇到了巅峰的星爷 遇到了最好的西科东艾北卡南麦 动画城陪我们成长 周杰伦陪我们成熟 我们看着星爷老去 见证科比退役 或许我们不是最好的一代 但一定是最精彩的一代","time":1457830064634,"likedCount":328748,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":43108078,"nickname":"长在地上的姑娘","authStatus":0,"avatarUrl":"https://p4.music.126.net/YPv0k5X2FXA3zNS77LtLNA==/109951163988181919.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[],"pendantData":null,"showFloorComment":null,"status":0,"commentId":6631017,"content":"周杰伦 你要对那么多人的青春负责","time":1417575389570,"likedCount":262876,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":46299149,"nickname":"我竟然是一个柚子","authStatus":0,"avatarUrl":"https://p4.music.126.net/exvr96f2EL_PMFElSoaa1Q==/18604836255346708.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":{"associator":{"vipCode":100,"rights":true},"musicPackage":null,"redVipAnnualCount":1},"remarkName":null,"vipType":11},"beReplied":[],"pendantData":{"id":5005,"imageUrl":"http://p1.music.126.net/re9ZZBYLQwnY_Q2C6CRXOQ==/109951163313115348.jpg"},"showFloorComment":null,"status":0,"commentId":10371796,"content":"初中听周杰伦被同学嘲笑：唱的什么鬼\u2026长那么丑\u2026词都唱不清\u2026他们哪知道若干年后会听着他的歌流泪一整晚，原来我们输给了时光，败给了唇红齿白的自己😭","time":1423436901019,"likedCount":221905,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":116973683,"nickname":"小東得100分","authStatus":0,"avatarUrl":"https://p4.music.126.net/to_DB2Sywwg_xgJInfAUMA==/109951162795358748.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[],"pendantData":null,"showFloorComment":null,"status":0,"commentId":137314256,"content":"2004年，高一，男班长，你很喜欢周杰伦，我存了很久的钱在你生日那天买了一张周杰伦的专辑送给你。2016年，如今，你女儿都可以上幼儿园了吧，但是你一直不知道我曾经也暗恋过你。是的，我只能暗恋，因为我也是男的。","time":1459656209121,"likedCount":190106,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":2985564,"nickname":"哥特式的永恒","authStatus":0,"avatarUrl":"https://p3.music.126.net/8cznmwtdA8VmQUhX_TZSIg==/5649290743627081.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[],"pendantData":null,"showFloorComment":null,"status":0,"commentId":5448087,"content":"有没有85前听杰伦的来这？我84，2000年开始听刚上高一，现在都是俩孩的爹了，再听谢谢老歌好像回到了那时候的晚自习，在操场一起牵手的那个没成了孩他娘，但也没什么遗憾～","time":1415029152333,"likedCount":139341,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":32176551,"nickname":"陈煜在听歌","authStatus":0,"avatarUrl":"https://p3.music.126.net/cf3RAquQ3Y0IR8Xk4R44Aw==/3252355402663226.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[],"pendantData":null,"showFloorComment":null,"status":0,"commentId":4909839,"content":"第一次听是小学四年级。印在脑中的场景是永远在拖地的教室和洁癖到偏执的班主任。时过境迁，还是没机会为你翘课，最后，勇气和那个下雨天一起消失。","time":1413351934058,"likedCount":106163,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":447559,"nickname":"痞子狗","authStatus":0,"avatarUrl":"https://p3.music.126.net/dwK0DlMZrypKm4BQ9e7EdA==/109951162846037573.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[],"pendantData":null,"showFloorComment":null,"status":0,"commentId":4973362,"content":"半夜听着周董的老歌，看着大家的评论，满满的回忆。","time":1413572332622,"likedCount":90842,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":61082869,"nickname":"-FishinthePool-","authStatus":0,"avatarUrl":"https://p4.music.126.net/699hjLUyseRv5mzEbELMtg==/109951163258591309.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[],"pendantData":null,"showFloorComment":null,"status":0,"commentId":18812504,"content":"我在网易云音乐里看到过评论达到一万多的歌     但没有一个歌手能及你拥有两首评论6000+的歌   十六首评论2000+的歌   超过四十首评论1000+的歌并且所有这些歌  都出自你的才华     而我也只是想表达  对于很多人的一生来说     你的影响真的是无人能够替代的","time":1432022482084,"likedCount":78669,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":76008264,"nickname":"伦桑嫁我可好","authStatus":0,"avatarUrl":"https://p3.music.126.net/kkCJVaal43DlaSHgZGCVDA==/19106213556047476.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[],"pendantData":null,"showFloorComment":null,"status":0,"commentId":23039286,"content":"肚皮上趟着个杰迷，在读这歌的评论","time":1434888708737,"likedCount":57098,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":45692971,"nickname":"mcdull100","authStatus":0,"avatarUrl":"https://p3.music.126.net/PewIbleluF2HWLH41LFKWA==/7779044766715897.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[],"pendantData":null,"showFloorComment":null,"status":0,"commentId":10093807,"content":"10年前高一听的，直到出了国，一天路过康乃尔的钟楼正点报时竟然用编钟演奏了这首歌，内心莫名感动","time":1423108461424,"likedCount":52113,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":46436303,"nickname":"frejaaaaaa","authStatus":0,"avatarUrl":"https://p4.music.126.net/8vv7etgk3eZgKLEzCboWeg==/109951162928637898.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[],"pendantData":null,"showFloorComment":null,"status":0,"commentId":8399019,"content":"那时候总喜欢在操场上偷偷看他打篮球 广播里总是放这首歌 每天一到吃饭的时候我就坐在操场的站台上看他 春天的风暖暖的 还有夕阳西下的晚霞 那时候我们有大把的青春可以浪掷 不知道成长是什么 然而所有的时光都是被辜负被浪费后 才能从记忆里将某一段拎出 拍拍上面沉积的灰尘 感叹它才是最好的时光","time":1420710731134,"likedCount":50753,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":292098758,"nickname":"我和一万头猪","authStatus":0,"avatarUrl":"https://p3.music.126.net/gRUL4DbFyUFM2-YHqucJRQ==/109951163120048402.jpg","experts":null,"userType":300,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[{"user":{"locationInfo":null,"liveInfo":null,"userId":1,"nickname":"网易云音乐","authStatus":1,"avatarUrl":"https://p3.music.126.net/QWMV-Ru_6149AKe0mCBXKg==/1420569024374784.jpg","experts":null,"userType":2,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":11},"beRepliedCommentId":45099584,"content":"【周杰伦出道15周年】十五年前，一个戴鸭舌帽的男生唱着《可爱女人》如《龙卷风》般闯入了我们的世界。从此，耳边的华语音乐有了不一样的色彩。十五年，或许，他不是你的至爱，但总有一首歌，写进了你的青春。他就是周杰伦。还记得你听他的第一首歌吗？[爱心]","status":0,"expressionUrl":null}],"pendantData":null,"showFloorComment":null,"status":0,"commentId":438479088,"content":"12年前，周杰伦带着他的《发如雪》闯进了我的世界。一直喜欢他到现在，不管是《晴天》，《七里香》还是《三年二班》，这些都变成小黄花写进了我们的青春。每次听他的歌，就像回到小时候，一边吃着西瓜，一边用着千千静听循环他的歌。现在，还好有网易云，可以分享我的感动。还记得你听他的第一首歌吗？","time":1497365209289,"likedCount":41353,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false},{"user":{"locationInfo":null,"liveInfo":null,"userId":29320819,"nickname":"我从未见过如此厚颜无耻之人","authStatus":0,"avatarUrl":"https://p3.music.126.net/Z0Wq2xPFeN0B4G0pVn5svA==/109951163042655307.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":{"associator":{"vipCode":100,"rights":true},"musicPackage":null,"redVipAnnualCount":1},"remarkName":null,"vipType":11},"beReplied":[],"pendantData":{"id":4007,"imageUrl":"http://p1.music.126.net/ohUUmYvut7fiwEILcC0Gtw==/109951163313124910.jpg"},"showFloorComment":null,"status":0,"commentId":5756723,"content":"这首歌，出了11年，我今天第一次听，我非常非常开心今天邂逅了这首歌[可爱]\u2014\u2014\u2014\u2014\u2014依然周杰伦","time":1415896400367,"likedCount":23550,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":null,"repliedMark":null,"liked":false}]
     * code : 200
     * comments : [{"user":{"locationInfo":null,"liveInfo":null,"userId":484899398,"nickname":"末名w","authStatus":0,"avatarUrl":"https://p4.music.126.net/kTbn2WvfzoSWyNZ89WHj8w==/109951163985718184.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0},"beReplied":[],"pendantData":null,"showFloorComment":null,"status":0,"commentId":1630635727,"content":"我又来了哈哈哈哈","time":1568683614450,"likedCount":4,"expressionUrl":null,"commentLocationType":0,"parentCommentId":0,"decoration":{},"repliedMark":null,"liked":false}]
     * total : 2017815
     * more : true
     */

    private boolean isMusician;
    private int cnum;
    private long userId;
    private boolean moreHot;
    private int code;
    private int total;
    private boolean more;
    private List<?> topComments;
    private List<CommentsBean> hotComments;
    private List<CommentsBean> comments;

    public boolean isIsMusician() {
        return isMusician;
    }

    public void setIsMusician(boolean isMusician) {
        this.isMusician = isMusician;
    }

    public int getCnum() {
        return cnum;
    }

    public void setCnum(int cnum) {
        this.cnum = cnum;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public boolean isMoreHot() {
        return moreHot;
    }

    public void setMoreHot(boolean moreHot) {
        this.moreHot = moreHot;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public boolean isMore() {
        return more;
    }

    public void setMore(boolean more) {
        this.more = more;
    }

    public List<?> getTopComments() {
        return topComments;
    }

    public void setTopComments(List<?> topComments) {
        this.topComments = topComments;
    }

    public List<CommentsBean> getHotComments() {
        return hotComments;
    }

    public void setHotComments(List<CommentsBean> hotComments) {
        this.hotComments = hotComments;
    }

    public List<CommentsBean> getComments() {
        return comments;
    }

    public void setComments(List<CommentsBean> comments) {
        this.comments = comments;
    }

    public static class CommentsBean {
        /**
         * user : {"locationInfo":null,"liveInfo":null,"userId":484899398,"nickname":"末名w","authStatus":0,"avatarUrl":"https://p4.music.126.net/kTbn2WvfzoSWyNZ89WHj8w==/109951163985718184.jpg","experts":null,"userType":0,"expertTags":null,"vipRights":null,"remarkName":null,"vipType":0}
         * beReplied : []
         * pendantData : null
         * showFloorComment : null
         * status : 0
         * commentId : 1630635727
         * content : 我又来了哈哈哈哈
         * time : 1568683614450
         * likedCount : 4
         * expressionUrl : null
         * commentLocationType : 0
         * parentCommentId : 0
         * decoration : {}
         * repliedMark : null
         * liked : false
         */

        private UserBeanX user;
        private UserBeanX beRepliedUser;
        private Object pendantData;
        private Object showFloorComment;
        private int status;
        private long commentId;
        private String content;
        private long time;
        private int likedCount;
        private Object expressionUrl;
        private int commentLocationType;
        private long parentCommentId;
        private DecorationBean decoration;
        private Object repliedMark;
        private boolean liked;
        private List<?> beReplied;

        public UserBeanX getBeRepliedUser() {
            return beRepliedUser;
        }

        public void setBeRepliedUser(UserBeanX beRepliedUser) {
            this.beRepliedUser = beRepliedUser;
        }

        public UserBeanX getUser() {
            return user;
        }

        public void setUser(UserBeanX user) {
            this.user = user;
        }

        public Object getPendantData() {
            return pendantData;
        }

        public void setPendantData(Object pendantData) {
            this.pendantData = pendantData;
        }

        public Object getShowFloorComment() {
            return showFloorComment;
        }

        public void setShowFloorComment(Object showFloorComment) {
            this.showFloorComment = showFloorComment;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public long getCommentId() {
            return commentId;
        }

        public void setCommentId(long commentId) {
            this.commentId = commentId;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public long getTime() {
            return time;
        }

        public void setTime(long time) {
            this.time = time;
        }

        public int getLikedCount() {
            return likedCount;
        }

        public void setLikedCount(int likedCount) {
            this.likedCount = likedCount;
        }

        public Object getExpressionUrl() {
            return expressionUrl;
        }

        public void setExpressionUrl(Object expressionUrl) {
            this.expressionUrl = expressionUrl;
        }

        public int getCommentLocationType() {
            return commentLocationType;
        }

        public void setCommentLocationType(int commentLocationType) {
            this.commentLocationType = commentLocationType;
        }

        public long getParentCommentId() {
            return parentCommentId;
        }

        public void setParentCommentId(long parentCommentId) {
            this.parentCommentId = parentCommentId;
        }

        public DecorationBean getDecoration() {
            return decoration;
        }

        public void setDecoration(DecorationBean decoration) {
            this.decoration = decoration;
        }

        public Object getRepliedMark() {
            return repliedMark;
        }

        public void setRepliedMark(Object repliedMark) {
            this.repliedMark = repliedMark;
        }

        public boolean isLiked() {
            return liked;
        }

        public void setLiked(boolean liked) {
            this.liked = liked;
        }

        public List<?> getBeReplied() {
            return beReplied;
        }

        public void setBeReplied(List<?> beReplied) {
            this.beReplied = beReplied;
        }

        public static class UserBeanX {
            /**
             * locationInfo : null
             * liveInfo : null
             * userId : 484899398
             * nickname : 末名w
             * authStatus : 0
             * avatarUrl : https://p4.music.126.net/kTbn2WvfzoSWyNZ89WHj8w==/109951163985718184.jpg
             * experts : null
             * userType : 0
             * expertTags : null
             * vipRights : null
             * remarkName : null
             * vipType : 0
             */

            private Object locationInfo;
            private Object liveInfo;
            private long userId;
            private String nickname;
            private int authStatus;
            private String avatarUrl;
            private Object experts;
            private int userType;
            private Object expertTags;
            private VipRights vipRights;
            private Object remarkName;
            private int vipType;

            public Object getLocationInfo() {
                return locationInfo;
            }

            public void setLocationInfo(Object locationInfo) {
                this.locationInfo = locationInfo;
            }

            public Object getLiveInfo() {
                return liveInfo;
            }

            public void setLiveInfo(Object liveInfo) {
                this.liveInfo = liveInfo;
            }

            public long getUserId() {
                return userId;
            }

            public void setUserId(long userId) {
                this.userId = userId;
            }

            public String getNickname() {
                return nickname;
            }

            public void setNickname(String nickname) {
                this.nickname = nickname;
            }

            public int getAuthStatus() {
                return authStatus;
            }

            public void setAuthStatus(int authStatus) {
                this.authStatus = authStatus;
            }

            public String getAvatarUrl() {
                return avatarUrl;
            }

            public void setAvatarUrl(String avatarUrl) {
                this.avatarUrl = avatarUrl;
            }

            public Object getExperts() {
                return experts;
            }

            public void setExperts(Object experts) {
                this.experts = experts;
            }

            public int getUserType() {
                return userType;
            }

            public void setUserType(int userType) {
                this.userType = userType;
            }

            public Object getExpertTags() {
                return expertTags;
            }

            public void setExpertTags(Object expertTags) {
                this.expertTags = expertTags;
            }

            public VipRights getVipRights() {
                return vipRights;
            }

            public void setVipRights(VipRights vipRights) {
                this.vipRights = vipRights;
            }

            public Object getRemarkName() {
                return remarkName;
            }

            public void setRemarkName(Object remarkName) {
                this.remarkName = remarkName;
            }

            public int getVipType() {
                return vipType;
            }

            public void setVipType(int vipType) {
                this.vipType = vipType;
            }
        }

        public static class VipRights {

            private Object musicPackage;
            private int redVipAnnualCount;

            public void setMusicPackage(Object musicPackage) {
                this.musicPackage = musicPackage;
            }

            public Object getMusicPackage() {
                return musicPackage;
            }

            public void setRedVipAnnualCount(int redVipAnnualCount) {
                this.redVipAnnualCount = redVipAnnualCount;
            }

            public int getRedVipAnnualCount() {
                return redVipAnnualCount;
            }

        }

        public static class DecorationBean {
        }

        @Override
        public String toString() {
            return "CommentsBean{" +
                    "user=" + user +
                    ", pendantData=" + pendantData +
                    ", showFloorComment=" + showFloorComment +
                    ", status=" + status +
                    ", commentId=" + commentId +
                    ", content='" + content + '\'' +
                    ", time=" + time +
                    ", likedCount=" + likedCount +
                    ", expressionUrl=" + expressionUrl +
                    ", commentLocationType=" + commentLocationType +
                    ", parentCommentId=" + parentCommentId +
                    ", decoration=" + decoration +
                    ", repliedMark=" + repliedMark +
                    ", liked=" + liked +
                    ", beReplied=" + beReplied +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "MusicCommentBean{" +
                "isMusician=" + isMusician +
                ", cnum=" + cnum +
                ", userId=" + userId +
                ", moreHot=" + moreHot +
                ", code=" + code +
                ", total=" + total +
                ", more=" + more +
                ", topComments=" + topComments +
                ", hotComments=" + hotComments +
                ", comments=" + comments +
                '}';
    }
}
