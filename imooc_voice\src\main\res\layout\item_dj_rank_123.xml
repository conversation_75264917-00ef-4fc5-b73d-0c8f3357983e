<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_item_rank_123_avatar"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_centerHorizontal="true"
        android:src="@drawable/ic_test" />

    <ImageView
        android:id="@+id/iv_item_rank_user_tag"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_alignLeft="@+id/iv_item_rank_123_avatar"
        android:layout_alignBottom="@+id/iv_item_rank_123_avatar"
        android:layout_marginLeft="82dp"
        android:src="@drawable/ic_official"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_item_rank_123_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_item_rank_123_avatar"
        android:layout_centerHorizontal="true"
        android:gravity="center_horizontal"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginTop="8dp"
        android:text="名字"
        android:textColor="@color/black" />

    <TextView
        android:id="@+id/tv_item_rank_123_hot"
        android:layout_width="40dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_item_rank_123_name"
        android:layout_centerHorizontal="true"
        android:gravity="center_horizontal"
        android:layout_marginTop="5dp"
        android:text="10万"
        android:textSize="11sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_below="@+id/tv_item_rank_123_hot"
        android:layout_marginTop="20dp"
        android:background="@color/app_background" />
</RelativeLayout>