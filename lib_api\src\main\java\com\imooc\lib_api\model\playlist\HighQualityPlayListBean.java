package com.imooc.lib_api.model.playlist;

import java.util.List;

public class HighQualityPlayListBean {

    /**
     * playlists : [{"name":"论钢琴的交响性：管弦乐名作及其钢琴改编","id":2317199475,"trackNumberUpdateTime":1565811561172,"status":0,"userId":********,"createTime":1531678315686,"updateTime":1565857559219,"subscribedCount":74517,"trackCount":160,"cloudTrackCount":0,"coverImgUrl":"http://p2.music.126.net/oS3ZLQ66uGPMnnOJDzDlBw==/*****************.jpg","coverImgId":*****************,"description":"交响乐的钢琴改编曲，在浩如烟海的钢琴作品中，是一颗异常璀璨的明珠。众所周知，钢琴被称为乐器之王，其原因有：音域宽广（涵盖了整个乐音体系），响度控制灵敏（可作出任意的强弱变化），和声手段多样（四手联弹和双钢琴等），在音响效果上几乎足以比肩整个交响乐团。所以用钢琴语言来诠释声势浩大、织体丰富的管弦乐作品有着很高的艺术与欣赏价值。\n\n古往今来许多著名作曲家都曾以用钢琴改编交响乐为挑战和乐趣，最著名的当属\u201c钢琴之王\u201d李斯特。他一生共创作了700余首钢琴改编曲，其中不乏贝多芬九部交响曲这样的旷世名作。李斯特的这些钢琴改编曲体现了他在钢琴领域的伟大成就，也把整个钢琴艺术推到了一个新高度。\n\n同时以李斯特为榜样，自19世纪末至今，不计其数的作曲家和钢琴家投身于钢琴改编交响乐的事业中去，诸如拉赫玛尼诺夫、霍洛维茨、戈多夫斯基等大师都是个中翘楚。他们用无与伦比的才华赋予了那星河般灿烂的乐章更别致的灵魂。\n\n歌单排序为交响乐原作（单数）及其钢琴改编曲（双数），作品间排名不分先后。\n\n1-8 肖斯塔科维奇、哈恰图良、格林卡、普罗科菲耶夫四首俄式圆舞曲\n9-12 伏尔塔瓦河（斯美塔那改）\n13-84 贝多芬九部交响曲（李斯特改）\n85-88 德沃夏克第九交响曲（德沃夏克改）\n89-94 柴可夫斯基第五、第六交响曲（塔涅耶夫改）\n95 莫扎特第四十交响曲（卡萨利斯改）\n97-104 西贝柳丝第二交响曲、忧郁圆舞曲、芬兰颂\n105-112 勃拉姆斯第三、第四交响曲（勃拉姆斯改）\n113 肖斯塔科维奇第十交响曲\n115 马勒第五交响曲（卡萨利斯改）\n117 柏辽兹：幻想交响曲（李斯特改）\n119-122 罗西尼：塞维利亚的理发师\n123 莫扎特：费加罗的婚礼 序曲\n125 女武神的骑行（布索尼改）\n127 门德尔松：婚礼进行曲（李斯特改）\n129-136 小约翰施特劳斯：蓝色多瑙河、春之声圆舞曲、维也纳森林圆舞曲、皇帝圆舞曲\n137 圣桑：骷髅之舞（李斯特改）\n139 野蜂飞舞（拉赫玛尼诺夫改）\n141 波莱罗舞曲（道格·阿恰兹改）\n143 罗密欧与朱丽叶 骑士之舞（普罗科菲耶夫改）\n145-152 柴可夫斯基：天鹅湖、睡美人、胡桃夹子（拉赫玛尼诺夫等改）\n153 斯特拉文斯基：彼得鲁什卡\n155 格什温：一个美国人在巴黎\n157-160 莫扎特：安魂曲（车尔尼改）\n\n封面设计来自柏林爱乐乐团。","tags":["古典","钢琴","器乐"],"playCount":2003062,"trackUpdateTime":*************,"specialType":0,"totalDuration":0,"creator":{"defaultAvatar":false,"province":510000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/o-K3vXnB18zJeqnmXnmqHw==/109951163448152552.jpg","accountStatus":0,"gender":1,"city":510100,"birthday":************,"userId":********,"userType":200,"nickname":"团战专用小火把","signature":"i∃ΛI∀N səɯ!ʇəωos'əɿdɯ!s ooʇ ƃunoʎ ooʇ 此号暂已废弃 陌生来电恕不接听","description":"","detailDescription":"","avatarImgId":109951163448152540,"backgroundImgId":109951163135555420,"backgroundUrl":"http://p1.music.126.net/cxcj1I1uwN_-ikwG2_KYBw==/109951163135555429.jpg","authority":0,"mutual":false,"expertTags":["日语","轻音乐","古典"],"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951163448152552","backgroundImgIdStr":"109951163135555429","avatarImgId_str":"109951163448152552"},"tracks":null,"subscribers":[{"defaultAvatar":false,"province":540000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/QA42dRLUig-KesZ6OyjjSw==/109951164166881348.jpg","accountStatus":0,"gender":2,"city":542100,"birthday":************,"userId":**********,"userType":0,"nickname":"happysing女","signature":"","description":"","detailDescription":"","avatarImgId":109951164166881340,"backgroundImgId":109951162868126480,"backgroundUrl":"http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951164166881348","backgroundImgIdStr":"109951162868126486","avatarImgId_str":"109951164166881348"}],"subscribed":false,"commentThreadId":"A_PL_0_2317199475","newImported":false,"adType":0,"highQuality":true,"privacy":0,"ordered":true,"anonimous":false,"coverStatus":3,"shareCount":489,"coverImgId_str":"*****************","commentCount":198,"copywriter":"管弦乐名作及其钢琴改编","tag":"古典"},{"name":"玫瑰与威士忌\u2022蓝调女歌手精选集","id":*********,"trackNumberUpdateTime":*************,"status":0,"userId":*********,"createTime":1505804775521,"updateTime":1565748319572,"subscribedCount":7318,"trackCount":31,"cloudTrackCount":0,"coverImgUrl":"http://p2.music.126.net/26PQbkiKjsRJiiiEKeP8sA==/*****************.jpg","coverImgId":19188676928322260,"description":"现代蓝调、吉他、美女这个歌单里都有。\n这是一个精选了蓝调女歌手/吉他手音乐的歌单。风格以现代蓝调为主。\n经典的蓝调过载吉他音色，性感又有力度的唱腔，情绪感极强的音乐，是歌单选曲的标准。","tags":["摇滚","蓝调","性感"],"playCount":571026,"trackUpdateTime":*************,"specialType":0,"totalDuration":0,"creator":{"defaultAvatar":false,"province":210000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/3njktKVbX1IVHJlvwfpwQQ==/109951163018167604.jpg","accountStatus":0,"gender":1,"city":210200,"birthday":************,"userId":*********,"userType":200,"nickname":"Melody_Groove","signature":"Melody&Groove音乐工作室\u2022Line1乐队吉他手\u2022Pop Rock Blues Jazz Funk Latin Swing Fusion.","description":"","detailDescription":"","avatarImgId":109951163018167600,"backgroundImgId":109951163250651300,"backgroundUrl":"http://p1.music.126.net/txs2OzeTXwZ208Qv_aAJSQ==/109951163250651294.jpg","authority":0,"mutual":false,"expertTags":["蓝调","爵士","摇滚"],"experts":null,"djStatus":0,"vipType":11,"remarkName":null,"avatarImgIdStr":"109951163018167604","backgroundImgIdStr":"109951163250651294","avatarImgId_str":"109951163018167604"},"tracks":null,"subscribers":[{"defaultAvatar":false,"province":350000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/NCvnD8FMavi1Wo2f1lG17g==/*****************.jpg","accountStatus":0,"gender":1,"city":350100,"birthday":-*************,"userId":**********,"userType":0,"nickname":"徐墨菲","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":109951162868126480,"backgroundUrl":"http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"*****************","backgroundImgIdStr":"109951162868126486","avatarImgId_str":"*****************"}],"subscribed":false,"commentThreadId":"A_PL_0_*********","newImported":false,"adType":0,"highQuality":true,"privacy":0,"ordered":true,"anonimous":false,"coverStatus":3,"shareCount":133,"coverImgId_str":"*****************","commentCount":49,"copywriter":"蓝调女声精选集","tag":"摇滚"},{"name":"华语电影台词对白｜念念不忘","id":**********,"trackNumberUpdateTime":*************,"status":0,"userId":2445590,"createTime":1522561030065,"updateTime":*************,"subscribedCount":130095,"trackCount":221,"cloudTrackCount":0,"coverImgUrl":"http://p2.music.126.net/JnQmWLPgIh97PZnBtpPKOA==/109951164274045767.jpg","coverImgId":109951164274045760,"description":"华语电影台词对白｜念念不忘\n.\n电影原声中除了收录歌曲、配乐，还会选择部分电影对白、旁白，这类台词对电影背景交代、情节推动、人物性格塑造往往有重要意义。例如《阿飞正传》中的\u201c无脚鸟\u201d、《倩女幽魂》里宁采臣与聂小倩的\u201c只羡鸳鸯不羡仙\u201d、《花样年华》中慕云、丽珍的\u201c如果有多一张船票\u201d。而这些淫浸了光影魅力的词句段落，如今便成了电影粉丝的心头好。\n.\n歌单封面：电影《大话西游》\n为了避免不同电影的台词切换太过突兀，加入了序号分隔。\n.\n电影名单（按照歌单分隔序号排列）：\n1.《周星驰系列电影》\n2.《倩女幽魂》\n3.《喜剧之王》\n4.《东邪西毒 终极版》\n5.《英雄本色1&2》\n6.《阿飞正传》\n7.《花样年华》\n8.《一代宗师》\n9.《大话西游》\n10.《恋恋风尘》\n11.《逆光飞翔》\n12.《功夫》\n13.《游园惊梦》\n14.《男人四十》\n15.《甜蜜蜜》\n16.《香港仔》\n17.《海角七号》\n18.《伊莎贝拉》\n19.《带我去远方》\n20.《阿司匹林》\n21.《鹿鼎记》\n22.《最遥远的距离》\n23.《心动》\n24.《美少年之恋》\n25.《向左走向右走》\n26.《脚趾上的阳光》\n27.《暗恋桃花源》\n28.《虎度门》\n29.《天下无双》\n30.《女人四十》\n31.《苏州河》\n32.《麦兜故事》\n33.《华语电影台词剪辑》\n包括电影如下：\n《独自等待》\n《蓝色大门》\n《最遥远的距离》\n《桃色蛋白质》\n《天下无双》\n《李米的猜想》\n《不能没有你》\n《向左走向右走》\n《如果爱》\n.","tags":["华语","粤语","影视原声"],"playCount":9064936,"trackUpdateTime":*************,"specialType":0,"totalDuration":0,"creator":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/Jrj86gDsmjxYaWG0VuIpNQ==/109951163446003588.jpg","accountStatus":0,"gender":0,"city":1010000,"birthday":************,"userId":2445590,"userType":200,"nickname":"下一颗巧克力","signature":"你从陌生走来，又走向了陌生。 ｜新浪微博:下一颗巧克力ZRY","description":"","detailDescription":"","avatarImgId":109951163446003580,"backgroundImgId":109951163515806930,"backgroundUrl":"http://p1.music.126.net/sKHUTgFskCqt6VISFcBcJA==/109951163515806932.jpg","authority":0,"mutual":false,"expertTags":["影视原声","另类/独立","欧美"],"experts":{"2":"资讯(影视、欧美音乐)"},"djStatus":0,"vipType":11,"remarkName":null,"avatarImgIdStr":"109951163446003588","backgroundImgIdStr":"109951163515806932","avatarImgId_str":"109951163446003588"},"tracks":null,"subscribers":[{"defaultAvatar":false,"province":0,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/YwdveCKtTVLtkWDGV6BMjQ==/109951164297991142.jpg","accountStatus":0,"gender":1,"city":100,"birthday":-*************,"userId":**********,"userType":0,"nickname":"man按计划","signature":"","description":"","detailDescription":"","avatarImgId":109951164297991140,"backgroundImgId":109951162868128400,"backgroundUrl":"http://p1.music.126.net/2zSNIqTcpHL2jIvU6hG0EA==/109951162868128395.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951164297991142","backgroundImgIdStr":"109951162868128395","avatarImgId_str":"109951164297991142"}],"subscribed":false,"commentThreadId":"A_PL_0_**********","newImported":false,"adType":0,"highQuality":true,"privacy":0,"ordered":true,"anonimous":false,"coverStatus":3,"shareCount":1174,"coverImgId_str":"109951164274045767","commentCount":462,"copywriter":"华语电影台词对白","tag":"华语"}]
     * code : 200
     * more : true
     * lasttime : *************
     * total : 288
     */

    private int code;
    private boolean more;
    private long lasttime;
    private int total;
    private List<PlaylistsBean> playlists;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public boolean isMore() {
        return more;
    }

    public void setMore(boolean more) {
        this.more = more;
    }

    public long getLasttime() {
        return lasttime;
    }

    public void setLasttime(long lasttime) {
        this.lasttime = lasttime;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<PlaylistsBean> getPlaylists() {
        return playlists;
    }

    public void setPlaylists(List<PlaylistsBean> playlists) {
        this.playlists = playlists;
    }

    public static class PlaylistsBean {
        /**
         * name : 论钢琴的交响性：管弦乐名作及其钢琴改编
         * id : 2317199475
         * trackNumberUpdateTime : 1565811561172
         * status : 0
         * userId : ********
         * createTime : 1531678315686
         * updateTime : 1565857559219
         * subscribedCount : 74517
         * trackCount : 160
         * cloudTrackCount : 0
         * coverImgUrl : http://p2.music.126.net/oS3ZLQ66uGPMnnOJDzDlBw==/*****************.jpg
         * coverImgId : *****************
         * description : 交响乐的钢琴改编曲，在浩如烟海的钢琴作品中，是一颗异常璀璨的明珠。众所周知，钢琴被称为乐器之王，其原因有：音域宽广（涵盖了整个乐音体系），响度控制灵敏（可作出任意的强弱变化），和声手段多样（四手联弹和双钢琴等），在音响效果上几乎足以比肩整个交响乐团。所以用钢琴语言来诠释声势浩大、织体丰富的管弦乐作品有着很高的艺术与欣赏价值。

         古往今来许多著名作曲家都曾以用钢琴改编交响乐为挑战和乐趣，最著名的当属“钢琴之王”李斯特。他一生共创作了700余首钢琴改编曲，其中不乏贝多芬九部交响曲这样的旷世名作。李斯特的这些钢琴改编曲体现了他在钢琴领域的伟大成就，也把整个钢琴艺术推到了一个新高度。

         同时以李斯特为榜样，自19世纪末至今，不计其数的作曲家和钢琴家投身于钢琴改编交响乐的事业中去，诸如拉赫玛尼诺夫、霍洛维茨、戈多夫斯基等大师都是个中翘楚。他们用无与伦比的才华赋予了那星河般灿烂的乐章更别致的灵魂。

         歌单排序为交响乐原作（单数）及其钢琴改编曲（双数），作品间排名不分先后。

         1-8 肖斯塔科维奇、哈恰图良、格林卡、普罗科菲耶夫四首俄式圆舞曲
         9-12 伏尔塔瓦河（斯美塔那改）
         13-84 贝多芬九部交响曲（李斯特改）
         85-88 德沃夏克第九交响曲（德沃夏克改）
         89-94 柴可夫斯基第五、第六交响曲（塔涅耶夫改）
         95 莫扎特第四十交响曲（卡萨利斯改）
         97-104 西贝柳丝第二交响曲、忧郁圆舞曲、芬兰颂
         105-112 勃拉姆斯第三、第四交响曲（勃拉姆斯改）
         113 肖斯塔科维奇第十交响曲
         115 马勒第五交响曲（卡萨利斯改）
         117 柏辽兹：幻想交响曲（李斯特改）
         119-122 罗西尼：塞维利亚的理发师
         123 莫扎特：费加罗的婚礼 序曲
         125 女武神的骑行（布索尼改）
         127 门德尔松：婚礼进行曲（李斯特改）
         129-136 小约翰施特劳斯：蓝色多瑙河、春之声圆舞曲、维也纳森林圆舞曲、皇帝圆舞曲
         137 圣桑：骷髅之舞（李斯特改）
         139 野蜂飞舞（拉赫玛尼诺夫改）
         141 波莱罗舞曲（道格·阿恰兹改）
         143 罗密欧与朱丽叶 骑士之舞（普罗科菲耶夫改）
         145-152 柴可夫斯基：天鹅湖、睡美人、胡桃夹子（拉赫玛尼诺夫等改）
         153 斯特拉文斯基：彼得鲁什卡
         155 格什温：一个美国人在巴黎
         157-160 莫扎特：安魂曲（车尔尼改）

         封面设计来自柏林爱乐乐团。
         * tags : ["古典","钢琴","器乐"]
         * playCount : 2003062
         * trackUpdateTime : *************
         * specialType : 0
         * totalDuration : 0
         * creator : {"defaultAvatar":false,"province":510000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/o-K3vXnB18zJeqnmXnmqHw==/109951163448152552.jpg","accountStatus":0,"gender":1,"city":510100,"birthday":************,"userId":********,"userType":200,"nickname":"团战专用小火把","signature":"i∃ΛI∀N səɯ!ʇəωos'əɿdɯ!s ooʇ ƃunoʎ ooʇ 此号暂已废弃 陌生来电恕不接听","description":"","detailDescription":"","avatarImgId":109951163448152540,"backgroundImgId":109951163135555420,"backgroundUrl":"http://p1.music.126.net/cxcj1I1uwN_-ikwG2_KYBw==/109951163135555429.jpg","authority":0,"mutual":false,"expertTags":["日语","轻音乐","古典"],"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951163448152552","backgroundImgIdStr":"109951163135555429","avatarImgId_str":"109951163448152552"}
         * tracks : null
         * subscribers : [{"defaultAvatar":false,"province":540000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/QA42dRLUig-KesZ6OyjjSw==/109951164166881348.jpg","accountStatus":0,"gender":2,"city":542100,"birthday":************,"userId":**********,"userType":0,"nickname":"happysing女","signature":"","description":"","detailDescription":"","avatarImgId":109951164166881340,"backgroundImgId":109951162868126480,"backgroundUrl":"http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951164166881348","backgroundImgIdStr":"109951162868126486","avatarImgId_str":"109951164166881348"}]
         * subscribed : false
         * commentThreadId : A_PL_0_2317199475
         * newImported : false
         * adType : 0
         * highQuality : true
         * privacy : 0
         * ordered : true
         * anonimous : false
         * coverStatus : 3
         * shareCount : 489
         * coverImgId_str : *****************
         * commentCount : 198
         * copywriter : 管弦乐名作及其钢琴改编
         * tag : 古典
         */

        private String name;
        private long id;
        private long trackNumberUpdateTime;
        private int status;
        private long userId;
        private long createTime;
        private long updateTime;
        private int subscribedCount;
        private int trackCount;
        private int cloudTrackCount;
        private String coverImgUrl;
        private long coverImgId;
        private String description;
        private int playCount;
        private long trackUpdateTime;
        private int specialType;
        private int totalDuration;
        private CreatorBean creator;
        private Object tracks;
        private boolean subscribed;
        private String commentThreadId;
        private boolean newImported;
        private int adType;
        private boolean highQuality;
        private int privacy;
        private boolean ordered;
        private boolean anonimous;
        private int coverStatus;
        private int shareCount;
        private String coverImgId_str;
        private int commentCount;
        private String copywriter;
        private String tag;
        private List<String> tags;
        private List<SubscribersBean> subscribers;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public long getTrackNumberUpdateTime() {
            return trackNumberUpdateTime;
        }

        public void setTrackNumberUpdateTime(long trackNumberUpdateTime) {
            this.trackNumberUpdateTime = trackNumberUpdateTime;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public long getUserId() {
            return userId;
        }

        public void setUserId(long userId) {
            this.userId = userId;
        }

        public long getCreateTime() {
            return createTime;
        }

        public void setCreateTime(long createTime) {
            this.createTime = createTime;
        }

        public long getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(long updateTime) {
            this.updateTime = updateTime;
        }

        public int getSubscribedCount() {
            return subscribedCount;
        }

        public void setSubscribedCount(int subscribedCount) {
            this.subscribedCount = subscribedCount;
        }

        public int getTrackCount() {
            return trackCount;
        }

        public void setTrackCount(int trackCount) {
            this.trackCount = trackCount;
        }

        public int getCloudTrackCount() {
            return cloudTrackCount;
        }

        public void setCloudTrackCount(int cloudTrackCount) {
            this.cloudTrackCount = cloudTrackCount;
        }

        public String getCoverImgUrl() {
            return coverImgUrl;
        }

        public void setCoverImgUrl(String coverImgUrl) {
            this.coverImgUrl = coverImgUrl;
        }

        public long getCoverImgId() {
            return coverImgId;
        }

        public void setCoverImgId(long coverImgId) {
            this.coverImgId = coverImgId;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public int getPlayCount() {
            return playCount;
        }

        public void setPlayCount(int playCount) {
            this.playCount = playCount;
        }

        public long getTrackUpdateTime() {
            return trackUpdateTime;
        }

        public void setTrackUpdateTime(long trackUpdateTime) {
            this.trackUpdateTime = trackUpdateTime;
        }

        public int getSpecialType() {
            return specialType;
        }

        public void setSpecialType(int specialType) {
            this.specialType = specialType;
        }

        public int getTotalDuration() {
            return totalDuration;
        }

        public void setTotalDuration(int totalDuration) {
            this.totalDuration = totalDuration;
        }

        public CreatorBean getCreator() {
            return creator;
        }

        public void setCreator(CreatorBean creator) {
            this.creator = creator;
        }

        public Object getTracks() {
            return tracks;
        }

        public void setTracks(Object tracks) {
            this.tracks = tracks;
        }

        public boolean isSubscribed() {
            return subscribed;
        }

        public void setSubscribed(boolean subscribed) {
            this.subscribed = subscribed;
        }

        public String getCommentThreadId() {
            return commentThreadId;
        }

        public void setCommentThreadId(String commentThreadId) {
            this.commentThreadId = commentThreadId;
        }

        public boolean isNewImported() {
            return newImported;
        }

        public void setNewImported(boolean newImported) {
            this.newImported = newImported;
        }

        public int getAdType() {
            return adType;
        }

        public void setAdType(int adType) {
            this.adType = adType;
        }

        public boolean isHighQuality() {
            return highQuality;
        }

        public void setHighQuality(boolean highQuality) {
            this.highQuality = highQuality;
        }

        public int getPrivacy() {
            return privacy;
        }

        public void setPrivacy(int privacy) {
            this.privacy = privacy;
        }

        public boolean isOrdered() {
            return ordered;
        }

        public void setOrdered(boolean ordered) {
            this.ordered = ordered;
        }

        public boolean isAnonimous() {
            return anonimous;
        }

        public void setAnonimous(boolean anonimous) {
            this.anonimous = anonimous;
        }

        public int getCoverStatus() {
            return coverStatus;
        }

        public void setCoverStatus(int coverStatus) {
            this.coverStatus = coverStatus;
        }

        public int getShareCount() {
            return shareCount;
        }

        public void setShareCount(int shareCount) {
            this.shareCount = shareCount;
        }

        public String getCoverImgId_str() {
            return coverImgId_str;
        }

        public void setCoverImgId_str(String coverImgId_str) {
            this.coverImgId_str = coverImgId_str;
        }

        public int getCommentCount() {
            return commentCount;
        }

        public void setCommentCount(int commentCount) {
            this.commentCount = commentCount;
        }

        public String getCopywriter() {
            return copywriter;
        }

        public void setCopywriter(String copywriter) {
            this.copywriter = copywriter;
        }

        public String getTag() {
            return tag;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }

        public List<String> getTags() {
            return tags;
        }

        public void setTags(List<String> tags) {
            this.tags = tags;
        }

        public List<SubscribersBean> getSubscribers() {
            return subscribers;
        }

        public void setSubscribers(List<SubscribersBean> subscribers) {
            this.subscribers = subscribers;
        }

        public static class CreatorBean {
            /**
             * defaultAvatar : false
             * province : 510000
             * authStatus : 0
             * followed : false
             * avatarUrl : http://p1.music.126.net/o-K3vXnB18zJeqnmXnmqHw==/109951163448152552.jpg
             * accountStatus : 0
             * gender : 1
             * city : 510100
             * birthday : ************
             * userId : ********
             * userType : 200
             * nickname : 团战专用小火把
             * signature : i∃ΛI∀N səɯ!ʇəωos'əɿdɯ!s ooʇ ƃunoʎ ooʇ 此号暂已废弃 陌生来电恕不接听
             * description :
             * detailDescription :
             * avatarImgId : 109951163448152540
             * backgroundImgId : 109951163135555420
             * backgroundUrl : http://p1.music.126.net/cxcj1I1uwN_-ikwG2_KYBw==/109951163135555429.jpg
             * authority : 0
             * mutual : false
             * expertTags : ["日语","轻音乐","古典"]
             * experts : null
             * djStatus : 10
             * vipType : 0
             * remarkName : null
             * avatarImgIdStr : 109951163448152552
             * backgroundImgIdStr : 109951163135555429
             * avatarImgId_str : 109951163448152552
             */

            private boolean defaultAvatar;
            private int province;
            private int authStatus;
            private boolean followed;
            private String avatarUrl;
            private int accountStatus;
            private int gender;
            private int city;
            private long birthday;
            private int userId;
            private int userType;
            private String nickname;
            private String signature;
            private String description;
            private String detailDescription;
            private long avatarImgId;
            private long backgroundImgId;
            private String backgroundUrl;
            private int authority;
            private boolean mutual;
            private Object experts;
            private int djStatus;
            private int vipType;
            private Object remarkName;
            private String avatarImgIdStr;
            private String backgroundImgIdStr;
            private String avatarImgId_str;
            private List<String> expertTags;

            public boolean isDefaultAvatar() {
                return defaultAvatar;
            }

            public void setDefaultAvatar(boolean defaultAvatar) {
                this.defaultAvatar = defaultAvatar;
            }

            public int getProvince() {
                return province;
            }

            public void setProvince(int province) {
                this.province = province;
            }

            public int getAuthStatus() {
                return authStatus;
            }

            public void setAuthStatus(int authStatus) {
                this.authStatus = authStatus;
            }

            public boolean isFollowed() {
                return followed;
            }

            public void setFollowed(boolean followed) {
                this.followed = followed;
            }

            public String getAvatarUrl() {
                return avatarUrl;
            }

            public void setAvatarUrl(String avatarUrl) {
                this.avatarUrl = avatarUrl;
            }

            public int getAccountStatus() {
                return accountStatus;
            }

            public void setAccountStatus(int accountStatus) {
                this.accountStatus = accountStatus;
            }

            public int getGender() {
                return gender;
            }

            public void setGender(int gender) {
                this.gender = gender;
            }

            public int getCity() {
                return city;
            }

            public void setCity(int city) {
                this.city = city;
            }

            public long getBirthday() {
                return birthday;
            }

            public void setBirthday(long birthday) {
                this.birthday = birthday;
            }

            public int getUserId() {
                return userId;
            }

            public void setUserId(int userId) {
                this.userId = userId;
            }

            public int getUserType() {
                return userType;
            }

            public void setUserType(int userType) {
                this.userType = userType;
            }

            public String getNickname() {
                return nickname;
            }

            public void setNickname(String nickname) {
                this.nickname = nickname;
            }

            public String getSignature() {
                return signature;
            }

            public void setSignature(String signature) {
                this.signature = signature;
            }

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public String getDetailDescription() {
                return detailDescription;
            }

            public void setDetailDescription(String detailDescription) {
                this.detailDescription = detailDescription;
            }

            public long getAvatarImgId() {
                return avatarImgId;
            }

            public void setAvatarImgId(long avatarImgId) {
                this.avatarImgId = avatarImgId;
            }

            public long getBackgroundImgId() {
                return backgroundImgId;
            }

            public void setBackgroundImgId(long backgroundImgId) {
                this.backgroundImgId = backgroundImgId;
            }

            public String getBackgroundUrl() {
                return backgroundUrl;
            }

            public void setBackgroundUrl(String backgroundUrl) {
                this.backgroundUrl = backgroundUrl;
            }

            public int getAuthority() {
                return authority;
            }

            public void setAuthority(int authority) {
                this.authority = authority;
            }

            public boolean isMutual() {
                return mutual;
            }

            public void setMutual(boolean mutual) {
                this.mutual = mutual;
            }

            public Object getExperts() {
                return experts;
            }

            public void setExperts(Object experts) {
                this.experts = experts;
            }

            public int getDjStatus() {
                return djStatus;
            }

            public void setDjStatus(int djStatus) {
                this.djStatus = djStatus;
            }

            public int getVipType() {
                return vipType;
            }

            public void setVipType(int vipType) {
                this.vipType = vipType;
            }

            public Object getRemarkName() {
                return remarkName;
            }

            public void setRemarkName(Object remarkName) {
                this.remarkName = remarkName;
            }

            public String getAvatarImgIdStr() {
                return avatarImgIdStr;
            }

            public void setAvatarImgIdStr(String avatarImgIdStr) {
                this.avatarImgIdStr = avatarImgIdStr;
            }

            public String getBackgroundImgIdStr() {
                return backgroundImgIdStr;
            }

            public void setBackgroundImgIdStr(String backgroundImgIdStr) {
                this.backgroundImgIdStr = backgroundImgIdStr;
            }

            public String getAvatarImgId_str() {
                return avatarImgId_str;
            }

            public void setAvatarImgId_str(String avatarImgId_str) {
                this.avatarImgId_str = avatarImgId_str;
            }

            public List<String> getExpertTags() {
                return expertTags;
            }

            public void setExpertTags(List<String> expertTags) {
                this.expertTags = expertTags;
            }
        }

        public static class SubscribersBean {
            /**
             * defaultAvatar : false
             * province : 540000
             * authStatus : 0
             * followed : false
             * avatarUrl : http://p1.music.126.net/QA42dRLUig-KesZ6OyjjSw==/109951164166881348.jpg
             * accountStatus : 0
             * gender : 2
             * city : 542100
             * birthday : ************
             * userId : **********
             * userType : 0
             * nickname : happysing女
             * signature :
             * description :
             * detailDescription :
             * avatarImgId : 109951164166881340
             * backgroundImgId : 109951162868126480
             * backgroundUrl : http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg
             * authority : 0
             * mutual : false
             * expertTags : null
             * experts : null
             * djStatus : 0
             * vipType : 0
             * remarkName : null
             * avatarImgIdStr : 109951164166881348
             * backgroundImgIdStr : 109951162868126486
             * avatarImgId_str : 109951164166881348
             */

            private boolean defaultAvatar;
            private int province;
            private int authStatus;
            private boolean followed;
            private String avatarUrl;
            private int accountStatus;
            private int gender;
            private int city;
            private long birthday;
            private int userId;
            private int userType;
            private String nickname;
            private String signature;
            private String description;
            private String detailDescription;
            private long avatarImgId;
            private long backgroundImgId;
            private String backgroundUrl;
            private int authority;
            private boolean mutual;
            private Object expertTags;
            private Object experts;
            private int djStatus;
            private int vipType;
            private Object remarkName;
            private String avatarImgIdStr;
            private String backgroundImgIdStr;
            private String avatarImgId_str;

            public boolean isDefaultAvatar() {
                return defaultAvatar;
            }

            public void setDefaultAvatar(boolean defaultAvatar) {
                this.defaultAvatar = defaultAvatar;
            }

            public int getProvince() {
                return province;
            }

            public void setProvince(int province) {
                this.province = province;
            }

            public int getAuthStatus() {
                return authStatus;
            }

            public void setAuthStatus(int authStatus) {
                this.authStatus = authStatus;
            }

            public boolean isFollowed() {
                return followed;
            }

            public void setFollowed(boolean followed) {
                this.followed = followed;
            }

            public String getAvatarUrl() {
                return avatarUrl;
            }

            public void setAvatarUrl(String avatarUrl) {
                this.avatarUrl = avatarUrl;
            }

            public int getAccountStatus() {
                return accountStatus;
            }

            public void setAccountStatus(int accountStatus) {
                this.accountStatus = accountStatus;
            }

            public int getGender() {
                return gender;
            }

            public void setGender(int gender) {
                this.gender = gender;
            }

            public int getCity() {
                return city;
            }

            public void setCity(int city) {
                this.city = city;
            }

            public long getBirthday() {
                return birthday;
            }

            public void setBirthday(long birthday) {
                this.birthday = birthday;
            }

            public int getUserId() {
                return userId;
            }

            public void setUserId(int userId) {
                this.userId = userId;
            }

            public int getUserType() {
                return userType;
            }

            public void setUserType(int userType) {
                this.userType = userType;
            }

            public String getNickname() {
                return nickname;
            }

            public void setNickname(String nickname) {
                this.nickname = nickname;
            }

            public String getSignature() {
                return signature;
            }

            public void setSignature(String signature) {
                this.signature = signature;
            }

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public String getDetailDescription() {
                return detailDescription;
            }

            public void setDetailDescription(String detailDescription) {
                this.detailDescription = detailDescription;
            }

            public long getAvatarImgId() {
                return avatarImgId;
            }

            public void setAvatarImgId(long avatarImgId) {
                this.avatarImgId = avatarImgId;
            }

            public long getBackgroundImgId() {
                return backgroundImgId;
            }

            public void setBackgroundImgId(long backgroundImgId) {
                this.backgroundImgId = backgroundImgId;
            }

            public String getBackgroundUrl() {
                return backgroundUrl;
            }

            public void setBackgroundUrl(String backgroundUrl) {
                this.backgroundUrl = backgroundUrl;
            }

            public int getAuthority() {
                return authority;
            }

            public void setAuthority(int authority) {
                this.authority = authority;
            }

            public boolean isMutual() {
                return mutual;
            }

            public void setMutual(boolean mutual) {
                this.mutual = mutual;
            }

            public Object getExpertTags() {
                return expertTags;
            }

            public void setExpertTags(Object expertTags) {
                this.expertTags = expertTags;
            }

            public Object getExperts() {
                return experts;
            }

            public void setExperts(Object experts) {
                this.experts = experts;
            }

            public int getDjStatus() {
                return djStatus;
            }

            public void setDjStatus(int djStatus) {
                this.djStatus = djStatus;
            }

            public int getVipType() {
                return vipType;
            }

            public void setVipType(int vipType) {
                this.vipType = vipType;
            }

            public Object getRemarkName() {
                return remarkName;
            }

            public void setRemarkName(Object remarkName) {
                this.remarkName = remarkName;
            }

            public String getAvatarImgIdStr() {
                return avatarImgIdStr;
            }

            public void setAvatarImgIdStr(String avatarImgIdStr) {
                this.avatarImgIdStr = avatarImgIdStr;
            }

            public String getBackgroundImgIdStr() {
                return backgroundImgIdStr;
            }

            public void setBackgroundImgIdStr(String backgroundImgIdStr) {
                this.backgroundImgIdStr = backgroundImgIdStr;
            }

            public String getAvatarImgId_str() {
                return avatarImgId_str;
            }

            public void setAvatarImgId_str(String avatarImgId_str) {
                this.avatarImgId_str = avatarImgId_str;
            }
        }
    }
}
