<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginLeft="15dp"
    android:layout_marginRight="10dp">
    <TextView
        android:id="@+id/tv_suggest_rank"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:textSize="18sp"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:textColor="@color/gray"
        android:text="1"/>
    <TextView
        android:id="@+id/tv_suggest_name"
        android:layout_toRightOf="@+id/tv_suggest_rank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:textSize="15.5sp"
        android:textColor="@color/black"
        android:text="处处吻"/>

    <ImageView
        android:id="@+id/iv_suggest_icon"
        android:layout_width="25dp"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/tv_suggest_name"
        android:layout_alignBottom="@+id/tv_suggest_name"
        android:layout_marginLeft="10dp"
        android:layout_toRightOf="@id/tv_suggest_name" />

    <TextView
        android:id="@+id/tv_suggest_artist"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_suggest_name"
        android:layout_alignLeft="@+id/tv_suggest_name"
        android:textSize="12sp"
        android:layout_marginTop="3dp"
        android:paddingBottom="10dp"
        android:text="会唱着这首歌的人会发光"/>
    <TextView
        android:id="@+id/tv_suggest_hot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="1200022"
        android:textSize="12sp"
        android:textColor="@color/silver"
        android:layout_alignParentRight="true"/>

</RelativeLayout>