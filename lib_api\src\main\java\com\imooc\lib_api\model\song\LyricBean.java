package com.imooc.lib_api.model.song;

/**
 * 歌词Bean
 */
public class LyricBean {

    /**
     * sgc : false
     * sfy : false
     * qfy : false
     * transUser : {"id":31010814,"status":99,"demand":1,"userid":20596713,"nickname":"淡其如水","uptime":1557831659617}
     * lyricUser : {"id":31010814,"status":99,"demand":0,"userid":20596713,"nickname":"淡其如水","uptime":1448003250100}
     * lrc : {"version":11,"lyric":"[00:00.42]遠く離れてるほどに  近くに感じてる\n[00:07.87]寂しさも強さへと  変換(かわ)ってく\n[00:13.74]\u2026君を想ったなら\n[00:34.98]街も 人も 夢も 変えていく時間に\n[00:41.54]ただ  逆らっていた\n[00:48.35]言葉を重ねても  理解(わか)り合えないこと\n[00:56.18]まだ  知らなかったね\n[01:01.76]\n[01:03.20]君だけを抱きしめたくて失くした夢  君は\n[01:10.55]「諦メナイデ」と云った\n[01:16.48]\n[01:17.63]遠く離れてるほどに  近くに感じてる\n[01:24.38]寂しさも強さへと  変換(かわ)ってく\n[01:29.66]\u2026君を想ったなら\n[01:32.75]切なく胸を刺す  それは夢の欠片(かけら)\n[01:39.06]ありのまま出逢えてた  その奇跡\n[01:44.42]もう一度信じて\n[01:47.85]\n[01:54.90]君がいない日々に  ずっと  立ち止まった\n[02:02.73]でも  歩き出してる\n[02:09.58]君と分かち合った  どの偶然にも意味が\n[02:17.50]そう  必ずあった\n[02:23.18]\n[02:24.53]それぞれの夢を叶えて  まためぐり逢う時\n[02:31.85]偶然は運命になる\n[02:37.46]\n[02:38.91]破れた約束さえも  誓いに変えたなら\n[02:45.55]あの場所で  出逢う時  あの頃の\n[02:50.97]二人に戻(なれ)るかな?\n[02:54.05]\"優しさ\"  に似ている  懐かしい面影\n[03:00.50]瞳(め)を閉じて見えるから\n[03:04.23]手を触れず在(あ)ることを知るから\n[03:09.01]\n[03:34.32]明日(あす)に  はぐれて  答えが\n[03:37.79]何も見えなくても\n[03:41.10]君に逢う  そのために重ねてく\n[03:46.43]\"今日\"  という真実\n[03:50.27]\n[03:50.95]遠く離れてるほどに  近くに感じてる\n[03:57.77]寂しさも強さへと  変換(かわ)ってく\n[04:03.03]\u2026君を想ったなら\n[04:06.02]切なく胸を刺す  それは夢の欠片(かけら)\n[04:12.53]ありのまま出逢えてた  その奇跡\n[04:17.75]もう一度信じて\n"}
     * klyric : {"version":0,"lyric":null}
     * tlyric : {"version":5,"lyric":"[00:00.42]相隔天涯海角  却宛如近在咫尺\n[00:07.87]一切寂寞皆化作坚强\n[00:13.74]每当思念你之际\n[00:34.98]街也是 人也是 梦也是 都在随着时间改变\n[00:41.54]只能无力地反抗\n[00:48.35]用尽千言万语  也无法谅解彼此\n[00:56.18]依然未懂这个道理\n[01:01.76]\n[01:03.20]为了拥你入怀而梦想破灭  你却说\n[01:10.55]「不要轻易放弃」\n[01:16.48]\n[01:17.63]相隔天涯海角  却宛如近在咫尺\n[01:24.38]一切寂寞皆化作坚强\n[01:29.66]每当思念你之际\n[01:32.75] 梦想的碎片  狠狠刺穿悲伤的胸怀\n[01:39.06] 两人真诚邂逅的奇迹\n[01:44.42]望再度相信\n[01:47.85]\n[01:54.90]在失去你的日子里  我一直驻足不前\n[02:02.73]但现在已迈出步伐\n[02:09.58]与你分享的每个偶然的意义\n[02:17.50]没错  必定存在\n[02:23.18]\n[02:24.53] 为了实现各自的梦  又再相逢之时\n[02:31.85]偶然便会成命运\n[02:37.46]\n[02:38.91]若破碎的约定也化作誓言的话\n[02:45.55]在那个地方重逢时  能否回到\n[02:50.97] 昔日当初的两人?\n[02:54.05] 你那怀念的面容  近似于\"温柔\"\n[03:00.50]轻闭双眸便会浮现脑海\n[03:04.23]尽管互不相碰也能感知彼此的存在\n[03:09.01]\n[03:34.32]迷失于明天  哪怕所有答案\n[03:37.79]全然落空也无妨\n[03:41.10]仅为了与你相逢  而交织起\n[03:46.43]名为\"今天\"的真实\n[03:50.27]\n[03:50.95]相隔天涯海角  却宛如近在咫尺\n[03:57.77]一切寂寞皆化作坚强\n[04:03.03]每当思念你之际\n[04:06.02]梦想的碎片  狠狠刺穿悲伤的胸怀\n[04:12.53] 两人真诚邂逅的奇迹\n[04:17.75]望再度相信"}
     * code : 200
     */

    private boolean sgc;
    private boolean sfy;
    private boolean qfy;
    private TransUserBean transUser;
    private LyricUserBean lyricUser;
    private LrcBean lrc;
    private KlyricBean klyric;
    private TlyricBean tlyric;
    private int code;

    public boolean isSgc() {
        return sgc;
    }

    public void setSgc(boolean sgc) {
        this.sgc = sgc;
    }

    public boolean isSfy() {
        return sfy;
    }

    public void setSfy(boolean sfy) {
        this.sfy = sfy;
    }

    public boolean isQfy() {
        return qfy;
    }

    public void setQfy(boolean qfy) {
        this.qfy = qfy;
    }

    public TransUserBean getTransUser() {
        return transUser;
    }

    public void setTransUser(TransUserBean transUser) {
        this.transUser = transUser;
    }

    public LyricUserBean getLyricUser() {
        return lyricUser;
    }

    public void setLyricUser(LyricUserBean lyricUser) {
        this.lyricUser = lyricUser;
    }

    public LrcBean getLrc() {
        return lrc;
    }

    public void setLrc(LrcBean lrc) {
        this.lrc = lrc;
    }

    public KlyricBean getKlyric() {
        return klyric;
    }

    public void setKlyric(KlyricBean klyric) {
        this.klyric = klyric;
    }

    public TlyricBean getTlyric() {
        return tlyric;
    }

    public void setTlyric(TlyricBean tlyric) {
        this.tlyric = tlyric;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public static class TransUserBean {
        /**
         * id : 31010814
         * status : 99
         * demand : 1
         * userid : 20596713
         * nickname : 淡其如水
         * uptime : 1557831659617
         */

        private int id;
        private int status;
        private int demand;
        private int userid;
        private String nickname;
        private long uptime;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getDemand() {
            return demand;
        }

        public void setDemand(int demand) {
            this.demand = demand;
        }

        public int getUserid() {
            return userid;
        }

        public void setUserid(int userid) {
            this.userid = userid;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public long getUptime() {
            return uptime;
        }

        public void setUptime(long uptime) {
            this.uptime = uptime;
        }
    }

    public static class LyricUserBean {
        /**
         * id : 31010814
         * status : 99
         * demand : 0
         * userid : 20596713
         * nickname : 淡其如水
         * uptime : 1448003250100
         */

        private int id;
        private int status;
        private int demand;
        private int userid;
        private String nickname;
        private long uptime;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getDemand() {
            return demand;
        }

        public void setDemand(int demand) {
            this.demand = demand;
        }

        public int getUserid() {
            return userid;
        }

        public void setUserid(int userid) {
            this.userid = userid;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }

        public long getUptime() {
            return uptime;
        }

        public void setUptime(long uptime) {
            this.uptime = uptime;
        }
    }

    public static class LrcBean {
        /**
         * version : 11
         * lyric : [00:00.42]遠く離れてるほどに  近くに感じてる
         * [00:07.87]寂しさも強さへと  変換(かわ)ってく
         * [00:13.74]…君を想ったなら
         * [00:34.98]街も 人も 夢も 変えていく時間に
         * [00:41.54]ただ  逆らっていた
         * [00:48.35]言葉を重ねても  理解(わか)り合えないこと
         * [00:56.18]まだ  知らなかったね
         * [01:01.76]
         * [01:03.20]君だけを抱きしめたくて失くした夢  君は
         * [01:10.55]「諦メナイデ」と云った
         * [01:16.48]
         * [01:17.63]遠く離れてるほどに  近くに感じてる
         * [01:24.38]寂しさも強さへと  変換(かわ)ってく
         * [01:29.66]…君を想ったなら
         * [01:32.75]切なく胸を刺す  それは夢の欠片(かけら)
         * [01:39.06]ありのまま出逢えてた  その奇跡
         * [01:44.42]もう一度信じて
         * [01:47.85]
         * [01:54.90]君がいない日々に  ずっと  立ち止まった
         * [02:02.73]でも  歩き出してる
         * [02:09.58]君と分かち合った  どの偶然にも意味が
         * [02:17.50]そう  必ずあった
         * [02:23.18]
         * [02:24.53]それぞれの夢を叶えて  まためぐり逢う時
         * [02:31.85]偶然は運命になる
         * [02:37.46]
         * [02:38.91]破れた約束さえも  誓いに変えたなら
         * [02:45.55]あの場所で  出逢う時  あの頃の
         * [02:50.97]二人に戻(なれ)るかな?
         * [02:54.05]"優しさ"  に似ている  懐かしい面影
         * [03:00.50]瞳(め)を閉じて見えるから
         * [03:04.23]手を触れず在(あ)ることを知るから
         * [03:09.01]
         * [03:34.32]明日(あす)に  はぐれて  答えが
         * [03:37.79]何も見えなくても
         * [03:41.10]君に逢う  そのために重ねてく
         * [03:46.43]"今日"  という真実
         * [03:50.27]
         * [03:50.95]遠く離れてるほどに  近くに感じてる
         * [03:57.77]寂しさも強さへと  変換(かわ)ってく
         * [04:03.03]…君を想ったなら
         * [04:06.02]切なく胸を刺す  それは夢の欠片(かけら)
         * [04:12.53]ありのまま出逢えてた  その奇跡
         * [04:17.75]もう一度信じて
         */

        private int version;
        private String lyric;

        public int getVersion() {
            return version;
        }

        public void setVersion(int version) {
            this.version = version;
        }

        public String getLyric() {
            return lyric;
        }

        public void setLyric(String lyric) {
            this.lyric = lyric;
        }
    }

    public static class KlyricBean {
        /**
         * version : 0
         * lyric : null
         */

        private int version;
        private Object lyric;

        public int getVersion() {
            return version;
        }

        public void setVersion(int version) {
            this.version = version;
        }

        public Object getLyric() {
            return lyric;
        }

        public void setLyric(Object lyric) {
            this.lyric = lyric;
        }
    }

    public static class TlyricBean {
        /**
         * version : 5
         * lyric : [00:00.42]相隔天涯海角  却宛如近在咫尺
         * [00:07.87]一切寂寞皆化作坚强
         * [00:13.74]每当思念你之际
         * [00:34.98]街也是 人也是 梦也是 都在随着时间改变
         * [00:41.54]只能无力地反抗
         * [00:48.35]用尽千言万语  也无法谅解彼此
         * [00:56.18]依然未懂这个道理
         * [01:01.76]
         * [01:03.20]为了拥你入怀而梦想破灭  你却说
         * [01:10.55]「不要轻易放弃」
         * [01:16.48]
         * [01:17.63]相隔天涯海角  却宛如近在咫尺
         * [01:24.38]一切寂寞皆化作坚强
         * [01:29.66]每当思念你之际
         * [01:32.75] 梦想的碎片  狠狠刺穿悲伤的胸怀
         * [01:39.06] 两人真诚邂逅的奇迹
         * [01:44.42]望再度相信
         * [01:47.85]
         * [01:54.90]在失去你的日子里  我一直驻足不前
         * [02:02.73]但现在已迈出步伐
         * [02:09.58]与你分享的每个偶然的意义
         * [02:17.50]没错  必定存在
         * [02:23.18]
         * [02:24.53] 为了实现各自的梦  又再相逢之时
         * [02:31.85]偶然便会成命运
         * [02:37.46]
         * [02:38.91]若破碎的约定也化作誓言的话
         * [02:45.55]在那个地方重逢时  能否回到
         * [02:50.97] 昔日当初的两人?
         * [02:54.05] 你那怀念的面容  近似于"温柔"
         * [03:00.50]轻闭双眸便会浮现脑海
         * [03:04.23]尽管互不相碰也能感知彼此的存在
         * [03:09.01]
         * [03:34.32]迷失于明天  哪怕所有答案
         * [03:37.79]全然落空也无妨
         * [03:41.10]仅为了与你相逢  而交织起
         * [03:46.43]名为"今天"的真实
         * [03:50.27]
         * [03:50.95]相隔天涯海角  却宛如近在咫尺
         * [03:57.77]一切寂寞皆化作坚强
         * [04:03.03]每当思念你之际
         * [04:06.02]梦想的碎片  狠狠刺穿悲伤的胸怀
         * [04:12.53] 两人真诚邂逅的奇迹
         * [04:17.75]望再度相信
         */

        private int version;
        private String lyric;

        public int getVersion() {
            return version;
        }

        public void setVersion(int version) {
            this.version = version;
        }

        public String getLyric() {
            return lyric;
        }

        public void setLyric(String lyric) {
            this.lyric = lyric;
        }
    }
}
