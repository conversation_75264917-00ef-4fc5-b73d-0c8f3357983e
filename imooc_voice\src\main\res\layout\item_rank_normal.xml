<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="5dp"
    android:paddingBottom="5dp">


    <ImageView
        android:id="@+id/iv_item_rank_cover_image"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:scaleType="centerCrop"
        android:src="@mipmap/ranklist_first" />

    <TextView
        android:id="@+id/tv_item_rank_update_frequency"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/iv_item_rank_cover_image"
        android:layout_alignBottom="@+id/iv_item_rank_cover_image"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="5dp"
        android:layout_marginBottom="5dp"
        android:gravity="center_horizontal"
        android:text="每周一更新"
        android:textSize="10sp"
        android:textColor="@color/white" />
    <TextView
        android:id="@+id/tv_item_rank_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="榜单"
        android:textSize="12sp"
        android:layout_below="@+id/iv_item_rank_cover_image"
        android:maxLines="2"
        android:textColor="@color/black"
        android:layout_alignLeft="@+id/iv_item_rank_cover_image"/>

</RelativeLayout>