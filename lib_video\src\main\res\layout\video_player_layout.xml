<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/mraid_content_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <TextureView
        android:id="@+id/xadsdk_player_video_textureView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:duplicateParentState="true" />

    <ImageView
        android:id="@+id/xadsdk_to_full_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="10dp"
        android:layout_marginRight="10dp"
        android:src="@drawable/xadsdk_ad_mini" />

    <Button
        android:id="@+id/xadsdk_small_play_btn"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_centerInParent="true"
        android:background="@drawable/xadsdk_ad_play"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/loading_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/xadsdk_ad_loading_anim" />
</RelativeLayout>