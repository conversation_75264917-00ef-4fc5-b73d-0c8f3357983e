<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_dialog_corner_normal"
    android:orientation="vertical"
    android:paddingLeft="30dp"
    android:paddingTop="20dp"
    android:paddingBottom="20dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="选择排序方式"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold" />

    <LinearLayout
        android:id="@+id/ll_notimer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:visibility="visible"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="4"
            android:text="不开启"
            android:textColor="@color/black"
            android:textSize="15dp" />

        <ImageView
            android:id="@+id/iv_notimer_check"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="30dp"
            android:layout_weight="3"
            android:visibility="gone"
            android:src="@drawable/ic_check_red" />

    </LinearLayout>
    <LinearLayout
        android:id="@+id/ll_10timer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:visibility="visible"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="4"
            android:text="10分钟后"
            android:textColor="@color/black"
            android:textSize="15dp" />

        <ImageView
            android:id="@+id/iv_10min_check"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="30dp"
            android:layout_weight="3"
            android:visibility="gone"
            android:src="@drawable/ic_check_red" />

    </LinearLayout>
    <LinearLayout
        android:id="@+id/ll_20timer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:visibility="visible"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="4"
            android:text="20分钟后"
            android:textColor="@color/black"
            android:textSize="15dp" />

        <ImageView
            android:id="@+id/iv_20min_check"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="30dp"
            android:layout_weight="3"
            android:visibility="gone"
            android:src="@drawable/ic_check_red" />

    </LinearLayout>
    <LinearLayout
        android:id="@+id/ll_30timer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:visibility="visible"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="4"
            android:text="30分钟后"
            android:textColor="@color/black"
            android:textSize="15dp" />

        <ImageView
            android:id="@+id/iv_30min_check"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="30dp"
            android:layout_weight="3"
            android:visibility="gone"
            android:src="@drawable/ic_check_red" />

    </LinearLayout>
    <LinearLayout
        android:id="@+id/ll_45timer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:visibility="visible"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="4"
            android:text="45分钟后"
            android:textColor="@color/black"
            android:textSize="15dp" />

        <ImageView
            android:id="@+id/iv_45min_check"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="30dp"
            android:layout_weight="3"
            android:visibility="gone"
            android:src="@drawable/ic_check_red" />

    </LinearLayout>
    <LinearLayout
        android:id="@+id/ll_60timer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:visibility="visible"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="4"
            android:text="60分钟后"
            android:textColor="@color/black"
            android:textSize="15dp" />

        <ImageView
            android:id="@+id/iv_60min_check"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="30dp"
            android:layout_weight="3"
            android:visibility="gone"
            android:src="@drawable/ic_check_red" />

    </LinearLayout>
</LinearLayout>