apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.android.compileSdkVersion
    buildToolsVersion rootProject.android.buildToolsVersion
    namespace 'com.imooc.lib_network'

    defaultConfig {
        minSdkVersion rootProject.android.minSdkVersion
        targetSdkVersion rootProject.android.targetSdkVersion
        versionCode rootProject.android.versionCode
        versionName rootProject.android.versionName
        multiDexEnabled rootProject.android.multiDexEnabled

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api rootProject.depsLibs.okhttp //okttp依赖
    api rootProject.depsLibs.cookieJar //cookie依赖
    api rootProject.depsLibs.gson

}
