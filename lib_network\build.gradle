apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.android.compileSdkVersion
    buildToolsVersion rootProject.android.buildToolsVersion

    defaultConfig {
        applicationId rootProject.android.applicationId
        minSdkVersion rootProject.android.minSdkVersion
        targetSdkVersion rootProject.android.targetSdkVersion
        versionCode rootProject.android.versionCode
        versionName rootProject.android.versionName
        multiDexEnabled rootProject.android.multiDexEnabled
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api rootProject.depsLibs.okhttp //okttp依赖
    api rootProject.depsLibs.cookieJar //cookie依赖
    api rootProject.depsLibs.gson

}
