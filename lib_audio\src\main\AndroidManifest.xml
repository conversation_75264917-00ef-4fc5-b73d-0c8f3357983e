<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.imooc.lib_audio">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <application android:label="@string/app_name">
        <service
            android:name=".mediaplayer.core.MusicService"
            android:enabled="true"
            android:exported="false" />

        <activity
            android:name=".mediaplayer.view.MusicPlayerActivity"
            android:enabled="true"
            android:exported="false"
            android:launchMode="singleTask" />
    </application>
</manifest>
