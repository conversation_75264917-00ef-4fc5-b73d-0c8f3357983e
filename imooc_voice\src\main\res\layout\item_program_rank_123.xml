<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="190dp">

    <ImageView
        android:id="@+id/iv_item_program_rank_avatar"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:src="@drawable/ic_test" />

    <ImageView
        android:id="@+id/iv_item_rank_program_play"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignLeft="@+id/iv_item_program_rank_avatar"
        android:layout_alignBottom="@+id/iv_item_program_rank_avatar"
        android:layout_marginLeft="82dp"
        android:src="@drawable/ic_play_red"
        android:visibility="visible" />

    <TextView
        android:id="@+id/tv_item_rank_program_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_item_program_rank_avatar"
        android:layout_alignLeft="@+id/iv_item_program_rank_avatar"
        android:layout_alignRight="@+id/iv_item_program_rank_avatar"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="8dp"
        android:ellipsize="end"
        android:gravity="center_horizontal"
        android:maxLines="2"
        android:text="名字"
        android:textColor="@color/black"
        android:textSize="11sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_alignParentBottom="true"
        android:background="@color/app_background" />
</RelativeLayout>