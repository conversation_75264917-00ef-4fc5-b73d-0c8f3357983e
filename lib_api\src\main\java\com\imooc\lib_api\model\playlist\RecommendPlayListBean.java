package com.imooc.lib_api.model.playlist;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 歌单模块的歌单Bean
 */
public class RecommendPlayListBean {

    /**
     * playlists : [{"name":"别担心 迟早都会和喜欢的人在一起的","id":2874150248,"trackNumberUpdateTime":1563267826448,"status":0,"userId":*********,"createTime":1562420431857,"updateTime":1563267877078,"subscribedCount":169,"trackCount":70,"cloudTrackCount":0,"coverImgUrl":"http://p2.music.126.net/Pj7wd9IRiwOi4FaAp6Vhrg==/109951164202276724.jpg","coverImgId":109951164202276720,"description":"别担心\n你终会遇见这样的一个人\n好的总是压箱底\n所有的不期而遇\n只为遇见你\n无论你有多么羡慕别人的感情\n无论你现在多么地孤独\n那份属于你独一无二的情感\n正在向你赶来\n你千万不要担心\n那个属于你的人\n也一定在快马加鞭向你赶来","tags":["华语","流行","治愈"],"playCount":26164,"trackUpdateTime":*************,"specialType":0,"totalDuration":0,"creator":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/GXWLdisZgSlb2hRYEqsG9A==/109951164185001989.jpg","accountStatus":0,"gender":2,"city":1010000,"birthday":************,"userId":*********,"userType":200,"nickname":"螚安Vivienne","signature":"從紅著臉到紅著眼","description":"","detailDescription":"","avatarImgId":109951164185001980,"backgroundImgId":109951164185008850,"backgroundUrl":"http://p1.music.126.net/3tL-lIqtyJCXU-5DtQ32wg==/109951164185008847.jpg","authority":0,"mutual":false,"expertTags":["华语","欧美","民谣"],"experts":{"2":"情感图文达人"},"djStatus":10,"vipType":11,"remarkName":null,"avatarImgIdStr":"109951164185001989","backgroundImgIdStr":"109951164185008847","avatarImgId_str":"109951164185001989"},"tracks":null,"subscribers":[{"defaultAvatar":false,"province":320000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/0yvAy0431O05Yl7eiiQIrw==/*****************.jpg","accountStatus":0,"gender":2,"city":320400,"birthday":-*************,"userId":*********,"userType":0,"nickname":"帆帆帆帆帆世界爱你","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":109951162868128400,"backgroundUrl":"http://p1.music.126.net/2zSNIqTcpHL2jIvU6hG0EA==/109951162868128395.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"*****************","backgroundImgIdStr":"109951162868128395","avatarImgId_str":"*****************"}],"subscribed":false,"commentThreadId":"A_PL_0_2874150248","newImported":false,"adType":0,"highQuality":false,"privacy":0,"ordered":true,"anonimous":false,"shareCount":3,"coverImgId_str":"109951164202276724","commentCount":8,"alg":"alg_sq_featured"},{"name":"赛博朋克 迷失未来","id":**********,"trackNumberUpdateTime":************8,"status":0,"userId":*********,"createTime":*************,"updateTime":************8,"subscribedCount":13,"trackCount":45,"cloudTrackCount":0,"coverImgUrl":"http://p2.music.126.net/eILEFP1UYZHuHyZ_nazStw==/109951164194738139.jpg","coverImgId":109951164194738140,"description":"赛博朋克，Cyberpunk，字面意思是 Cybernetics 与 Punk 的结合。前者代表以电子（仿生）人、复制人、机器人为标志的高科技，后者则是朋克精神。赛博朋克便是二者融合而成、未来式的、反乌托邦世界观。人类处于拥有相当发达程度科技、却又缺失人性关怀，低生活水平的社会。简言之，这是与蒸汽朋克所寄托的\u201d理想未来\u201c相对的，\u201d脏未来\u201c，融入其中的，是现实人类对未知未来的恐惧和悲观。\n\n作为反主流文化，赛博朋克源起于威廉·吉布森的小说《神经漫游者》，兴盛于雷德利·斯科特的电影《银翼杀手》，以及《攻壳机动队》等后来者，与时兴的电子游戏和动漫文化一起，深刻地影响了80年代流行音乐，Synthwave（合成器浪潮） 便是其中的典型。Synthwave 与赛博朋克文化相互影响与交融，（80年代初大量科幻电影采用合成器配乐，如银翼杀手中范吉斯的电子乐），紧密地联系在一起，使得 Synthwave，以其未来复古风，逐渐成为赛博朋克文化的符号之一，并深入人心。\n\n赛博朋克元素：雨夜，潮湿，红蓝，霓虹，东方，贫民窟，酒吧，高楼，机械仿生，垄断公司，控制论，人工智能，生物科技\n\n选曲风格：Synthwave\n封面：《银翼杀手 2049》","tags":["欧美","电子"],"playCount":2615,"trackUpdateTime":*************,"specialType":0,"totalDuration":0,"creator":{"defaultAvatar":false,"province":440000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/6Dtp4VRyT_kkNu7jpRuVWw==/109951163828047508.jpg","accountStatus":0,"gender":1,"city":445200,"birthday":************,"userId":*********,"userType":0,"nickname":"Galadhrim","signature":"maybe I have miles to drive and moments to catch","description":"","detailDescription":"","avatarImgId":109951163828047500,"backgroundImgId":109951163828005970,"backgroundUrl":"http://p1.music.126.net/Vfk0Pm0I0Lvug2Maumyssw==/109951163828005962.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951163828047508","backgroundImgIdStr":"109951163828005962","avatarImgId_str":"109951163828047508"},"tracks":null,"subscribers":[],"subscribed":false,"commentThreadId":"A_PL_0_**********","newImported":false,"adType":0,"highQuality":false,"privacy":0,"ordered":true,"anonimous":false,"shareCount":4,"coverImgId_str":"109951164194738139","commentCount":0,"alg":"alg_sq_featured"},{"name":"『轻音乐』清凉如水 给夏天加点冰","id":**********,"trackNumberUpdateTime":*************,"status":0,"userId":**********,"createTime":1549988629304,"updateTime":*************,"subscribedCount":145,"trackCount":65,"cloudTrackCount":0,"coverImgUrl":"http://p1.music.126.net/DG4DSwhdv38TGjnyn412cw==/109951163973121663.jpg","coverImgId":109951163973121660,"description":"像清晨的第一口橘子汽水，甘甜回味\n\n像午后的第一杯蜂蜜花茶，香如兰桂\n\n像深夜的第一声唏嘘虫鸣，动听优美\n\nʕ๑\u2022㉨\u2022๑ʔ夏日必备的解暑清凉纯音♪\n\n♡ ♡ ♡ ♡ ♡聆听入境♡ ♡ ♡ ♡ ♡","tags":["轻音乐","清新","治愈"],"playCount":6769,"trackUpdateTime":*************,"specialType":0,"totalDuration":0,"creator":{"defaultAvatar":false,"province":430000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/ACLUcvchE5n2e9a1hsJV9Q==/109951164159949385.jpg","accountStatus":0,"gender":2,"city":430600,"birthday":-*************,"userId":**********,"userType":200,"nickname":"浮春梦雪","signature":"一枚画师，正在努力学习中.所有在网易云发布的作品都禁止商用｜大概是因为，遇见了星星。｜路途遥远，且行且歌.｜永远爱轩轩\n\n【欢迎优质原创音乐人的投稿】","description":"","detailDescription":"","avatarImgId":109951164159949390,"backgroundImgId":109951164163729020,"backgroundUrl":"http://p1.music.126.net/7tAv_uqiODcg_Nbm1ztFZw==/109951164163729025.jpg","authority":0,"mutual":false,"expertTags":["日语","轻音乐","ACG"],"experts":{"2":"生活|动漫图文达人"},"djStatus":0,"vipType":11,"remarkName":null,"avatarImgIdStr":"109951164159949385","backgroundImgIdStr":"109951164163729025","avatarImgId_str":"109951164159949385"},"tracks":null,"subscribers":[{"defaultAvatar":false,"province":530000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/-jzXtr2bM43xkonVPtPLVA==/109951164155511321.jpg","accountStatus":0,"gender":2,"city":530100,"birthday":-*************,"userId":**********,"userType":0,"nickname":"旧忆浅光","signature":"","description":"","detailDescription":"","avatarImgId":109951164155511330,"backgroundImgId":109951164155509260,"backgroundUrl":"http://p1.music.126.net/VO5jLfYdOpGAGebjAlyzZg==/109951164155509263.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951164155511321","backgroundImgIdStr":"109951164155509263","avatarImgId_str":"109951164155511321"}],"subscribed":false,"commentThreadId":"A_PL_0_**********","newImported":false,"adType":0,"highQuality":false,"privacy":0,"ordered":true,"anonimous":false,"shareCount":2,"coverImgId_str":"109951163973121663","commentCount":4,"alg":"alg_sq_featured"},{"name":"真希望一觉醒来，还在初中的某一节课上","id":**********,"trackNumberUpdateTime":*************,"status":0,"userId":*********,"createTime":*************,"updateTime":*************,"subscribedCount":114,"trackCount":40,"cloudTrackCount":0,"coverImgUrl":"http://p1.music.126.net/R2kYRJ5fT7pEupQnGKCSpg==/109951163959633011.jpg","coverImgId":109951163959633010,"description":"常常在想，如果突然醒来，发现自己还在初中的某一节课上该多好，这些年发生的一切只不过是课间不小心睡着做的一个梦\u2026\u2026可是，这个世界上没有如果，我们也再回不去了\n\n每当这些歌曲前奏一出来一开口，我便知道那是中学的时光\u2026\u2026那年背着书包，里面除了课本，还有\u2026\u2026青春年少","tags":["华语","90后","怀旧"],"playCount":14736,"trackUpdateTime":*************,"specialType":0,"totalDuration":0,"creator":{"defaultAvatar":false,"province":440000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/4olggUjlN_H1CNyh6yHyGQ==/109951164214501534.jpg","accountStatus":0,"gender":2,"city":440900,"birthday":-*************,"userId":*********,"userType":200,"nickname":"-穷尽诗家笔-","signature":"依旧是偏爱荔枝入梦的时节","description":"","detailDescription":"","avatarImgId":109951164214501540,"backgroundImgId":109951164202318720,"backgroundUrl":"http://p1.music.126.net/hzuUJGS0T4Xgn-MpxmTn4w==/109951164202318727.jpg","authority":0,"mutual":false,"expertTags":["古风","华语","流行"],"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951164214501534","backgroundImgIdStr":"109951164202318727","avatarImgId_str":"109951164214501534"},"tracks":null,"subscribers":[{"defaultAvatar":false,"province":130000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/EH9C2t5Gjm2FHeYwn0aHjg==/109951163826245157.jpg","accountStatus":0,"gender":2,"city":130200,"birthday":-*************,"userId":**********,"userType":0,"nickname":"言熙___","signature":"不好好学习祁醉都嫌给你讲他和于炀的故事浪费时间","description":"","detailDescription":"","avatarImgId":109951163826245150,"backgroundImgId":109951163967242530,"backgroundUrl":"http://p1.music.126.net/cdOMZCytsKfR5QpsRhyxrQ==/109951163967242523.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951163826245157","backgroundImgIdStr":"109951163967242523","avatarImgId_str":"109951163826245157"}],"subscribed":false,"commentThreadId":"A_PL_0_**********","newImported":false,"adType":0,"highQuality":false,"privacy":0,"ordered":true,"anonimous":false,"shareCount":2,"coverImgId_str":"109951163959633011","commentCount":8,"alg":"alg_sq_featured"},{"name":"夏日女嗓 | 追一场落日和汽水味的海风","id":**********,"trackNumberUpdateTime":*************,"status":0,"userId":*********,"createTime":1560344277469,"updateTime":*************,"subscribedCount":150,"trackCount":42,"cloudTrackCount":0,"coverImgUrl":"http://p1.music.126.net/Ctocs8A2LGxUTP9n3vvulg==/109951164154739088.jpg","coverImgId":109951164154739090,"description":"【夏日寻声计划之女声篇】\n\n清新的电子\n如同夏季烈日下行道树投出的一片荫凉\n又酸又甜的女声是不可多得的宝藏 \n如温温的暖风穿过袖口\n一字一句抵达心脏\n像极了初夏永远绯红的夕阳\n也像那一口让你解暑的花草冰\n明媚又让人难忘。\n\n\u2014\u2014\u2014\u2014夏季寻音计划之女嗓篇\u2014\u2014\u2014\u2014\n\n时间：2019.6.14.\n文字：来自同学（wb：最后一丝呼吸）","tags":["欧美","电子","兴奋"],"playCount":14870,"trackUpdateTime":*************,"specialType":0,"totalDuration":0,"creator":{"defaultAvatar":false,"province":500000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/J_hHCs1o30muzI6-LYJzsA==/109951164196080852.jpg","accountStatus":0,"gender":1,"city":500101,"birthday":************,"userId":*********,"userType":200,"nickname":"LeoOfficial","signature":"各有各一生一世，各有各的温柔乡","description":"","detailDescription":"","avatarImgId":109951164196080850,"backgroundImgId":109951164207465260,"backgroundUrl":"http://p1.music.126.net/6QjY0l8Vjv1ryHO5WcHkkQ==/109951164207465271.jpg","authority":0,"mutual":false,"expertTags":["电子","流行","欧美"],"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951164196080852","backgroundImgIdStr":"109951164207465271","avatarImgId_str":"109951164196080852"},"tracks":null,"subscribers":[{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/PFDCS1ZdXRCVd-wtOjmkPg==/109951163738040813.jpg","accountStatus":0,"gender":2,"city":1001300,"birthday":-*************,"userId":*********,"userType":0,"nickname":"ChrissyChien","signature":"","description":"","detailDescription":"","avatarImgId":109951163738040820,"backgroundImgId":109951162928879380,"backgroundUrl":"http://p1.music.126.net/w2eU2ief7vvTKjes7k_0Wg==/109951162928879369.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951163738040813","backgroundImgIdStr":"109951162928879369","avatarImgId_str":"109951163738040813"}],"subscribed":false,"commentThreadId":"A_PL_0_**********","newImported":false,"adType":0,"highQuality":false,"privacy":0,"ordered":true,"anonimous":false,"shareCount":11,"coverImgId_str":"109951164154739088","commentCount":5,"alg":"alg_sq_featured"},{"name":"『ACG/魔性』当声优在KTV放飞自我","id":**********,"trackNumberUpdateTime":*************,"status":0,"userId":*********,"createTime":*************,"updateTime":*************,"subscribedCount":21,"trackCount":19,"cloudTrackCount":0,"coverImgUrl":"http://p1.music.126.net/pxkWg7wwlKUyyHUp20K8Rg==/109951164210106875.jpg","coverImgId":109951164210106880,"description":"精心挑选了一些动漫中出现的KTV场景，并放上原版/正常发挥版进行比较，来感受声优放飞自我时与平时不一样的魔性吧！（欢迎补充）\n\n《日常》\n1-3：KTV版\n4：原版op\n\n《神薙》\n5、7、9：KTV版\n6、8、10：正常发挥版\n\n《幸运星》\n11、13、15、17：KTV版\n12、14、16、18：原版/完整版\n19：小神晶/博士你怎么了！","tags":["日语","ACG","影视原声"],"playCount":2321,"trackUpdateTime":*************,"specialType":0,"totalDuration":0,"creator":{"defaultAvatar":false,"province":340000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/xhJ6iK41u8uEawJFxrl5gg==/109951163581269254.jpg","accountStatus":0,"gender":1,"city":340800,"birthday":************,"userId":*********,"userType":0,"nickname":"-弘木-","signature":"充电中，中午和晚上不定时上线回私信/看评论/暖动态\rACG、电音、古风音乐爱好者丨节奏控 | 补番狂魔 \r现主听ACG，业余做些推番歌单\r只要你喜欢『许嵩』『米津玄師 』『秋田弘 』『Ingrid Michaelson』『Imagine Dragons』『骨头社』『京阿尼』『疯房子』，我们就是好朋友","description":"","detailDescription":"","avatarImgId":109951163581269250,"backgroundImgId":109951164217145220,"backgroundUrl":"http://p1.music.126.net/FyowuFFsfrtwg_s4AH-CLg==/109951164217145224.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":11,"remarkName":null,"avatarImgIdStr":"109951163581269254","backgroundImgIdStr":"109951164217145224","avatarImgId_str":"109951163581269254"},"tracks":null,"subscribers":[{"defaultAvatar":false,"province":210000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/_NHXASQli_APNy_IFtaoLQ==/109951164094149802.jpg","accountStatus":0,"gender":1,"city":210300,"birthday":************,"userId":**********,"userType":0,"nickname":"藤原我的书记","signature":"闷骚达人/半点赞狂魔","description":"","detailDescription":"","avatarImgId":109951164094149810,"backgroundImgId":109951164094146370,"backgroundUrl":"http://p1.music.126.net/nNkSjXb-XBCeUfvKmGihzg==/109951164094146366.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951164094149802","backgroundImgIdStr":"109951164094146366","avatarImgId_str":"109951164094149802"}],"subscribed":false,"commentThreadId":"A_PL_0_**********","newImported":false,"adType":0,"highQuality":false,"privacy":0,"ordered":true,"anonimous":false,"shareCount":4,"coverImgId_str":"109951164210106875","commentCount":0,"alg":"alg_sq_featured"},{"name":"高级感Vlog纯音乐♡","id":**********,"trackNumberUpdateTime":*************,"status":0,"userId":*********,"createTime":*************,"updateTime":*************,"subscribedCount":24211,"trackCount":100,"cloudTrackCount":0,"coverImgUrl":"http://p1.music.126.net/W5T5TQJhQGB2H90jGJg_6Q==/109951164017144832.jpg","coverImgId":109951164017144830,"description":"喜欢是藏不住的 藏久了会难受♡","tags":["另类/独立","浪漫"],"playCount":555625,"trackUpdateTime":*************,"specialType":0,"totalDuration":0,"creator":{"defaultAvatar":false,"province":210000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/pN4Mztq1L29jwqwl8AcuFA==/109951164165755195.jpg","accountStatus":0,"gender":2,"city":210100,"birthday":*************,"userId":*********,"userType":0,"nickname":"da_vinci_leonardo","signature":"情有独钟","description":"","detailDescription":"","avatarImgId":109951164165755200,"backgroundImgId":109951164165748930,"backgroundUrl":"http://p1.music.126.net/QsDylE4reygBV75Bb73Cww==/109951164165748928.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":11,"remarkName":null,"avatarImgIdStr":"109951164165755195","backgroundImgIdStr":"109951164165748928","avatarImgId_str":"109951164165755195"},"tracks":null,"subscribers":[{"defaultAvatar":false,"province":530000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/pUwxoIG5LuPGgeE6Aofnhw==/*****************.jpg","accountStatus":0,"gender":1,"city":532800,"birthday":-*************,"userId":*********,"userType":0,"nickname":"justexperience","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/bmA_ablsXpq3Tk9HlEg9sA==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":10,"remarkName":null,"avatarImgIdStr":"*****************","backgroundImgIdStr":"****************","avatarImgId_str":"*****************"}],"subscribed":false,"commentThreadId":"A_PL_0_**********","newImported":false,"adType":0,"highQuality":false,"privacy":0,"ordered":true,"anonimous":false,"shareCount":123,"coverImgId_str":"109951164017144832","commentCount":145,"alg":"alg_sq_topn_lr"},{"name":"【灵感时刻】独立音乐人的优秀作品集","id":**********,"trackNumberUpdateTime":*************,"status":0,"userId":897345,"createTime":*************,"updateTime":1554982742742,"subscribedCount":10519,"trackCount":300,"cloudTrackCount":0,"coverImgUrl":"http://p1.music.126.net/J0FdWp1LvsyFH7w0yme8sg==/109951163990952840.jpg","coverImgId":109951163990952830,"description":"因为独立，所以没有音乐之外的羁绊，音乐人可以随心所欲记录灵光乍现时的音乐感觉，从而创作出令人耳目一新的作品。\n\n本单主要收录了独立音乐人创作的一些优秀作品，希望大家喜欢哦！","tags":["欧美","放松","另类/独立"],"playCount":438986,"trackUpdateTime":*************,"specialType":0,"totalDuration":0,"creator":{"defaultAvatar":false,"province":310000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/59fQYIif4_Vn3o-D850e_Q==/****************.jpg","accountStatus":0,"gender":1,"city":310101,"birthday":************,"userId":897345,"userType":200,"nickname":"pure日月","signature":"菩提本无树，明镜亦非台，本来无一物，何处惹尘埃。     PS：我下载的音乐详见歌单\u2014\u2014《音乐播放器保留曲目》。 PPS：思想碰撞-知乎@pure日月。","description":"","detailDescription":"","avatarImgId":****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/YSl7l-KoR42rWfEBbpXbDA==/****************.jpg","authority":0,"mutual":false,"expertTags":["轻音乐","世界音乐","古典"],"experts":{"2":"生活图文达人"},"djStatus":10,"vipType":11,"remarkName":null,"avatarImgIdStr":"****************","backgroundImgIdStr":"****************"},"tracks":null,"subscribers":[{"defaultAvatar":false,"province":440000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/k3H2i7Ols0q68QQJuqOm9g==/109951164095434577.jpg","accountStatus":0,"gender":0,"city":440100,"birthday":************,"userId":**********,"userType":0,"nickname":"Lieatxiang","signature":"","description":"","detailDescription":"","avatarImgId":109951164095434580,"backgroundImgId":109951162868126480,"backgroundUrl":"http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":11,"remarkName":null,"avatarImgIdStr":"109951164095434577","backgroundImgIdStr":"109951162868126486","avatarImgId_str":"109951164095434577"}],"subscribed":false,"commentThreadId":"A_PL_0_**********","newImported":false,"adType":0,"highQuality":false,"privacy":0,"ordered":true,"anonimous":false,"shareCount":221,"coverImgId_str":"109951163990952840","commentCount":45,"alg":"alg_sq_topn_lr"},{"name":"[华语私人订制] 愿你会爱上这些华语好歌","id":**********,"trackNumberUpdateTime":*************,"status":0,"userId":1,"createTime":*************,"updateTime":0,"subscribedCount":29666,"trackCount":30,"cloudTrackCount":0,"coverImgUrl":"http://p1.music.126.net/KEBE7CU-y2lInkHBcgzpUA==/109951164173101961.jpg","coverImgId":109951164173101970,"description":"无论新歌、老歌，都是耐听的华语歌\n为你专属订制的华语歌单，每日上新\n记得收藏订阅，歌荒？不存在的事~~","tags":["华语"],"playCount":2989516,"trackUpdateTime":*************,"specialType":100,"totalDuration":0,"creator":{"defaultAvatar":false,"province":110000,"authStatus":1,"followed":false,"avatarUrl":"http://p1.music.126.net/QWMV-Ru_6149AKe0mCBXKg==/****************.jpg","accountStatus":0,"gender":1,"city":110101,"birthday":-*************,"userId":1,"userType":2,"nickname":"网易云音乐","signature":"网易云音乐是6亿人都在使用的音乐平台，致力于帮助用户发现音乐惊喜，帮助音乐人实现梦想。客服@云音乐客服 在线时间：9：00 - 24：00，如您在使用过程中遇到任何问题，欢迎私信咨询，我们会尽快回复。如果仍然不能解决您的问题，与活动相关的疑问请私信@云音乐客服","description":"网易云音乐官方账号","detailDescription":"网易云音乐官方账号","avatarImgId":****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/pmHS4fcQtcNEGewNb5HRhg==/****************.jpg","authority":3,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":11,"remarkName":null,"avatarImgIdStr":"****************","backgroundImgIdStr":"****************"},"tracks":null,"subscribers":[{"defaultAvatar":true,"province":430000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/VnZiScyynLG7atLIZ2YPkw==/*****************.jpg","accountStatus":0,"gender":0,"city":431300,"birthday":-*************,"userId":*********,"userType":0,"nickname":"哎老男人","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":109951162868128400,"backgroundUrl":"http://p1.music.126.net/2zSNIqTcpHL2jIvU6hG0EA==/109951162868128395.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"*****************","backgroundImgIdStr":"109951162868128395","avatarImgId_str":"*****************"}],"subscribed":false,"commentThreadId":"A_PL_0_**********","newImported":false,"adType":0,"highQuality":false,"privacy":0,"ordered":false,"anonimous":false,"shareCount":121,"commentCount":154,"alg":"official_playlist_lanMatch"},{"name":"夏季纯音|竹深树密虫鸣处，时有微凉不是风","id":**********,"trackNumberUpdateTime":*************,"status":0,"userId":*********,"createTime":*************,"updateTime":*************,"subscribedCount":23851,"trackCount":94,"cloudTrackCount":0,"coverImgUrl":"http://p1.music.126.net/rK-S6tg1WrJ-JaGxl8HQ-g==/109951164052424929.jpg","coverImgId":109951164052424930,"description":"一些关于夏天的纯音。从初夏，仲夏，夏末的顺序排列\n\n拒绝复制粘贴\n\n夜热依然午热同，开门小立月明中。\n竹深树密虫鸣处，时有微凉不是风。 \n\u2014\u2014《夏夜追凉》\n\n封面画师：DSマイル","tags":["治愈","轻音乐","清新"],"playCount":848465,"trackUpdateTime":*************,"specialType":0,"totalDuration":0,"creator":{"defaultAvatar":false,"province":440000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/TDtdP31ry5QcSh0yU22RiQ==/109951164168677258.jpg","accountStatus":0,"gender":2,"city":440200,"birthday":*************,"userId":*********,"userType":0,"nickname":"不曾闻风起","signature":"痛苦更能引发思考","description":"","detailDescription":"","avatarImgId":109951164168677260,"backgroundImgId":109951164189187260,"backgroundUrl":"http://p1.music.126.net/BhHvFl-u72wlNdiBr6P9Ig==/109951164189187258.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951164168677258","backgroundImgIdStr":"109951164189187258","avatarImgId_str":"109951164168677258"},"tracks":null,"subscribers":[{"defaultAvatar":true,"province":110000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/VnZiScyynLG7atLIZ2YPkw==/*****************.jpg","accountStatus":0,"gender":0,"city":110108,"birthday":-*************,"userId":********,"userType":0,"nickname":"美玉啊美玉","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/bmA_ablsXpq3Tk9HlEg9sA==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"*****************","backgroundImgIdStr":"****************","avatarImgId_str":"*****************"}],"subscribed":false,"commentThreadId":"A_PL_0_**********","newImported":false,"adType":0,"highQuality":false,"privacy":0,"ordered":true,"anonimous":false,"shareCount":233,"coverImgId_str":"109951164052424929","commentCount":106,"alg":"alg_sq_topn_lr"}]
     * total : 1305
     * code : 200
     * more : true
     * cat : 全部
     */

    private int total;
    private int code;
    private boolean more;
    private String cat;
    private List<PlaylistsBean> playlists;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public boolean isMore() {
        return more;
    }

    public void setMore(boolean more) {
        this.more = more;
    }

    public String getCat() {
        return cat;
    }

    public void setCat(String cat) {
        this.cat = cat;
    }

    public List<PlaylistsBean> getPlaylists() {
        return playlists;
    }

    public void setPlaylists(List<PlaylistsBean> playlists) {
        this.playlists = playlists;
    }

    public static class PlaylistsBean {
        /**
         * name : 别担心 迟早都会和喜欢的人在一起的
         * id : 2874150248
         * trackNumberUpdateTime : 1563267826448
         * status : 0
         * userId : *********
         * createTime : 1562420431857
         * updateTime : 1563267877078
         * subscribedCount : 169
         * trackCount : 70
         * cloudTrackCount : 0
         * coverImgUrl : http://p2.music.126.net/Pj7wd9IRiwOi4FaAp6Vhrg==/109951164202276724.jpg
         * coverImgId : 109951164202276720
         * description : 别担心
         你终会遇见这样的一个人
         好的总是压箱底
         所有的不期而遇
         只为遇见你
         无论你有多么羡慕别人的感情
         无论你现在多么地孤独
         那份属于你独一无二的情感
         正在向你赶来
         你千万不要担心
         那个属于你的人
         也一定在快马加鞭向你赶来
         * tags : ["华语","流行","治愈"]
         * playCount : 26164
         * trackUpdateTime : *************
         * specialType : 0
         * totalDuration : 0
         * creator : {"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/GXWLdisZgSlb2hRYEqsG9A==/109951164185001989.jpg","accountStatus":0,"gender":2,"city":1010000,"birthday":************,"userId":*********,"userType":200,"nickname":"螚安Vivienne","signature":"從紅著臉到紅著眼","description":"","detailDescription":"","avatarImgId":109951164185001980,"backgroundImgId":109951164185008850,"backgroundUrl":"http://p1.music.126.net/3tL-lIqtyJCXU-5DtQ32wg==/109951164185008847.jpg","authority":0,"mutual":false,"expertTags":["华语","欧美","民谣"],"experts":{"2":"情感图文达人"},"djStatus":10,"vipType":11,"remarkName":null,"avatarImgIdStr":"109951164185001989","backgroundImgIdStr":"109951164185008847","avatarImgId_str":"109951164185001989"}
         * tracks : null
         * subscribers : [{"defaultAvatar":false,"province":320000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/0yvAy0431O05Yl7eiiQIrw==/*****************.jpg","accountStatus":0,"gender":2,"city":320400,"birthday":-*************,"userId":*********,"userType":0,"nickname":"帆帆帆帆帆世界爱你","signature":"","description":"","detailDescription":"","avatarImgId":*****************,"backgroundImgId":109951162868128400,"backgroundUrl":"http://p1.music.126.net/2zSNIqTcpHL2jIvU6hG0EA==/109951162868128395.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"*****************","backgroundImgIdStr":"109951162868128395","avatarImgId_str":"*****************"}]
         * subscribed : false
         * commentThreadId : A_PL_0_2874150248
         * newImported : false
         * adType : 0
         * highQuality : false
         * privacy : 0
         * ordered : true
         * anonimous : false
         * shareCount : 3
         * coverImgId_str : 109951164202276724
         * commentCount : 8
         * alg : alg_sq_featured
         */

        private String name;
        private long id;
        private long trackNumberUpdateTime;
        private int status;
        private String userId;
        private long createTime;
        private long updateTime;
        private int subscribedCount;
        private int trackCount;
        private int cloudTrackCount;
        private String coverImgUrl;
        private long coverImgId;
        private String description;
        private int playCount;
        private long trackUpdateTime;
        private int specialType;
        private int totalDuration;
        private CreatorBean creator;
        private Object tracks;
        private boolean subscribed;
        private String commentThreadId;
        private boolean newImported;
        private int adType;
        private boolean highQuality;
        private int privacy;
        private boolean ordered;
        private boolean anonimous;
        private int shareCount;
        private String coverImgId_str;
        private int commentCount;
        private String alg;
        private List<String> tags;
        private List<SubscribersBean> subscribers;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public long getTrackNumberUpdateTime() {
            return trackNumberUpdateTime;
        }

        public void setTrackNumberUpdateTime(long trackNumberUpdateTime) {
            this.trackNumberUpdateTime = trackNumberUpdateTime;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public long getCreateTime() {
            return createTime;
        }

        public void setCreateTime(long createTime) {
            this.createTime = createTime;
        }

        public long getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(long updateTime) {
            this.updateTime = updateTime;
        }

        public int getSubscribedCount() {
            return subscribedCount;
        }

        public void setSubscribedCount(int subscribedCount) {
            this.subscribedCount = subscribedCount;
        }

        public int getTrackCount() {
            return trackCount;
        }

        public void setTrackCount(int trackCount) {
            this.trackCount = trackCount;
        }

        public int getCloudTrackCount() {
            return cloudTrackCount;
        }

        public void setCloudTrackCount(int cloudTrackCount) {
            this.cloudTrackCount = cloudTrackCount;
        }

        public String getCoverImgUrl() {
            return coverImgUrl;
        }

        public void setCoverImgUrl(String coverImgUrl) {
            this.coverImgUrl = coverImgUrl;
        }

        public long getCoverImgId() {
            return coverImgId;
        }

        public void setCoverImgId(long coverImgId) {
            this.coverImgId = coverImgId;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public int getPlayCount() {
            return playCount;
        }

        public void setPlayCount(int playCount) {
            this.playCount = playCount;
        }

        public long getTrackUpdateTime() {
            return trackUpdateTime;
        }

        public void setTrackUpdateTime(long trackUpdateTime) {
            this.trackUpdateTime = trackUpdateTime;
        }

        public int getSpecialType() {
            return specialType;
        }

        public void setSpecialType(int specialType) {
            this.specialType = specialType;
        }

        public int getTotalDuration() {
            return totalDuration;
        }

        public void setTotalDuration(int totalDuration) {
            this.totalDuration = totalDuration;
        }

        public CreatorBean getCreator() {
            return creator;
        }

        public void setCreator(CreatorBean creator) {
            this.creator = creator;
        }

        public Object getTracks() {
            return tracks;
        }

        public void setTracks(Object tracks) {
            this.tracks = tracks;
        }

        public boolean isSubscribed() {
            return subscribed;
        }

        public void setSubscribed(boolean subscribed) {
            this.subscribed = subscribed;
        }

        public String getCommentThreadId() {
            return commentThreadId;
        }

        public void setCommentThreadId(String commentThreadId) {
            this.commentThreadId = commentThreadId;
        }

        public boolean isNewImported() {
            return newImported;
        }

        public void setNewImported(boolean newImported) {
            this.newImported = newImported;
        }

        public int getAdType() {
            return adType;
        }

        public void setAdType(int adType) {
            this.adType = adType;
        }

        public boolean isHighQuality() {
            return highQuality;
        }

        public void setHighQuality(boolean highQuality) {
            this.highQuality = highQuality;
        }

        public int getPrivacy() {
            return privacy;
        }

        public void setPrivacy(int privacy) {
            this.privacy = privacy;
        }

        public boolean isOrdered() {
            return ordered;
        }

        public void setOrdered(boolean ordered) {
            this.ordered = ordered;
        }

        public boolean isAnonimous() {
            return anonimous;
        }

        public void setAnonimous(boolean anonimous) {
            this.anonimous = anonimous;
        }

        public int getShareCount() {
            return shareCount;
        }

        public void setShareCount(int shareCount) {
            this.shareCount = shareCount;
        }

        public String getCoverImgId_str() {
            return coverImgId_str;
        }

        public void setCoverImgId_str(String coverImgId_str) {
            this.coverImgId_str = coverImgId_str;
        }

        public int getCommentCount() {
            return commentCount;
        }

        public void setCommentCount(int commentCount) {
            this.commentCount = commentCount;
        }

        public String getAlg() {
            return alg;
        }

        public void setAlg(String alg) {
            this.alg = alg;
        }

        public List<String> getTags() {
            return tags;
        }

        public void setTags(List<String> tags) {
            this.tags = tags;
        }

        public List<SubscribersBean> getSubscribers() {
            return subscribers;
        }

        public void setSubscribers(List<SubscribersBean> subscribers) {
            this.subscribers = subscribers;
        }

        public static class CreatorBean {
            /**
             * defaultAvatar : false
             * province : 1000000
             * authStatus : 0
             * followed : false
             * avatarUrl : http://p1.music.126.net/GXWLdisZgSlb2hRYEqsG9A==/109951164185001989.jpg
             * accountStatus : 0
             * gender : 2
             * city : 1010000
             * birthday : ************
             * userId : *********
             * userType : 200
             * nickname : 螚安Vivienne
             * signature : 從紅著臉到紅著眼
             * description :
             * detailDescription :
             * avatarImgId : 109951164185001980
             * backgroundImgId : 109951164185008850
             * backgroundUrl : http://p1.music.126.net/3tL-lIqtyJCXU-5DtQ32wg==/109951164185008847.jpg
             * authority : 0
             * mutual : false
             * expertTags : ["华语","欧美","民谣"]
             * experts : {"2":"情感图文达人"}
             * djStatus : 10
             * vipType : 11
             * remarkName : null
             * avatarImgIdStr : 109951164185001989
             * backgroundImgIdStr : 109951164185008847
             * avatarImgId_str : 109951164185001989
             */

            private boolean defaultAvatar;
            private int province;
            private int authStatus;
            private boolean followed;
            private String avatarUrl;
            private int accountStatus;
            private int gender;
            private int city;
            private long birthday;
            private String userId;
            private int userType;
            private String nickname;
            private String signature;
            private String description;
            private String detailDescription;
            private long avatarImgId;
            private long backgroundImgId;
            private String backgroundUrl;
            private int authority;
            private boolean mutual;
            private ExpertsBean experts;
            private int djStatus;
            private int vipType;
            private Object remarkName;
            private String avatarImgIdStr;
            private String backgroundImgIdStr;
            private String avatarImgId_str;
            private List<String> expertTags;

            public boolean isDefaultAvatar() {
                return defaultAvatar;
            }

            public void setDefaultAvatar(boolean defaultAvatar) {
                this.defaultAvatar = defaultAvatar;
            }

            public int getProvince() {
                return province;
            }

            public void setProvince(int province) {
                this.province = province;
            }

            public int getAuthStatus() {
                return authStatus;
            }

            public void setAuthStatus(int authStatus) {
                this.authStatus = authStatus;
            }

            public boolean isFollowed() {
                return followed;
            }

            public void setFollowed(boolean followed) {
                this.followed = followed;
            }

            public String getAvatarUrl() {
                return avatarUrl;
            }

            public void setAvatarUrl(String avatarUrl) {
                this.avatarUrl = avatarUrl;
            }

            public int getAccountStatus() {
                return accountStatus;
            }

            public void setAccountStatus(int accountStatus) {
                this.accountStatus = accountStatus;
            }

            public int getGender() {
                return gender;
            }

            public void setGender(int gender) {
                this.gender = gender;
            }

            public int getCity() {
                return city;
            }

            public void setCity(int city) {
                this.city = city;
            }

            public long getBirthday() {
                return birthday;
            }

            public void setBirthday(long birthday) {
                this.birthday = birthday;
            }

            public String getUserId() {
                return userId;
            }

            public void setUserId(String userId) {
                this.userId = userId;
            }

            public int getUserType() {
                return userType;
            }

            public void setUserType(int userType) {
                this.userType = userType;
            }

            public String getNickname() {
                return nickname;
            }

            public void setNickname(String nickname) {
                this.nickname = nickname;
            }

            public String getSignature() {
                return signature;
            }

            public void setSignature(String signature) {
                this.signature = signature;
            }

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public String getDetailDescription() {
                return detailDescription;
            }

            public void setDetailDescription(String detailDescription) {
                this.detailDescription = detailDescription;
            }

            public long getAvatarImgId() {
                return avatarImgId;
            }

            public void setAvatarImgId(long avatarImgId) {
                this.avatarImgId = avatarImgId;
            }

            public long getBackgroundImgId() {
                return backgroundImgId;
            }

            public void setBackgroundImgId(long backgroundImgId) {
                this.backgroundImgId = backgroundImgId;
            }

            public String getBackgroundUrl() {
                return backgroundUrl;
            }

            public void setBackgroundUrl(String backgroundUrl) {
                this.backgroundUrl = backgroundUrl;
            }

            public int getAuthority() {
                return authority;
            }

            public void setAuthority(int authority) {
                this.authority = authority;
            }

            public boolean isMutual() {
                return mutual;
            }

            public void setMutual(boolean mutual) {
                this.mutual = mutual;
            }

            public ExpertsBean getExperts() {
                return experts;
            }

            public void setExperts(ExpertsBean experts) {
                this.experts = experts;
            }

            public int getDjStatus() {
                return djStatus;
            }

            public void setDjStatus(int djStatus) {
                this.djStatus = djStatus;
            }

            public int getVipType() {
                return vipType;
            }

            public void setVipType(int vipType) {
                this.vipType = vipType;
            }

            public Object getRemarkName() {
                return remarkName;
            }

            public void setRemarkName(Object remarkName) {
                this.remarkName = remarkName;
            }

            public String getAvatarImgIdStr() {
                return avatarImgIdStr;
            }

            public void setAvatarImgIdStr(String avatarImgIdStr) {
                this.avatarImgIdStr = avatarImgIdStr;
            }

            public String getBackgroundImgIdStr() {
                return backgroundImgIdStr;
            }

            public void setBackgroundImgIdStr(String backgroundImgIdStr) {
                this.backgroundImgIdStr = backgroundImgIdStr;
            }

            public String getAvatarImgId_str() {
                return avatarImgId_str;
            }

            public void setAvatarImgId_str(String avatarImgId_str) {
                this.avatarImgId_str = avatarImgId_str;
            }

            public List<String> getExpertTags() {
                return expertTags;
            }

            public void setExpertTags(List<String> expertTags) {
                this.expertTags = expertTags;
            }

            public static class ExpertsBean {
                /**
                 * 2 : 情感图文达人
                 */

                @SerializedName("2")
                private String _$2;

                public String get_$2() {
                    return _$2;
                }

                public void set_$2(String _$2) {
                    this._$2 = _$2;
                }
            }
        }

        public static class SubscribersBean {
            /**
             * defaultAvatar : false
             * province : 320000
             * authStatus : 0
             * followed : false
             * avatarUrl : http://p1.music.126.net/0yvAy0431O05Yl7eiiQIrw==/*****************.jpg
             * accountStatus : 0
             * gender : 2
             * city : 320400
             * birthday : -*************
             * userId : *********
             * userType : 0
             * nickname : 帆帆帆帆帆世界爱你
             * signature :
             * description :
             * detailDescription :
             * avatarImgId : *****************
             * backgroundImgId : 109951162868128400
             * backgroundUrl : http://p1.music.126.net/2zSNIqTcpHL2jIvU6hG0EA==/109951162868128395.jpg
             * authority : 0
             * mutual : false
             * expertTags : null
             * experts : null
             * djStatus : 10
             * vipType : 0
             * remarkName : null
             * avatarImgIdStr : *****************
             * backgroundImgIdStr : 109951162868128395
             * avatarImgId_str : *****************
             */

            private boolean defaultAvatar;
            private int province;
            private int authStatus;
            private boolean followed;
            private String avatarUrl;
            private int accountStatus;
            private int gender;
            private int city;
            private long birthday;
            private String userId;
            private int userType;
            private String nickname;
            private String signature;
            private String description;
            private String detailDescription;
            private long avatarImgId;
            private long backgroundImgId;
            private String backgroundUrl;
            private int authority;
            private boolean mutual;
            private Object expertTags;
            private Object experts;
            private int djStatus;
            private int vipType;
            private Object remarkName;
            private String avatarImgIdStr;
            private String backgroundImgIdStr;
            private String avatarImgId_str;

            public boolean isDefaultAvatar() {
                return defaultAvatar;
            }

            public void setDefaultAvatar(boolean defaultAvatar) {
                this.defaultAvatar = defaultAvatar;
            }

            public int getProvince() {
                return province;
            }

            public void setProvince(int province) {
                this.province = province;
            }

            public int getAuthStatus() {
                return authStatus;
            }

            public void setAuthStatus(int authStatus) {
                this.authStatus = authStatus;
            }

            public boolean isFollowed() {
                return followed;
            }

            public void setFollowed(boolean followed) {
                this.followed = followed;
            }

            public String getAvatarUrl() {
                return avatarUrl;
            }

            public void setAvatarUrl(String avatarUrl) {
                this.avatarUrl = avatarUrl;
            }

            public int getAccountStatus() {
                return accountStatus;
            }

            public void setAccountStatus(int accountStatus) {
                this.accountStatus = accountStatus;
            }

            public int getGender() {
                return gender;
            }

            public void setGender(int gender) {
                this.gender = gender;
            }

            public int getCity() {
                return city;
            }

            public void setCity(int city) {
                this.city = city;
            }

            public long getBirthday() {
                return birthday;
            }

            public void setBirthday(long birthday) {
                this.birthday = birthday;
            }

            public String getUserId() {
                return userId;
            }

            public void setUserId(String userId) {
                this.userId = userId;
            }

            public int getUserType() {
                return userType;
            }

            public void setUserType(int userType) {
                this.userType = userType;
            }

            public String getNickname() {
                return nickname;
            }

            public void setNickname(String nickname) {
                this.nickname = nickname;
            }

            public String getSignature() {
                return signature;
            }

            public void setSignature(String signature) {
                this.signature = signature;
            }

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public String getDetailDescription() {
                return detailDescription;
            }

            public void setDetailDescription(String detailDescription) {
                this.detailDescription = detailDescription;
            }

            public long getAvatarImgId() {
                return avatarImgId;
            }

            public void setAvatarImgId(long avatarImgId) {
                this.avatarImgId = avatarImgId;
            }

            public long getBackgroundImgId() {
                return backgroundImgId;
            }

            public void setBackgroundImgId(long backgroundImgId) {
                this.backgroundImgId = backgroundImgId;
            }

            public String getBackgroundUrl() {
                return backgroundUrl;
            }

            public void setBackgroundUrl(String backgroundUrl) {
                this.backgroundUrl = backgroundUrl;
            }

            public int getAuthority() {
                return authority;
            }

            public void setAuthority(int authority) {
                this.authority = authority;
            }

            public boolean isMutual() {
                return mutual;
            }

            public void setMutual(boolean mutual) {
                this.mutual = mutual;
            }

            public Object getExpertTags() {
                return expertTags;
            }

            public void setExpertTags(Object expertTags) {
                this.expertTags = expertTags;
            }

            public Object getExperts() {
                return experts;
            }

            public void setExperts(Object experts) {
                this.experts = experts;
            }

            public int getDjStatus() {
                return djStatus;
            }

            public void setDjStatus(int djStatus) {
                this.djStatus = djStatus;
            }

            public int getVipType() {
                return vipType;
            }

            public void setVipType(int vipType) {
                this.vipType = vipType;
            }

            public Object getRemarkName() {
                return remarkName;
            }

            public void setRemarkName(Object remarkName) {
                this.remarkName = remarkName;
            }

            public String getAvatarImgIdStr() {
                return avatarImgIdStr;
            }

            public void setAvatarImgIdStr(String avatarImgIdStr) {
                this.avatarImgIdStr = avatarImgIdStr;
            }

            public String getBackgroundImgIdStr() {
                return backgroundImgIdStr;
            }

            public void setBackgroundImgIdStr(String backgroundImgIdStr) {
                this.backgroundImgIdStr = backgroundImgIdStr;
            }

            public String getAvatarImgId_str() {
                return avatarImgId_str;
            }

            public void setAvatarImgId_str(String avatarImgId_str) {
                this.avatarImgId_str = avatarImgId_str;
            }
        }
    }

    @Override
    public String toString() {
        return "RecommendPlayListBean{" +
                "total=" + total +
                ", code=" + code +
                ", more=" + more +
                ", cat='" + cat + '\'' +
                ", playlists=" + playlists +
                '}';
    }
}
