<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="400dp"
    android:background="@drawable/bg_dialog_sheet_header">

    <RelativeLayout
        android:id="@+id/title_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="10dp"
        android:paddingTop="10dp"
        android:paddingEnd="10dp"
        android:paddingBottom="10dp">

        <ImageView
            android:id="@+id/mode_image_view"
            android:layout_width="25dp"
            android:layout_height="15dp"
            android:layout_centerVertical="true" />

        <TextView
            android:id="@+id/mode_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="8dp"
            android:layout_toEndOf="@id/mode_image_view"
            android:textColor="#333333"
            android:textSize="15sp"
            tools:text="单曲循环" />

        <TextView
            android:id="@+id/num_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="2dp"
            android:layout_toEndOf="@id/mode_text_view"
            android:textColor="#333333"
            android:textSize="15sp"
            tools:text="(10)" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/tip_view"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_gravity="center"
                android:layout_marginRight="5dp"
                android:src="@drawable/ic_collect" />

            <TextView
                android:id="@+id/favourite_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="收藏全部"
                android:textColor="#333333"
                android:textSize="15sp" />

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:background="#dddddd" />

            <ImageView
                android:id="@+id/delete_view"
                android:layout_width="30dp"
                android:layout_height="35dp"
                android:src="@drawable/ic_delete" />
        </LinearLayout>
    </RelativeLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@id/title_layout"
        android:background="#dddddd" />

    <android.support.v7.widget.RecyclerView
        android:id="@+id/recycler"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/divider" />

</RelativeLayout>