<?xml version="1.0" encoding="utf-8"?>
<android.support.design.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/rl_user_detail_bottom"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#ffffff"
        android:paddingBottom="40dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:id="@+id/ll_artist_magicindicator"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:orientation="horizontal"
            tools:ignore="RtlSymmetry">

            <net.lucode.hackware.magicindicator.MagicIndicator
                android:id="@+id/magic_artist_indicator"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_gap"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_below="@id/ll_artist_magicindicator"
            android:background="#f0f0f0" />

        <android.support.v4.view.ViewPager
            android:id="@+id/view_pager_artist_detail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/tv_gap" />
    </RelativeLayout>

    <android.support.design.widget.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <android.support.design.widget.CollapsingToolbarLayout
            android:layout_width="match_parent"
            android:layout_height="290dp"
            android:fitsSystemWindows="true"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <ImageView
                android:id="@+id/iv_artist_detail_background"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_test"
                app:layout_collapseMode="parallax" />

            <ImageView
                android:id="@+id/iv_artist_detail_background_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                app:layout_collapseMode="parallax" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_gravity="bottom"
                android:layout_marginBottom="20dp"
                app:layout_collapseMode="parallax">

                <FrameLayout
                    android:id="@+id/fl_artist_detail_followed"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="20dp"
                    android:layout_marginTop="20dp"
                    android:background="@drawable/bg_user_detail_followed"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_followed" />
                </FrameLayout>

                <LinearLayout
                    android:id="@+id/ll_artist_detail_follow"
                    android:layout_width="70dp"
                    android:layout_height="30dp"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="20dp"
                    android:layout_marginTop="20dp"
                    android:background="@drawable/bg_round_red"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="13dp"
                        android:layout_height="13dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="5dp"
                        android:layout_marginRight="5dp"
                        android:src="@drawable/ic_add_white" />

                    <TextView
                        android:id="@+id/tv_item_search_user_follow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical|right"
                        android:layout_marginRight="5dp"
                        android:text="关注"
                        android:textColor="@color/white"
                        android:textSize="11sp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_artist_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:textColor="#f0f0f0f0"
                    android:textSize="17sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_artist_fans"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignLeft="@+id/tv_artist_name"
                    android:layout_below="@+id/tv_artist_name"
                    android:layout_marginTop="10dp"
                    android:text="粉丝 55万"
                    android:textColor="#f0f0f0"
                    android:textSize="13sp"
                    android:typeface="monospace"
                    android:visibility="gone" />
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="15dp"
                android:layout_marginTop="650dp"
                android:background="@drawable/bg_dailyrecommend"
                app:layout_collapseMode="pin" />

            <android.support.v7.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="55dp"
                app:layout_collapseMode="pin"
                app:contentInsetLeft="0dp"
                app:contentInsetStart="0dp"
                app:contentInsetEnd="0dp"
                app:maxButtonHeight="20dp"
                app:titleMargin="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/img_artist_detail_back"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginBottom="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginLeft="10dp"
                        android:src="@drawable/ic_left_arrow_white" />

                </LinearLayout>
            </android.support.v7.widget.Toolbar>
        </android.support.design.widget.CollapsingToolbarLayout>
    </android.support.design.widget.AppBarLayout>

</android.support.design.widget.CoordinatorLayout>