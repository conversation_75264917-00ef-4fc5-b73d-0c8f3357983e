<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_dialog_corner_normal"
    android:orientation="vertical"
    android:paddingLeft="20dp"
    android:paddingTop="20dp"
    android:paddingBottom="20dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="选择排序方式"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold" />

    <LinearLayout
        android:id="@+id/ll_dialog_sort_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:visibility="gone"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_sort_time_red" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:layout_weight="4"
            android:text="按添加时间"
            android:textColor="@color/black"
            android:textSize="15dp" />

        <ImageView
            android:id="@+id/iv_time_check"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="30dp"
            android:layout_weight="3"
            android:visibility="gone"
            android:src="@drawable/ic_check_red" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_dialog_sort_music"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:visibility="visible"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_sort_song_red" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:layout_weight="4"
            android:text="按单曲名"
            android:textColor="@color/black"
            android:textSize="15dp" />

        <ImageView
            android:id="@+id/iv_music_check"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="30dp"
            android:layout_weight="3"
            android:visibility="visible"
            android:src="@drawable/ic_check_red" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_dialog_sort_album"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:visibility="gone"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_sort_album_red" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:layout_weight="4"
            android:text="按专辑名"
            android:textColor="@color/black"
            android:textSize="15dp" />

        <ImageView
            android:id="@+id/iv_album_check"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="30dp"
            android:layout_weight="3"
            android:visibility="gone"
            android:src="@drawable/ic_check_red" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_dialog_sort_artist"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:visibility="gone"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_sort_artist_red" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:layout_weight="4"
            android:text="按歌手名"
            android:textColor="@color/black"
            android:textSize="15dp" />

        <ImageView
            android:id="@+id/iv_artist_check"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="30dp"
            android:layout_weight="3"
            android:visibility="gone"
            android:src="@drawable/ic_check_red" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_dialog_sort_folder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:visibility="gone"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_sort_song_red" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:layout_weight="4"
            android:text="按文件夹名"
            android:textColor="@color/black"
            android:textSize="15dp" />

        <ImageView
            android:id="@+id/iv_folder_check"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="30dp"
            android:layout_weight="3"
            android:visibility="gone"
            android:src="@drawable/ic_check_red" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_dialog_sort_number"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:visibility="gone"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_sort_number" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:layout_weight="4"
            android:text="按歌曲数量"
            android:textColor="@color/black"
            android:textSize="15dp" />

        <ImageView
            android:id="@+id/iv_number_check"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="30dp"
            android:layout_weight="3"
            android:visibility="gone"
            android:src="@drawable/ic_check_red" />

    </LinearLayout>
</LinearLayout>