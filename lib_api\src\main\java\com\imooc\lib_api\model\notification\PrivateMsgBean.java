package com.imooc.lib_api.model.notification;


import com.imooc.lib_api.model.user.UserEventBean;

import java.util.ArrayList;

public class PrivateMsgBean {

	/**
	 * {
	 *     "msgs": [
	 *         {
	 *             "fromUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "109951164794438049",
	 *                 "avatarImgIdStr": "109951164794423119",
	 *                 "avatarImgId": 109951164794423120,
	 *                 "backgroundImgId": 109951164794438050,
	 *                 "birthday": -*************,
	 *                 "gender": 0,
	 *                 "accountStatus": 0,
	 *                 "city": 1010000,
	 *                 "detailDescription": "云村云圈管理员 ",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Q8TuLFxXCjvUMps5_GvIvw==/109951164794438049.jpg",
	 *                 "description": "云村云圈管理员 ",
	 *                 "avatarUrl": "https://p2.music.126.net/PwCqhqsESsD83VUWfXS79A==/109951164794423119.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 10,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 1000000,
	 *                 "remarkName": null,
	 *                 "nickname": "云村云圈酱",
	 *                 "signature": "云圈酱使用指南：\n私信太多回复很慢\n最新消息打开动态\n圈主手册走进专栏\n都是成熟圈友，快点学会自我寻找答案\nskrrrr",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"Snigellin在「少年场」内发布了一条新动态，快来看看吧~\",\"pushMsg\":\"Snigellin刚刚在「少年场」内发布了一条新动态，快来看看吧>>\",\"resType\":23,\"type\":23,\"generalMsg\":{\"noticeMsg\":\"少年场\",\"inboxBriefContent\":\"Snigellin在「少年场」内发布了一条新动态，快来看看吧~\",\"webUrl\":\"http://music.163.com/st/circle/pages/index/index.html?circleId=010082FF5B7E9C84E3A1BC298E0DE66E3030\",\"nativeUrl\":\"orpheus://open?url1=orpheus%3A%2F%2Fnm%2Fcircle%2FmainPage%3FcircleId%3D010082FF5B7E9C84E3A1BC298E0DE66E3030&url2=http%3A%2F%2Fmusic.163.com%2Fst%2Fcircle%2Fpages%2Findex%2Findex.html%3FcircleId%3D010082FF5B7E9C84E3A1BC298E0DE66E3030\",\"cover\":\"http://p5.music.126.net/obj/w5nDkMOCwrDCmDDDi8Om/**********/3617/0655/1f0b/45d416e239e4dd4cabf43b635c460273.png\",\"title\":\"少年场\",\"tag\":\"云圈\",\"subTitle\":\"2221圈友已加入\",\"subTag\":\"\",\"channel\":0,\"subType\":1007,\"canPlay\":false,\"resName\":\"\"}}",
	 *             "newMsgCount": 2
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": ********,
	 *                 "backgroundImgIdStr": "109951164471001946",
	 *                 "avatarImgIdStr": "*****************",
	 *                 "avatarImgId": *****************,
	 *                 "backgroundImgId": 109951164471001950,
	 *                 "birthday": ************,
	 *                 "gender": 1,
	 *                 "accountStatus": 0,
	 *                 "city": 340100,
	 *                 "detailDescription": "说唱音乐人",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 10,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/XluC74VhIjABD5wIaP-IkQ==/109951164471001946.jpg",
	 *                 "description": "说唱音乐人",
	 *                 "avatarUrl": "https://p2.music.126.net/xv-zaq3a-6Ge31VXP4W01g==/*****************.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 4,
	 *                 "vipType": 11,
	 *                 "experts": null,
	 *                 "province": 340000,
	 *                 "remarkName": null,
	 *                 "nickname": "冷的少年",
	 *                 "signature": "",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": "chill boy"
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"我的最新专辑《《形影问答·春思》》发布了，快来抢先听！\",\"album\":{\"name\":\"《形影问答·春思》\",\"id\":********,\"type\":\"EP/Single\",\"size\":1,\"picId\":109951164922615348,\"blurPicUrl\":\"http://p1.music.126.net/SOFxhNo7neP8Skp8CFsJLg==/109951164922615348.jpg\",\"companyId\":0,\"pic\":109951164922615348,\"picUrl\":\"http://p1.music.126.net/SOFxhNo7neP8Skp8CFsJLg==/109951164922615348.jpg\",\"publishTime\":*************,\"description\":\"\",\"tags\":\"\",\"company\":null,\"briefDesc\":\"\",\"artist\":{\"name\":\"chill boy\",\"id\":571797,\"picId\":109951163764686677,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/oAQKRnWB3wvanu1w91tM0Q==/109951163764686677.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/****************.jpg\",\"albumSize\":22,\"alias\":[\"chill boy\"],\"trans\":\"\",\"musicSize\":69,\"topicPerson\":0,\"picId_str\":\"109951163764686677\"},\"songs\":[],\"alias\":[],\"status\":0,\"copyrightId\":0,\"commentThreadId\":\"R_AL_3_********\",\"artists\":[{\"name\":\"chill boy\",\"id\":571797,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/****************.jpg\",\"img1v1Url\":\"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/****************.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0,\"topicPerson\":0}],\"subType\":\"录音室版\",\"transName\":null,\"picId_str\":\"109951164922615348\"},\"type\":2,\"title\":\"分享专辑\"}",
	 *             "newMsgCount": 1
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": 2082221,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "109951163879473083",
	 *                 "avatarImgId": 109951163879473090,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": *************,
	 *                 "gender": 0,
	 *                 "accountStatus": 0,
	 *                 "city": 110101,
	 *                 "detailDescription": "云音乐 官方活动账号",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 10,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/bmA_ablsXpq3Tk9HlEg9sA==/****************.jpg",
	 *                 "description": "云音乐 官方活动账号",
	 *                 "avatarUrl": "https://p2.music.126.net/WvyEVEeoqnwGvRmk3Iq3cA==/109951163879473083.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 10,
	 *                 "vipType": 11,
	 *                 "experts": null,
	 *                 "province": 110000,
	 *                 "remarkName": null,
	 *                 "nickname": "云音乐福利",
	 *                 "signature": "网易云音乐是8亿人都在使用的音乐平台，致力于帮助用户发现音乐惊喜，帮助音乐人实现梦想。客服@云音乐客服 在线时间：9：00 - 24：00，如您在使用过程中遇到任何问题，欢迎私信咨询，我们会尽快回复。如果仍然不能解决您的问题，与活动相关的疑问请私信@云音乐福利",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"promotionUrl\":{\"id\":0,\"cover\":109951164914767308,\"title\":\"宅家看快本，云村领福利\",\"text\":\"有奖\",\"url\":\"https://music.163.com/m/at/happycamp\",\"addTime\":0,\"bizChannel\":null,\"coverStr\":\"109951164914767308\",\"coverUrl\":\"http://p2.music.126.net/lROmwcWitGXGwLFHd__i5g==/109951164914767308.jpg\"},\"msg\":\"《快乐大本营》首届迷惑行为大赏限时营业，朱正廷、吴宣仪、周震南、夏之光迷惑日常大起底！回顾分享节目精彩内容，呼朋唤友来抽奖，尽在网易云音乐。所有用户活动期间内共有1次抽奖机会，每天【分享】活动页面到其他社交平台，即可额外增加2次抽奖机会！奖品有限，先到先得↓↓↓\",\"type\":12,\"title\":\"宅家看快本，云村领福利\"}",
	 *             "newMsgCount": 3
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "109951164342737124",
	 *                 "avatarImgIdStr": "109951164595234162",
	 *                 "avatarImgId": 109951164595234160,
	 *                 "backgroundImgId": 109951164342737120,
	 *                 "birthday": ************,
	 *                 "gender": 1,
	 *                 "accountStatus": 0,
	 *                 "city": 110101,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 10,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/dWnVA7rIXanvZav0i7DDWA==/109951164342737124.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/LJxXT8e1xV8t1aTKNP3O8Q==/109951164595234162.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 4,
	 *                 "vipType": 11,
	 *                 "experts": null,
	 *                 "province": 110000,
	 *                 "remarkName": null,
	 *                 "nickname": "隔壁老樊c",
	 *                 "signature": "工作邮箱：<EMAIL>",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": "隔壁老樊"
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"我的最新专辑《你我不一》发布了, 快来抢先听！\",\"album\":{\"name\":\"你我不一\",\"id\":********,\"type\":\"EP/Single\",\"size\":1,\"picId\":109951164910151138,\"blurPicUrl\":\"http://p2.music.126.net/3paCNaN6ZbnJO2jQBQcSzQ==/109951164910151138.jpg\",\"companyId\":0,\"pic\":109951164910151138,\"picUrl\":\"http://p2.music.126.net/3paCNaN6ZbnJO2jQBQcSzQ==/109951164910151138.jpg\",\"publishTime\":*************,\"description\":\"\",\"tags\":\"\",\"company\":\"\",\"briefDesc\":\"\",\"artist\":{\"name\":\"隔壁老樊\",\"id\":********,\"picId\":109951164232057952,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p2.music.126.net/uTwOm8AEFFX_BYHvfvFcmQ==/109951164232057952.jpg\",\"img1v1Url\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/****************.jpg\",\"albumSize\":18,\"alias\":[],\"trans\":\"\",\"musicSize\":59,\"topicPerson\":0,\"picId_str\":\"109951164232057952\"},\"songs\":[],\"alias\":[\"我是唱作人2第1期\"],\"status\":0,\"copyrightId\":-1,\"commentThreadId\":\"R_AL_3_********\",\"artists\":[{\"name\":\"隔壁老樊\",\"id\":********,\"picId\":0,\"img1v1Id\":0,\"briefDesc\":\"\",\"picUrl\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/****************.jpg\",\"img1v1Url\":\"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/****************.jpg\",\"albumSize\":0,\"alias\":[],\"trans\":\"\",\"musicSize\":0,\"topicPerson\":0}],\"subType\":\"现场版\",\"transName\":null,\"picId_str\":\"109951164910151138\"},\"newPub\":true,\"type\":2}",
	 *             "newMsgCount": 0
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": **********,
	 *                 "backgroundImgIdStr": "109951162868128395",
	 *                 "avatarImgIdStr": "109951164890817781",
	 *                 "avatarImgId": 109951164890817780,
	 *                 "backgroundImgId": 109951162868128400,
	 *                 "birthday": -*************,
	 *                 "gender": 0,
	 *                 "accountStatus": 0,
	 *                 "city": 330100,
	 *                 "detailDescription": "云贝小助手官方账号",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/2zSNIqTcpHL2jIvU6hG0EA==/109951162868128395.jpg",
	 *                 "description": "云贝小助手官方账号",
	 *                 "avatarUrl": "https://p2.music.126.net/NOXO9ymTFXk4M8y7tSvdVQ==/109951164890817781.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 10,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 330000,
	 *                 "remarkName": null,
	 *                 "nickname": "云贝小助手",
	 *                 "signature": "",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"今日有20云贝待领取，请至云贝中心领取。\",\"pushMsg\":\"你收到了云贝小助手的私信:\\r\\n今日有20云贝待领取，请至云贝中心领取。\",\"resType\":23,\"type\":23,\"generalMsg\":{\"noticeMsg\":\"前往领取云贝\",\"inboxBriefContent\":\"今日有20云贝待领取，请至云贝中心领取。\",\"webUrl\":\"https://music.163.com/#/topic?id=********\",\"nativeUrl\":\"orpheus://open?url1=orpheus%3A%2F%2Frnpage%3Fcomponent%3Drn-cloudshell-center&url2=https%3A%2F%2Fmusic.163.com%2F%23%2Ftopic%3Fid%3D********\",\"cover\":\"http://p5.music.126.net/obj/w5rChcOIw6rDiDzDicOj/**********/19fa/52d6/4234/88e4a416a170d473a875e9449d46df7b.png\",\"title\":\"前往领取云贝\",\"tag\":\"\",\"subTitle\":\"\",\"subTag\":\"\",\"channel\":0,\"subType\":0,\"canPlay\":false,\"resName\":\"\"}}",
	 *             "newMsgCount": 0
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "109951164894907422",
	 *                 "avatarImgIdStr": "109951164910727441",
	 *                 "avatarImgId": 109951164910727440,
	 *                 "backgroundImgId": 109951164894907420,
	 *                 "birthday": -*************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 310101,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/L7LOh5T7avXVafN2dIqb3g==/109951164894907422.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/_e8uX_wrgu0gmO1i02HkGA==/109951164910727441.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 11,
	 *                 "experts": null,
	 *                 "province": 310000,
	 *                 "remarkName": null,
	 *                 "nickname": "梦游尘埃",
	 *                 "signature": "",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"认识啊\",\"type\":6,\"title\":\"\"}",
	 *             "newMsgCount": 0
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "109951164004163434",
	 *                 "avatarImgIdStr": "109951164880408408",
	 *                 "avatarImgId": 109951164880408420,
	 *                 "backgroundImgId": 109951164004163440,
	 *                 "birthday": *************,
	 *                 "gender": 1,
	 *                 "accountStatus": 0,
	 *                 "city": 532500,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 10,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/7awFDZKHk9RAAvoevw4BaA==/109951164004163434.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/3fSR5cq-LoTUSWy4YqyRLw==/109951164880408408.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 4,
	 *                 "vipType": 11,
	 *                 "experts": null,
	 *                 "province": 530000,
	 *                 "remarkName": null,
	 *                 "nickname": "中二少年SLIN菌",
	 *                 "signature": "Verse鸽子 很菜\r热爱音乐，热爱美好的一切。",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": "Snigellin"
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"欢迎加入「少年场」，我会经常在云圈内跟大家沟通交流，你可以通过「云村-关注-我的云圈」进入我的云圈，期待你的参与哦~\",\"pushMsg\":\"你收到了中二少年SLIN菌的私信:\\r\\n欢迎加入「少年场」，我会经常在云圈内跟大家沟通交流，你可以通过「云村-关注-我的云圈」进入我的云圈，期待你的参与哦~\",\"resType\":23,\"type\":23,\"generalMsg\":{\"noticeMsg\":\"Snigellin粉丝圈\",\"inboxBriefContent\":\"欢迎加入「少年场」，我会经常在云圈内跟大家沟通交流，你可以通过「云村-关注-我的云圈」进入我的云圈，期待你的参与哦~\",\"webUrl\":\"http://music.163.com/st/circle/pages/index/index.html?circleId=010082FF5B7E9C84E3A1BC298E0DE66E3030\",\"nativeUrl\":\"orpheus://open?url1=orpheus%3A%2F%2Fnm%2Fcircle%2FmainPage%3FcircleId%3D010082FF5B7E9C84E3A1BC298E0DE66E3030&url2=http%3A%2F%2Fmusic.163.com%2Fst%2Fcircle%2Fpages%2Findex%2Findex.html%3FcircleId%3D010082FF5B7E9C84E3A1BC298E0DE66E3030\",\"cover\":\"http://p5.music.126.net/obj/w5nDkMOCwrDCmDDDi8Om/**********/3617/0655/1f0b/45d416e239e4dd4cabf43b635c460273.png\",\"title\":\"Snigellin粉丝圈\",\"tag\":\"云圈\",\"subTitle\":\"838圈友已加入\",\"subTag\":\"\",\"channel\":0,\"subType\":0,\"canPlay\":false,\"resName\":\"\"}}",
	 *             "newMsgCount": 1
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": **********,
	 *                 "backgroundImgIdStr": "109951162868126486",
	 *                 "avatarImgIdStr": "109951164768890817",
	 *                 "avatarImgId": 109951164768890820,
	 *                 "backgroundImgId": 109951162868126480,
	 *                 "birthday": *************,
	 *                 "gender": 0,
	 *                 "accountStatus": 0,
	 *                 "city": 110101,
	 *                 "detailDescription": "云村夜话官方账号",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 10,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg",
	 *                 "description": "云村夜话官方账号",
	 *                 "avatarUrl": "https://p2.music.126.net/sE1PrioS2tefddgjer6vVg==/109951164768890817.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 10,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 110000,
	 *                 "remarkName": null,
	 *                 "nickname": "云村夜话",
	 *                 "signature": "点击关注 @云村夜话\n每周三、周五、周日晚22:00-23:00\n情感主播，在线连麦\n我们等你\n不见不散",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"promotionUrl\":{\"id\":0,\"cover\":109951164842110306,\"title\":\"今晚，等你的连麦\",\"text\":\"云村夜话\",\"url\":\"https://music.163.com/m/at/5e78cac81f84b18bee81cc2d\",\"addTime\":0,\"bizChannel\":null,\"coverUrl\":\"http://p1.music.126.net/ewiX56MjkFHcgcvBeuChvQ==/109951164842110306.jpg\",\"coverStr\":\"109951164842110306\"},\"msg\":\"晚间值班模式ONONON！今晚22:00，云村独家情感直播栏目@云村夜话准时开播，主播@解忧大白在此化身你的专属减压师，解决你在生活中的种种烦恼，夜晚太漫长，让我们的治愈声音陪伴你入睡吧~\",\"type\":12,\"title\":\"今晚，等你的连麦\"}",
	 *             "newMsgCount": 1
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": **********,
	 *                 "backgroundImgIdStr": "109951162868126486",
	 *                 "avatarImgIdStr": "109951163382070805",
	 *                 "avatarImgId": 109951163382070800,
	 *                 "backgroundImgId": 109951162868126480,
	 *                 "birthday": -*************,
	 *                 "gender": 0,
	 *                 "accountStatus": 0,
	 *                 "city": 330100,
	 *                 "detailDescription": "云音乐吉祥物",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg",
	 *                 "description": "云音乐吉祥物",
	 *                 "avatarUrl": "https://p2.music.126.net/QcgQ0sEqI2oh4fEhIGPpcg==/109951163382070805.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 10,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 330000,
	 *                 "remarkName": null,
	 *                 "nickname": "云音乐多多和西西",
	 *                 "signature": "",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"Hi ，我是西西，热爱摇滚的宇宙驯鹿，从今天起，我们将在这里一直陪伴着你\",\"realFromUserId\":*********,\"type\":6,\"title\":\"\"}",
	 *             "newMsgCount": 2
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": **********,
	 *                 "backgroundImgIdStr": "109951163251528198",
	 *                 "avatarImgIdStr": "109951164023729614",
	 *                 "avatarImgId": 109951164023729620,
	 *                 "backgroundImgId": 109951163251528200,
	 *                 "birthday": -*************,
	 *                 "gender": 0,
	 *                 "accountStatus": 0,
	 *                 "city": 330100,
	 *                 "detailDescription": "云村社区管理员",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Pj3d9IJqfJQ5WQvyuzvFbw==/109951163251528198.jpg",
	 *                 "description": "云村社区管理员",
	 *                 "avatarUrl": "https://p2.music.126.net/5gxmoh5YtJC8n59QNRdMYA==/109951164023729614.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 10,
	 *                 "vipType": 11,
	 *                 "experts": null,
	 *                 "province": 330000,
	 *                 "remarkName": null,
	 *                 "nickname": "云村大喇叭",
	 *                 "signature": "致力于云村社区建设和活动通知！由于系统消息多大家的私信无法及时查看和回复，反馈建议请不要直接和我发私信喔～！ \n\n客服@云音乐客服 在线时间：9：00 - 24：00，如您在使用过程中遇到任何问题，欢迎站内私信咨询@云音乐客服，我们会尽快回复。\n\n也可在社区指南专栏文末的反馈通道里填写，即：\n https://survey.163.com/htmls/yn7oub/paper.html，感谢各位的支持和帮助！",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"根据华晨宇歌曲《春》制作的爱的微型纪录片，\\n献给这个特别的春天，原来爱可以平凡而伟大。\\n\\n和时光一起记录在特殊日子的点滴温暖吧↓↓↓\",\"type\":23,\"resType\":23,\"pushMsg\":\"\",\"generalMsg\":{\"cover\":\"https://p1.music.126.net/c6R0Ef0iVyoxE1VUjy9hFg==/109951164742575760.jpg\",\"nativeUrl0\":\"orpheus://nm/MLog/videoDetail?id=a10e3vp2khQ7U1y&toSquare=true\",\"nativeUrl1\":\"\",\"webUrl\":\"\",\"noticeMsg\":\"\",\"inboxBriefContent\":\"春天来了，华晨宇在花下等你！\",\"pushMsg\":\"\",\"tag\":\"Mlog\",\"subTag\":\"\",\"title\":\"《关于爱的100种春天》\",\"subTitle\":\"与华晨宇一起，记录春日里的爱\",\"msg\":\"根据华晨宇歌曲《春》制作的爱的微型纪录片，\\n献给这个特别的春天，原来爱可以平凡而伟大。\\n\\n和时光一起记录在特殊日子的点滴温暖吧↓↓↓\",\"id\":-1,\"nativeUrl\":\"orpheus://open?url1=orpheus%3A%2F%2Fnm%2FMLog%2FvideoDetail%3Fid%3Da10e3vp2khQ7U1y%26toSquare%3Dtrue&url2=\"}}",
	 *             "newMsgCount": 1
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": 201586,
	 *                 "backgroundImgIdStr": "109951163521987363",
	 *                 "avatarImgIdStr": "109951164872418659",
	 *                 "avatarImgId": 109951164872418660,
	 *                 "backgroundImgId": 109951163521987360,
	 *                 "birthday": *************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 440100,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 10,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/6WOd8M2uPlfuu9TCiBCSMw==/109951163521987363.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/gWqtiBXp1knrGQbTkaJdRA==/109951164872418659.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 4,
	 *                 "vipType": 11,
	 *                 "experts": null,
	 *                 "province": 440000,
	 *                 "remarkName": null,
	 *                 "nickname": "原创君",
	 *                 "signature": "网易云音乐是8亿人都在使用的音乐平台，致力于帮助音乐爱好者发现音乐惊喜，帮助音乐人实现梦想。\n如您在使用过程中遇到任何问题，欢迎私信咨询，我们会尽快回复。\n如果仍然不能解决您的问题，请邮件我们：\n用户：<EMAIL>\n音乐人：<EMAIL>",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": "网易音乐人"
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"promotionUrl\":{\"id\":0,\"cover\":109951164731300731,\"title\":\"「播放」\",\"text\":\"通知\",\"url\":\"https://music.163.com/m/at/5e4367286de586caaa2b4f21\",\"addTime\":0,\"bizChannel\":null,\"coverUrl\":\"http://p1.music.126.net/8jW8RUq0ykEbIpJoPDzcCA==/109951164731300731.jpg\",\"coverStr\":\"109951164731300731\"},\"msg\":\"【亲眼听到《想见你》OST！】今晚，《想见你》OST 演唱者空降直播间！\\n按下下方「播放」键，随着音乐的步伐，将美丽的剧集回忆慢慢重来！\",\"type\":12,\"title\":\"「播放」\"}",
	 *             "newMsgCount": 1
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "109951163114350390",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": 109951163114350380,
	 *                 "birthday": ************,
	 *                 "gender": 1,
	 *                 "accountStatus": 0,
	 *                 "city": 450100,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/QsCL3mKXKxYmSv_LLmhqTg==/109951163114350390.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/-hK85ZJCV5-jM5FDbLHFMg==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 450000,
	 *                 "remarkName": null,
	 *                 "nickname": "张张________",
	 *                 "signature": "null",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"\",\"djRadio\":{\"id\":*********,\"dj\":{\"defaultAvatar\":false,\"province\":320000,\"authStatus\":0,\"followed\":false,\"avatarUrl\":\"http://p1.music.126.net/ibb8YiVuElUreqg5sur7Yg==/*****************.jpg\",\"accountStatus\":0,\"gender\":1,\"city\":320100,\"birthday\":************,\"userId\":********,\"userType\":0,\"nickname\":\"苏妙慈\",\"signature\":\"苏妙慈\",\"description\":\"\",\"detailDescription\":\"\",\"avatarImgId\":*****************,\"backgroundImgId\":109951163324415661,\"backgroundUrl\":\"http://p1.music.126.net/2wuwwnfRcOMxcGF3HHKFqg==/109951163324415661.jpg\",\"authority\":0,\"mutual\":false,\"expertTags\":null,\"experts\":null,\"djStatus\":10,\"vipType\":0,\"remarkName\":null,\"avatarImgIdStr\":\"*****************\",\"backgroundImgIdStr\":\"109951163324415661\",\"avatarImgId_str\":\"*****************\"},\"name\":\"郭德纲于谦高清相声(持续更新)\",\"picUrl\":\"http://p2.music.126.net/m00AiSZ0tmHSPNAAIZ6avw==/109951164263223904.jpg\",\"desc\":\"全网最全最高清\",\"subCount\":345928,\"programCount\":269,\"createTime\":*************,\"categoryId\":8,\"category\":\"相声曲艺\",\"radioFeeType\":0,\"feeScope\":0,\"buyed\":false,\"videos\":null,\"finished\":false,\"underShelf\":false,\"purchaseCount\":0,\"price\":0,\"originalPrice\":0,\"discountPrice\":null,\"lastProgramCreateTime\":1576058836896,\"lastProgramName\":null,\"lastProgramId\":2064536957,\"picId\":109951164263223904,\"rcmdText\":null,\"composeVideo\":false,\"shareCount\":1476,\"subed\":true,\"likedCount\":0,\"commentCount\":0},\"type\":13,\"title\":\"分享有声电台\"}",
	 *             "newMsgCount": 0
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": **********,
	 *                 "backgroundImgIdStr": "109951163979962548",
	 *                 "avatarImgIdStr": "109951164501547387",
	 *                 "avatarImgId": 109951164501547400,
	 *                 "backgroundImgId": 109951163979962540,
	 *                 "birthday": ************,
	 *                 "gender": 1,
	 *                 "accountStatus": 0,
	 *                 "city": 371400,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 10,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/zGsKHsn8-h0sjo6O48GILw==/109951163979962548.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/2O40gObreTFT9Q103MtPSQ==/109951164501547387.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 4,
	 *                 "vipType": 11,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "柏松M",
	 *                 "signature": "工作邮箱：<EMAIL> ***********",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": "柏松"
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"promotionUrl\":{\"id\":0,\"cover\":109951164460950130,\"title\":\"愿世间的美好，与你环环相扣\",\"text\":\"直播\",\"url\":\"https://h5.iplay.163.com/st/column/home.html?id=50001\",\"addTime\":0,\"coverStr\":\"109951164460950130\",\"coverUrl\":\"http://p1.music.126.net/jPzfJ6AG2bI3QHpqjSreIw==/109951164460950130.jpg\"},\"msg\":\"晚上好，我是柏松，谢谢你喜欢我的歌。11月2日（明晚）20：00，我将携歌曲《世间美好与你环环相扣》做客云村独家直播品牌#音乐人来了# ，想把这世间的美好通通唱给你听~还有机会获得我签名的云音乐笔记本。明晚八点，我们不见不散~\",\"type\":12,\"title\":\"愿世间的美好，与你环环相扣\"}",
	 *             "newMsgCount": 0
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": **********,
	 *                 "backgroundImgIdStr": "109951164483145882",
	 *                 "avatarImgIdStr": "109951164879572466",
	 *                 "avatarImgId": 109951164879572460,
	 *                 "backgroundImgId": 109951164483145890,
	 *                 "birthday": ************,
	 *                 "gender": 0,
	 *                 "accountStatus": 0,
	 *                 "city": 330100,
	 *                 "detailDescription": "网易云音乐VIP官方账号",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/XU7dACKWXNiiudHl073oow==/109951164483145882.jpg",
	 *                 "description": "网易云音乐VIP官方账号",
	 *                 "avatarUrl": "https://p2.music.126.net/ZVQwrUkMExU9Npzn22ak0A==/109951164879572466.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 3,
	 *                 "vipType": 11,
	 *                 "experts": null,
	 *                 "province": 330000,
	 *                 "remarkName": null,
	 *                 "nickname": "云音乐VIP",
	 *                 "signature": "网易云音乐是6亿人都在使用的音乐平台，致力于帮助音乐爱好者发现音乐惊喜，帮助音乐人实现梦想。本号为画风不太正经但很走心且一听歌就会摇摆的网易云音乐VIP正经官号。如果仍然不能解决您的问题，请邮件我们：用户：***************音乐人：<EMAIL>",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"promotionUrl\":{\"id\":0,\"cover\":109951164359810362,\"title\":\"中秋限时特惠\",\"text\":\"限时买赠\",\"url\":\"https://music.163.com/m/at/supervipday20190913\",\"addTime\":0,\"coverUrl\":\"http://p1.music.126.net/JWkzoWrLCv5KM798qzRocQ==/109951164359810362.jpg\",\"coverStr\":\"109951164359810362\"},\"msg\":\"买就送黑胶VIP！还给大家准备了耳机、大闸蟹、法国红酒等中秋礼物~最后，希望大家开心幸福，中秋节快乐呀！\",\"type\":12,\"title\":\"中秋限时特惠\"}",
	 *             "newMsgCount": 0
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": 9003,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "109951163879490620",
	 *                 "avatarImgId": 109951163879490620,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 0,
	 *                 "accountStatus": 0,
	 *                 "city": 110101,
	 *                 "detailDescription": "云音乐小甜心",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/bKMPa2ExjGaNPXRjCISOZg==/****************.jpg",
	 *                 "description": "云音乐小甜心",
	 *                 "avatarUrl": "https://p2.music.126.net/_aEPXmHuskM-g140GROZnQ==/109951163879490620.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 10,
	 *                 "vipType": 11,
	 *                 "experts": null,
	 *                 "province": 110000,
	 *                 "remarkName": null,
	 *                 "nickname": "云音乐小秘书",
	 *                 "signature": "网易云音乐是8亿人都在使用的音乐平台，致力于帮助音乐爱好者发现音乐惊喜，帮助音乐人实现梦想。 2019年8月31日起，将不再提供实时在线人工服务。您可以优先通过自助方式解决问题，如仍需求助，可在相关页面留下您的问题，后续会有人工为您解答，辛苦您耐心等待，给您带来的不便敬请谅解。如果仍然不能解决您的问题，可以邮件我们：用户：<EMAIL>  音乐人：<EMAIL>",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"您已成功开通黑胶VIP自动续费，并获得考拉红卡会员。我们将持续为您提供优质会员服务，并在到期前为您自动续费。快领取你的福利吧，活动期间每人限领1次\",\"resType\":23,\"type\":23,\"generalMsg\":{\"noticeMsg\":\"点击领取网易考拉会员\",\"inboxBriefContent\":\"您已成功开通黑胶VIP自动续费，并获得考拉红卡会员。我们将持续为您提供优质会员服务，并在到期前为您自动续费。快领取你的福利吧，活动期间每人限领1次\",\"webUrl\":\"http://da.kaola.com/redirect?t=5b1aef94b8392c00&p=8e9bc8ac&proId=1024&code=0d44887b7b984aebf3517e599f26e4f3&target=https%3A%2F%2Fweex.kaola.com%2Factivity%2Fpages%2Fyyyhj2.html%3Fnoklappbanner%3D2%26tag%3D39c18428bdd72630e076b136f47c6b31\",\"nativeUrl\":\"orpheus://open?url1=&url2=http%3A%2F%2Fda.kaola.com%2Fredirect%3Ft%3D5b1aef94b8392c00%26p%3D8e9bc8ac%26proId%3D1024%26code%3D0d44887b7b984aebf3517e599f26e4f3%26target%3Dhttps%253A%252F%252Fweex.kaola.com%252Factivity%252Fpages%252Fyyyhj2.html%253Fnoklappbanner%253D2%2526tag%253D39c18428bdd72630e076b136f47c6b31\",\"cover\":\"https://p1.music.126.net/iDwntvst2VMwP-AwT8rEcg==/109951164092535915.png\",\"title\":\"点击领取网易考拉会员\",\"tag\":\"\",\"subTitle\":\"\",\"subTag\":\"\",\"channel\":0,\"subType\":0,\"canPlay\":false}}",
	 *             "newMsgCount": 0
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "109951164611663122",
	 *                 "avatarImgIdStr": "109951164*********",
	 *                 "avatarImgId": 109951164611660270,
	 *                 "backgroundImgId": 109951164611663120,
	 *                 "birthday": ************,
	 *                 "gender": 1,
	 *                 "accountStatus": 0,
	 *                 "city": 210200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/rBmigM2UtRAsL3BaAeOXpA==/109951164611663122.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/GlRyEPaCRUJhBuBikAXBDg==/109951164*********.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 210000,
	 *                 "remarkName": null,
	 *                 "nickname": "笙笙不息xi",
	 *                 "signature": "可是后来我们都离开。",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"你加qq1948209459吧网易里发不了歌曲\",\"type\":6,\"title\":\"\"}",
	 *             "newMsgCount": 0
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": **********,
	 *                 "backgroundImgIdStr": "109951163084619052",
	 *                 "avatarImgIdStr": "109951164878222303",
	 *                 "avatarImgId": 109951164878222300,
	 *                 "backgroundImgId": 109951163084619060,
	 *                 "birthday": *************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 110101,
	 *                 "detailDescription": "云音乐视频官方管理员",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 10,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/N8YPMHxG8matEK2xsOIaaQ==/109951163084619052.jpg",
	 *                 "description": "云音乐视频官方管理员",
	 *                 "avatarUrl": "https://p2.music.126.net/_yYTxkU9j6KTg0iSFiR1Uw==/109951164878222303.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 10,
	 *                 "vipType": 11,
	 *                 "experts": null,
	 *                 "province": 110000,
	 *                 "remarkName": null,
	 *                 "nickname": "云音乐视频酱",
	 *                 "signature": "网易云音乐是8亿人都在使用的音乐平台，致力于帮助音乐爱好者发现音乐惊喜，帮助音乐人实现梦想。\n@云音乐视频酱 在线时间：10：00 - 19：00，@云音乐客服 在线时间：9：00 - 24：00，如您在使用过程中遇到任何问题，欢迎私信咨询，我们会尽快回复。如果仍然不能解决您的问题，请邮件我们：\n用户：<EMAIL>\n音乐人：<EMAIL>",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"在街头听到这样的歌声，你会为她驻足吗？\",\"video\":{\"vid\":0,\"videoId\":0,\"creator\":**********,\"coverUrl\":\"http://p2.music.126.net/9zSMciFsRQFJ5WfPwg4R4g==/109951163610182696.jpg\",\"title\":\"烟嗓《往后余生》风雪是你，平淡是你。\",\"description\":\"你还是以前的你 初见时惊人\",\"durationms\":162112,\"resolutions\":[{\"width\":360,\"height\":640,\"size\":********,\"resolutionType\":1},{\"width\":540,\"height\":960,\"size\":********,\"resolutionType\":2},{\"width\":720,\"height\":1280,\"size\":********,\"resolutionType\":3},{\"width\":1080,\"height\":1920,\"size\":********,\"resolutionType\":4}],\"threadId\":\"\",\"playTime\":561871,\"likeCount\":3622,\"commentCount\":270,\"shareCount\":1227,\"state\":1,\"status\":0,\"creatorName\":\"歌手Ada\",\"markTypes\":null},\"type\":24,\"title\":\"分享视频\"}",
	 *             "newMsgCount": 0
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": **********,
	 *                 "backgroundImgIdStr": "109951163972163902",
	 *                 "avatarImgIdStr": "109951163339602331",
	 *                 "avatarImgId": 109951163339602340,
	 *                 "backgroundImgId": 109951163972163900,
	 *                 "birthday": -*************,
	 *                 "gender": 1,
	 *                 "accountStatus": 0,
	 *                 "city": 310101,
	 *                 "detailDescription": "网易电音品牌",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 10,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/2WS660DQ-wFPN03OEtsuqQ==/109951163972163902.jpg",
	 *                 "description": "网易电音品牌",
	 *                 "avatarUrl": "https://p2.music.126.net/UgpnSzetnErHoCDz3b1jkA==/109951163339602331.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": [
	 *                     "电子"
	 *                 ],
	 *                 "userType": 3,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 310000,
	 *                 "remarkName": null,
	 *                 "nickname": "网易电音品牌放刺FEVER",
	 *                 "signature": "网易电音品牌，诞生于2018年，基于上海、专注中国，致力于为中国电音乐迷创造优质的电音体验，感官全开。",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"热爱电音的你，是否也想变身DJ？DJ&音乐制作人也能拿到学士、硕士学位？官宣！网易放刺电音制作学院（Point Blank China）落地杭州！成为科班出身电音制作人不是梦！在国际师资的倾力打造下，网易放刺FEVER帮你实现电音梦。点击直达了解→\",\"topic\":{\"topic\":{\"id\":********,\"addTime\":*************,\"mainTitle\":\"中国最权威电音学院落地杭州啦！\",\"title\":\"中国最权威电音学院落地杭州啦！\",\"content\":[{\"type\":13,\"id\":0,\"content\":\"<p>热爱电音的你，是否也想变身DJ？DJ&amp;音乐制作人也能拿到学士、硕士学位？</p>\"},{\"type\":13,\"id\":0,\"content\":\"<p>号外号外！网易放刺电音制作学院（Point Blank China）落地杭州！</p>\"},{\"type\":13,\"id\":0,\"content\":\"<p>成为科班出身电音制作人不是梦！在国际师资的倾力打造下，网易放刺FEVER帮你实现电音梦。</p>\"},{\"type\":13,\"id\":0,\"content\":\"<p><b>下方直达了解↓↓↓</b></p>\"},{\"type\":13,\"id\":0,\"content\":\"<p>网易放刺FEVER独家联合全球最大电音学院Point Blank，推出中国最权威的电音学院——网易放刺电音制作学院（Point Blank China），@网易UFO丁磊 将出任名誉校长。</p>\"},{\"type\":13,\"id\":0,\"content\":\"<p>学院落地杭州国际博览中心，拥有6000平米空间及19门专业课程设计。未来，网易放刺电音制作学院（Point Blank China）将成为中国唯一一家颁发学士学位和硕士学位的电音学院。</p>\"},{\"type\":13,\"id\":0,\"content\":\"<p>明年1月，学院将开设冬令营，把中国的电音爱好者带往伦敦Point Blank总部，和最好的DJ一起学习音乐制作。同期，学院还将上线全球顶尖电音游学项目，包括美国洛杉矶、西班牙伊比萨，印度孟买等多家分校，亲临全球朝圣的夜店，享受与业界大师、顶级DJ和行业顶尖制作人一起，边打碟边游学的独家机会。</p>\"},{\"type\":13,\"id\":0,\"content\":\"<p>此外，明年初，学院还将发布首期对外招生，主要开设DJ Course和电子音乐制作两大类课程，12岁以上的电音从业人士及电音爱好者均可报名。学院初期也将开启音乐事业、流行歌曲唱法及编曲课程等。</p>\"},{\"type\":7,\"id\":0,\"content\":\"Point Blank有多牛？\"},{\"type\":13,\"id\":0,\"content\":\"<p>1994年，Point Blank在伦敦创立，曾被《DJ Mag》杂志的读者评选为全球最佳音乐制作和DJ学校，2018年，Point Blank更因丰富的教学经验、高端专业设施和优质的职业发展机会而，获选英国学生投票产生的WhatUni Award“最佳独立大学”。Point Blank在洛杉矶，伊维萨岛和孟买等地均设有分校，为乐坛输送了众多人才。</p>\"},{\"type\":13,\"id\":0,\"content\":\"<p>如DJ界的传奇人物DJ Pete Tong，英国流行女歌手Leona Lewis等都是出自这家学院的荣誉学员。如今，Point Blank与网易放刺FEVER合作后，也将首度走进中国。</p>\"},{\"type\":14,\"id\":0,\"content\":\"网易放刺电音制作学院（Point Blank China）特色揭秘\"},{\"type\":13,\"id\":0,\"content\":\"<ul>\\n <li>办学实力强悍，引进海外顶级师资和专业音乐设备</li>\\n <li>教学内容多样，覆盖DJ课程、各类音乐制作和艺人培训等19门课程</li>\\n <li><span style=\\\"background-color: initial;\\\">服务受众多元，包括学生、电音爱好者、从业人员、儿童等</span></li>\\n <li>教育体系完整，颁发本科学士、硕士学位及其他专业技能证书</li>\\n <li>完善人才体系，培养、发掘优质学员，孵化电音专业人才</li>\\n</ul>\"},{\"type\":13,\"id\":0,\"content\":\"<p>最后，奉上网易放刺电音制作学院（Point Blank China）高清大图，一起来看吧！</p>\"},{\"type\":2,\"id\":109951163607264454,\"content\":\"{\\\"url\\\":\\\"http://p1.music.126.net/Vq1Kfu9iikjGb630sZGJVA==/109951163607264454.jpg\\\",\\\"size\\\":\\\"1267x713\\\"}\"},{\"type\":2,\"id\":109951163607271260,\"content\":\"{\\\"url\\\":\\\"http://p1.music.126.net/4-p1LHbRl1mNFAK1i5v-Xw==/109951163607271260.jpg\\\",\\\"size\\\":\\\"1269x944\\\"}\"},{\"type\":2,\"id\":109951163607265903,\"content\":\"{\\\"url\\\":\\\"http://p1.music.126.net/YU027dD5GL5kDd6p6Tg5eA==/109951163607265903.jpg\\\",\\\"size\\\":\\\"1271x948\\\"}\"},{\"type\":2,\"id\":109951163607274618,\"content\":\"{\\\"url\\\":\\\"http://p1.music.126.net/QkpAJ3Zz5w7wK0HhqIDMaQ==/109951163607274618.jpg\\\",\\\"size\\\":\\\"1269x946\\\"}\"},{\"type\":2,\"id\":109951163607265397,\"content\":\"{\\\"url\\\":\\\"http://p1.music.126.net/5LeK9ekAthEs8pLDUNYUvg==/109951163607265397.jpg\\\",\\\"size\\\":\\\"1365x1016\\\"}\"},{\"type\":13,\"id\":0,\"content\":\"<p>更多信息请关注@网易电音品牌放刺FEVER&nbsp;</p>\"}],\"userId\":**********,\"cover\":109951163607288125,\"headPic\":0,\"shareContent\":\"\",\"wxTitle\":\"\",\"showComment\":true,\"status\":1,\"seriesId\":2839053,\"pubTime\":1539697258009,\"readCount\":80,\"tags\":[\"电子音乐\",\"EDM\",\"DJ\",\"曲风\",\"电音节\",\"电子音乐\",\"EDM\",\"DJ\",\"曲风\",\"电音节\"],\"pubImmidiatly\":true,\"auditor\":\"\",\"auditTime\":0,\"auditStatus\":0,\"startText\":\"热爱电音的你，是否也想变身DJ？DJ&音乐制作人也能拿到学士、硕士学位？ 号外号外！网易放刺电音制作学院（Point Blank China）落地杭州！ 成为科班出身电音制作人不是梦！在国际师\",\"delReason\":\"\",\"showRelated\":true,\"fromBackend\":true,\"rectanglePic\":109951163607266199,\"updateTime\":*************,\"reward\":false,\"summary\":\"热爱电音的你，是否也想变身DJ？DJ&音乐制作人也能拿到学士、硕士学位？ 号外号外！网\",\"memo\":null,\"adInfo\":\"\",\"categoryId\":3,\"hotScore\":0.0,\"recomdTitle\":\"\",\"recomdContent\":\"\",\"number\":16},\"creator\":{\"userId\":**********,\"userType\":3,\"nickname\":\"网易电音品牌放刺FEVER\",\"avatarImgId\":109951163339602331,\"avatarUrl\":\"http://p2.music.126.net/UgpnSzetnErHoCDz3b1jkA==/109951163339602331.jpg\",\"backgroundImgId\":109951163533459268,\"backgroundUrl\":\"http://p1.music.126.net/RVHQRmdWeAj3P-HCX4OU0g==/109951163533459268.jpg\",\"signature\":\"网易电音品牌，诞生于2018年，基于上海、专注中国，致力于为中国电音乐迷创造优质的电音体验。\",\"createTime\":*************,\"userName\":\"\",\"accountType\":1,\"shortUserName\":\"\",\"birthday\":-*************,\"authority\":0,\"gender\":1,\"accountStatus\":0,\"province\":310000,\"city\":310101,\"authStatus\":1,\"description\":\"网易电音品牌\",\"detailDescription\":\"网易电音品牌\",\"defaultAvatar\":false,\"expertTags\":null,\"experts\":null,\"djStatus\":10,\"locationStatus\":30,\"vipType\":0,\"followed\":false,\"mutual\":false,\"authenticated\":true,\"lastLoginTime\":*************,\"lastLoginIP\":\"**************\",\"remarkName\":null,\"viptypeVersion\":0},\"shareCount\":4,\"commentCount\":4,\"likedCount\":6,\"liked\":false,\"rewardCount\":0,\"rewardMoney\":0.0,\"relatedResource\":null,\"rectanglePicUrl\":\"http://p2.music.126.net/Ufjbh8GoNj5bZ1JO-9tyMg==/109951163607266199.jpg\",\"coverUrl\":\"http://p2.music.126.net/rLCFTOKke57jkod0IIeWRg==/109951163607288125.jpg\",\"categoryId\":3,\"categoryName\":\"幕后故事\",\"showRelated\":true,\"summary\":\"热爱电音的你，是否也想变身DJ？DJ&音乐制作人也能拿到学士、硕士学位？ 号外号外！网\",\"reward\":false,\"memo\":null,\"addTime\":1539697258009,\"shareContent\":\"\",\"wxTitle\":\"中国最权威电音学院落地杭州啦！\",\"mainTitle\":\"中国最权威电音学院落地杭州啦！\",\"showComment\":true,\"seriesId\":2839053,\"readCount\":80,\"tags\":[\"电子音乐\",\"EDM\",\"DJ\",\"曲风\",\"电音节\",\"电子音乐\",\"EDM\",\"DJ\",\"曲风\",\"电音节\"],\"recmdTitle\":\"中国最权威电音学院落地杭州啦！\",\"recmdContent\":\"热爱电音的你，是否也想变身DJ？DJ&音乐制作人也能拿到学士、硕士学位？ 号外号外！网\",\"commentThreadId\":\"A_TO_6_********\",\"title\":\"中国最权威电音学院落地杭州啦！\",\"url\":\"/m/topic/********\",\"id\":********,\"number\":16},\"type\":8,\"title\":\"分享专栏文章\"}",
	 *             "newMsgCount": 0
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": 1,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "109951164873881480",
	 *                 "avatarImgId": 109951164873881470,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": -*************,
	 *                 "gender": 1,
	 *                 "accountStatus": 0,
	 *                 "city": 110101,
	 *                 "detailDescription": "网易云音乐官方账号",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 10,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/pmHS4fcQtcNEGewNb5HRhg==/****************.jpg",
	 *                 "description": "网易云音乐官方账号",
	 *                 "avatarUrl": "https://p2.music.126.net/LzIA_BYxqJj9mrj1NfCWDQ==/109951164873881480.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 2,
	 *                 "vipType": 11,
	 *                 "experts": null,
	 *                 "province": 110000,
	 *                 "remarkName": null,
	 *                 "nickname": "网易云音乐",
	 *                 "signature": "网易云音乐是8亿人都在使用的音乐平台，致力于帮助音乐爱好者发现音乐惊喜，帮助音乐人实现梦想。 \n2019年8月31日起，将不再提供实时在线人工服务。您可以优先通过自助方式解决问题，如仍需求助，可在相关页面留下您的问题，后续会有人工为您解答，辛苦您耐心等待，给您带来的不便敬请谅解。 如果仍然不能解决您的问题，可以邮件我们： 用户：<EMAIL> 音乐人：<EMAIL>",
	 *                 "authority": 3,
	 *                 "blacklist": false,
	 *                 "artistName": "网易云音乐"
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"promotionUrl\":{\"id\":0,\"cover\":109951163334453629,\"title\":\"立即开通\",\"text\":\"消息提醒\",\"url\":\"https://music.163.com/m/at/vipxk10\",\"addTime\":0,\"coverUrl\":\"http://p1.music.126.net/cxuU16ROGBsrT-tVvUrCTQ==/109951163334453629.jpg\",\"coverStr\":\"109951163334453629\"},\"msg\":\"网易云音乐新用户专享福利来袭！开通低至10元/月！快来参与吧！\",\"type\":12,\"title\":\"立即开通\"}",
	 *             "newMsgCount": 0
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": ********,
	 *                 "backgroundImgIdStr": "109951163264910108",
	 *                 "avatarImgIdStr": "109951164879601059",
	 *                 "avatarImgId": 109951164879601060,
	 *                 "backgroundImgId": 109951163264910110,
	 *                 "birthday": ************,
	 *                 "gender": 1,
	 *                 "accountStatus": 0,
	 *                 "city": 330100,
	 *                 "detailDescription": "网易云音乐小编",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 10,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/k_a_lZripnbDF-p3H7PloA==/109951163264910108.jpg",
	 *                 "description": "网易云音乐小编",
	 *                 "avatarUrl": "https://p2.music.126.net/wveGD2tCQA5mcnU0eCIclA==/109951164879601059.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 10,
	 *                 "vipType": 10,
	 *                 "experts": null,
	 *                 "province": 330000,
	 *                 "remarkName": null,
	 *                 "nickname": "潇洒小编",
	 *                 "signature": "网易云音乐是8亿人都在使用的音乐平台，致力于帮助音乐爱好者发现音乐惊喜，帮助音乐人实现梦想。\n\n如您在使用电台过程中碰到任何问题，还请私信潇洒问题及图片，给潇洒留言。潇洒小编留言处理时间：周一至周五10：00 - 18：00，（法定节假日除外），我们会尽快回复。\n\n如果仍然不能解决您的问题，请邮件我们：\n用户：<EMAIL>\n音乐人：<EMAIL>",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"promotionUrl\":{\"id\":0,\"cover\":109951163260380861,\"title\":\"5周年感恩季精品电台全场五折\",\"text\":\"重要通知\",\"url\":\"http://music.163.com/m/at/5adb1abe3d11ffde45144ebf\",\"addTime\":0,\"coverUrl\":\"http://p1.music.126.net/wqWE9yBcDMeyIT2y-bq43g==/109951163260380861.jpg\",\"coverStr\":\"109951163260380861\"},\"msg\":\"今天是大云村五周年纪念日！云村电台特别在4.23-4.25，为大家准备了限时五折优惠活动！\\n\\n你想听的陈立客厅、蒋勋新说红楼梦、焦享乐、听歌学英文等精品电台统统五折！72小时后就要恢复原价\\n\\n一共60多个付费精品课程和100多本精选有声书全部五折优惠！赶紧下手了。\\n\\n这么好的事也记得喊上你的朋友一起啊！\\n\\n活动传送门，请使用手机客户端打开哦↓↓↓\",\"type\":12,\"title\":\"5周年感恩季精品电台全场五折\"}",
	 *             "newMsgCount": 0
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": **********,
	 *                 "backgroundImgIdStr": "109951162868126486",
	 *                 "avatarImgIdStr": "109951163237661387",
	 *                 "avatarImgId": 109951163237661390,
	 *                 "backgroundImgId": 109951162868126480,
	 *                 "birthday": -*************,
	 *                 "gender": 1,
	 *                 "accountStatus": 0,
	 *                 "city": 310101,
	 *                 "detailDescription": "著名作词人",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 10,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg",
	 *                 "description": "著名作词人",
	 *                 "avatarUrl": "https://p2.music.126.net/BUs2AJK89vsgTsSMn6Zykw==/109951163237661387.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 10,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 310000,
	 *                 "remarkName": null,
	 *                 "nickname": "方文山",
	 *                 "signature": "",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"大家好，我是方文山，这次我带着20多年的创作经验，30首不曾发表的诗作来云村开课，那些中国风歌词背后的故事和我这些年合作过的大咖轶事，在《方文山的音乐诗词课》中全部说给你听，让你跟我一起重温那些触动心弦的字字句句。更有我亲自参与的#寻找写词达人#创作大赛等你来参加，快来加入我的音乐小宇宙吧。\",\"djRadio\":{\"id\":*********,\"dj\":{\"defaultAvatar\":false,\"province\":310000,\"authStatus\":1,\"followed\":false,\"avatarUrl\":\"http://p1.music.126.net/BUs2AJK89vsgTsSMn6Zykw==/109951163237661387.jpg\",\"accountStatus\":0,\"gender\":1,\"city\":310101,\"birthday\":-*************,\"userId\":**********,\"userType\":0,\"nickname\":\"方文山\",\"signature\":\"\",\"description\":\"著名作词人\",\"detailDescription\":\"著名作词人\",\"avatarImgId\":109951163237661387,\"backgroundImgId\":109951162868126486,\"backgroundUrl\":\"http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg\",\"authority\":0,\"mutual\":false,\"expertTags\":null,\"experts\":null,\"djStatus\":10,\"vipType\":0,\"remarkName\":null,\"avatarImgIdStr\":\"109951163237661387\",\"backgroundImgIdStr\":\"109951162868126486\",\"avatarImgId_str\":\"109951163237661387\"},\"name\":\"方文山的音乐诗词课\",\"picUrl\":\"http://p1.music.126.net/JaUbwwzHW0dUMhhJB1KLRw==/109951163240521920.jpg\",\"desc\":\"特别福利\\n#寻找写词达人#创作大赛同步开启\\n下一个为周杰伦写词的人，可能就是你！\\n\\n面向对象\\n所有写词爱好者，所有热爱音乐创作的小伙伴\\n\\n参赛资格\\n凡购买《方文山的音乐诗词课》的用户均可参赛\\n\\n活动奖励\\n最终脱颖而出的优秀词作将由网易云音乐出资购买，请当红音乐人演唱，有可能下一首爆红单曲就是由你填词哦~\\n\\n活动详情：\\n方法一：关注“网易云音乐”公众号，回复“方文山”即可看到活动详情。\\n方法二：收听【大咖推荐】徐梦圆：跟我一起来参加#寻找写词达人#创作大赛\\n点击音频旋转的封面可以也可以看到详细活动规则\\n\\n方文山的音乐诗词课\\n从《青花瓷》到《烟花易冷》，他的词中有豪气血性，也有婉约深情。万般变化之间，除了细腻的情感，更有扎实的功底。“中国风”热潮至今，追随和效仿者甚众，得其精髓者却寥寥。\\n你是否也曾被某些音符和文字击中，自己词穷到只能惊呼“好听”？\\n你是否也曾受他的影响尝试创作，却苦寻门道不得，总觉得差点火候？\\n那么，你不该错过《方文山的音乐诗词课》。\\n\\n印象方文山\\n是“词圣”，更是“华语乐坛背后的男人”\\n二十年创作生涯，方文山亲手开创中国风热潮。作品得奖无数，被收入国中读本，他的江湖地位无可撼动。\\n方文山与周董互相成就，被称为“周杰伦背后的男人”。除此之外，林俊杰《醉赤壁》、陈奕迅《烟味》、孙燕姿《直来直往》皆出自他手，大批华语歌手背后都有方文山的身影。\\n\\n爱中国风，更爱中华传统文化\\n\\t高晓松曾盛赞：用中文写歌词，纯以美来说，方文山是第一人。把中文之美写到这个地步，他不输李商隐、李煜。\\n但于他，更重要的是发扬歌词背后的中华传统文化。歌词之外，他亦是作家兼诗人，出版九本书籍，自创风格“素颜韵脚诗”，打开新的审美空间。而他创办的“西塘汉服文化周”，至今已成首屈一指的汉服文化盛事。\\n\\n文山开讲：\\n创作解码：左手文学，右手乐理，方文山用专业的素养和通俗的语言，带你了解“中国风”的创作奥秘。\\n国风古韵：从古诗词的意象意境中汲取养料，方文山向我们展现“中国风”歌曲中深厚的文化底蕴。\\n合作故事：台前幕后、明星轶事，方文山和我们分享与周杰伦等大咖合作的点点滴滴。\\n独家诗作：方文山从未发表过的30首新诗作，将在节目中首次呈现，只给知音的你听。\\n\\n内容大纲：\\n1 痴人｜问世间情为何物\\n\\t《发如雪》《烟花易冷》《菊花台》《千里之外》《东风破》\\n2 浪子｜天涯尽头是风沙\\n\\t《红尘客栈》《娘子》《天涯过客》《爷爷泡的茶》\\n3 书生｜帘外芭蕉惹骤雨\\n\\t《青花瓷》《兰亭序》《乱舞春秋》《本草纲目》\\n4 高手｜我的拳脚了得\\n\\t《霍元甲》《双截棍》\\n5 遗珠｜方文山的私房收藏\\n\\t不被大众熟知的经典歌曲，方文山从未发表过的30首诗作。\\n\\n适合谁听：\\n周杰伦迷：逐返与偶像共情的岁月时光，重温那些触动心弦的字字句句。\\n音乐发烧友：重温记忆里经典歌词的诞生历程，探索文字和音符的化学反应。\\n“中国风”痴迷者：爱听更要会欣赏，方文山带你寻根词曲背后的古风古韵。\\n创作小新手：揭秘一首歌的创作过程，手把手教你打破语言惯性，让文字与心灵联动。\\n\\n\\n出品方：方道文山流X蜻蜓FM   \\n联合独家首发平台：网易云音乐X蜻蜓FM\",\"subCount\":533,\"programCount\":7,\"createTime\":1523184401603,\"categoryId\":2,\"category\":\"音乐故事\",\"radioFeeType\":2,\"feeScope\":0,\"buyed\":false,\"videos\":{\"720P\":\"http://v4.music.126.net/20180410200051/167a23e7165532d820b7a5e74b3fc097/cloudmusic/cdbfb3b9fa076edd536b89564e06fcfc.mp4\",\"480P\":\"http://v4.music.126.net/20180410200051/f6b2738d5c49d284301849a38e760ecb/cloudmusic/d38f3420cecb148d22b838654d2977a3.mp4\"},\"finished\":false,\"underShelf\":false,\"purchaseCount\":0,\"price\":6900,\"originalPrice\":6900,\"discountPrice\":null,\"lastProgramCreateTime\":1523267809282,\"lastProgramName\":null,\"lastProgramId\":1368787105,\"picId\":109951163240521920,\"rcmdText\":\"周杰伦等你下课，方文山等你上课\",\"composeVideo\":true,\"shareCount\":221,\"displayAuthor\":false,\"notes\":[],\"feeDesc\":[{\"type\":1,\"id\":0,\"content\":\"特别福利\\n#寻找写词达人#创作大赛同步开启\\n下一个为周杰伦写词的人，可能就是你！\\n\\n面向对象\\n所有写词爱好者，所有热爱音乐创作的小伙伴\\n\\n参赛资格\\n凡购买《方文山的音乐诗词课》的用户均可参赛\\n\\n活动奖励\\n最终脱颖而出的优秀词作将由网易云音乐出资购买，请当红音乐人演唱，有可能下一首爆红单曲就是由你填词哦~\\n\\n活动详情：\\n方法一：关注“网易云音乐”公众号，回复“方文山”即可看到活动详情。\\n方法二：收听【大咖推荐】徐梦圆：跟我一起来参加#寻找写词达人#创作大赛\\n点击音频旋转的封面可以也可以看到详细活动规则\\n\\n方文山的音乐诗词课\\n从《青花瓷》到《烟花易冷》，他的词中有豪气血性，也有婉约深情。万般变化之间，除了细腻的情感，更有扎实的功底。“中国风”热潮至今，追随和效仿者甚众，得其精髓者却寥寥。\\n你是否也曾被某些音符和文字击中，自己词穷到只能惊呼“好听”？\\n你是否也曾受他的影响尝试创作，却苦寻门道不得，总觉得差点火候？\\n那么，你不该错过《方文山的音乐诗词课》。\\n\\n印象方文山\\n是“词圣”，更是“华语乐坛背后的男人”\\n二十年创作生涯，方文山亲手开创中国风热潮。作品得奖无数，被收入国中读本，他的江湖地位无可撼动。\\n方文山与周董互相成就，被称为“周杰伦背后的男人”。除此之外，林俊杰《醉赤壁》、陈奕迅《烟味》、孙燕姿《直来直往》皆出自他手，大批华语歌手背后都有方文山的身影。\\n\\n爱中国风，更爱中华传统文化\\n\\t高晓松曾盛赞：用中文写歌词，纯以美来说，方文山是第一人。把中文之美写到这个地步，他不输李商隐、李煜。\\n但于他，更重要的是发扬歌词背后的中华传统文化。歌词之外，他亦是作家兼诗人，出版九本书籍，自创风格“素颜韵脚诗”，打开新的审美空间。而他创办的“西塘汉服文化周”，至今已成首屈一指的汉服文化盛事。\\n\\n文山开讲：\\n创作解码：左手文学，右手乐理，方文山用专业的素养和通俗的语言，带你了解“中国风”的创作奥秘。\\n国风古韵：从古诗词的意象意境中汲取养料，方文山向我们展现“中国风”歌曲中深厚的文化底蕴。\\n合作故事：台前幕后、明星轶事，方文山和我们分享与周杰伦等大咖合作的点点滴滴。\\n独家诗作：方文山从未发表过的30首新诗作，将在节目中首次呈现，只给知音的你听。\\n\\n内容大纲：\\n1 痴人｜问世间情为何物\\n\\t《发如雪》《烟花易冷》《菊花台》《千里之外》《东风破》\\n2 浪子｜天涯尽头是风沙\\n\\t《红尘客栈》《娘子》《天涯过客》《爷爷泡的茶》\\n3 书生｜帘外芭蕉惹骤雨\\n\\t《青花瓷》《兰亭序》《乱舞春秋》《本草纲目》\\n4 高手｜我的拳脚了得\\n\\t《霍元甲》《双截棍》\\n5 遗珠｜方文山的私房收藏\\n\\t不被大众熟知的经典歌曲，方文山从未发表过的30首诗作。\\n\\n适合谁听：\\n周杰伦迷：逐返与偶像共情的岁月时光，重温那些触动心弦的字字句句。\\n音乐发烧友：重温记忆里经典歌词的诞生历程，探索文字和音符的化学反应。\\n“中国风”痴迷者：爱听更要会欣赏，方文山带你寻根词曲背后的古风古韵。\\n创作小新手：揭秘一首歌的创作过程，手把手教你打破语言惯性，让文字与心灵联动。\\n\\n\\n出品方：方道文山流X蜻蜓FM   \\n联合独家首发平台：网易云音乐X蜻蜓FM\",\"height\":null,\"width\":null,\"imageContentURLInvalid\":false}],\"djUsers\":[],\"likedCount\":0,\"crowds\":[],\"commentCount\":0},\"type\":13,\"title\":\"分享有声电台\"}",
	 *             "newMsgCount": 0
	 *         },
	 *         {
	 *             "fromUser": {
	 *                 "userId": ********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 1,
	 *                 "accountStatus": 0,
	 *                 "city": 330100,
	 *                 "detailDescription": "云音乐Android客户端开发组官方账号",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 10,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/HlmdYUtmoHSFSi869VFJrQ==/****************.jpg",
	 *                 "description": "云音乐Android客户端开发组官方账号",
	 *                 "avatarUrl": "https://p2.music.126.net/lBBVW9RkHgsdnoUg85Fzbg==/****************.jpg",
	 *                 "authStatus": 1,
	 *                 "expertTags": null,
	 *                 "userType": 10,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 330000,
	 *                 "remarkName": null,
	 *                 "nickname": "无所不能的Android开发组",
	 *                 "signature": "网易云音乐是6亿人都在使用的音乐平台，致力于帮助音乐爱好者发现音乐惊喜，帮助音乐人实现梦想。\n任何问题，可以私信反馈，也可以加入Android端QQ群反馈：538109760，反馈邮箱：<EMAIL>\n如果仍然不能解决您的问题，请邮件我们：\n用户：<EMAIL>\n音乐人：<EMAIL>",
	 *                 "authority": 0,
	 *                 "blacklist": false,
	 *                 "artistName": null
	 *             },
	 *             "toUser": {
	 *                 "userId": *********,
	 *                 "backgroundImgIdStr": "****************",
	 *                 "avatarImgIdStr": "****************",
	 *                 "avatarImgId": ****************,
	 *                 "backgroundImgId": ****************,
	 *                 "birthday": ************,
	 *                 "gender": 2,
	 *                 "accountStatus": 0,
	 *                 "city": 370200,
	 *                 "detailDescription": "",
	 *                 "defaultAvatar": false,
	 *                 "djStatus": 0,
	 *                 "followed": false,
	 *                 "mutual": false,
	 *                 "backgroundUrl": "https://p2.music.126.net/Efu7aMxa86uufzDR91mmJw==/****************.jpg",
	 *                 "description": "",
	 *                 "avatarUrl": "https://p2.music.126.net/YpiqjxouCsS-llBgsHbEsw==/****************.jpg",
	 *                 "authStatus": 0,
	 *                 "expertTags": null,
	 *                 "userType": 0,
	 *                 "vipType": 0,
	 *                 "experts": null,
	 *                 "province": 370000,
	 *                 "remarkName": null,
	 *                 "nickname": "混球玩意儿的小可爱",
	 *                 "signature": "",
	 *                 "authority": 0
	 *             },
	 *             "noticeAccountFlag": false,
	 *             "noticeAccount": null,
	 *             "lastMsgTime": *************,
	 *             "lastMsg": "{\"msg\":\"小米用户，您好！由于MIUI系统设置限制，云音乐可能无法在“回到桌面”或“切换到其他程序”后继续播放。如果出现这类问题，请参考以下教程进行设置：：http://music.163.com/static/help/xiaomi.html\",\"type\":6,\"title\":\"\"}",
	 *             "newMsgCount": 0
	 *         }
	 *     ],
	 *     "code": 200,
	 *     "more": false,
	 *     "newMsgCount": 12
	 * }
	 */
	private int code;
	private boolean more;
	private int newMsgCount;
	private ArrayList<Msg> msgs;

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public boolean isMore() {
		return more;
	}

	public void setMore(boolean more) {
		this.more = more;
	}

	public int getNewMsgCount() {
		return newMsgCount;
	}

	public void setNewMsgCount(int newMsgCount) {
		this.newMsgCount = newMsgCount;
	}

	public ArrayList<Msg> getMsgs() {
		return msgs;
	}

	public void setMsgs(ArrayList<Msg> msgs) {
		this.msgs = msgs;
	}

	public static class Msg{
		private UserEventBean.EventsBean.UserBean fromUser;
		private UserEventBean.EventsBean.UserBean toUser;
		private boolean noticeAccountFlag;
		private Object noticeAccount;
		private int newMsgCount;
		private String lastMsg;
		private long lastMsgTime;

		public UserEventBean.EventsBean.UserBean getFromUser() {
			return fromUser;
		}

		public void setFromUser(UserEventBean.EventsBean.UserBean fromUser) {
			this.fromUser = fromUser;
		}

		public UserEventBean.EventsBean.UserBean getToUser() {
			return toUser;
		}

		public void setToUser(UserEventBean.EventsBean.UserBean toUser) {
			this.toUser = toUser;
		}

		public boolean isNoticeAccountFlag() {
			return noticeAccountFlag;
		}

		public void setNoticeAccountFlag(boolean noticeAccountFlag) {
			this.noticeAccountFlag = noticeAccountFlag;
		}

		public Object getNoticeAccount() {
			return noticeAccount;
		}

		public void setNoticeAccount(Object noticeAccount) {
			this.noticeAccount = noticeAccount;
		}

		public int getNewMsgCount() {
			return newMsgCount;
		}

		public void setNewMsgCount(int newMsgCount) {
			this.newMsgCount = newMsgCount;
		}

		public String getLastMsg() {
			return lastMsg;
		}

		public void setLastMsg(String lastMsg) {
			this.lastMsg = lastMsg;
		}

		public long getLastMsgTime() {
			return lastMsgTime;
		}

		public void setLastMsgTime(long lastMsgTime) {
			this.lastMsgTime = lastMsgTime;
		}
	}
}
