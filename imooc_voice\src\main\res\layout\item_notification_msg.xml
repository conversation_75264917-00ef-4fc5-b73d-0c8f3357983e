<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="70dp"
    android:paddingTop="5dp"
    android:paddingRight="10dp"
    android:paddingBottom="5dp">

    <ImageView
        android:id="@+id/iv_item_notification_fromuser_avatar"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="12dp"
        android:src="@drawable/ic_test" />

    <ImageView
        android:id="@+id/iv_item_notification_user_tag"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_alignRight="@+id/iv_item_notification_fromuser_avatar"
        android:layout_alignBottom="@+id/iv_item_notification_fromuser_avatar"
        android:src="@drawable/ic_official"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_item_notification_fromuser"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_toRightOf="@+id/iv_item_notification_fromuser_avatar"
        android:text="xxxxx"
        android:textColor="@color/black"
        android:textSize="15sp" />

    <TextView
        android:id="@+id/iv_item_notification_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_item_notification_fromuser"
        android:layout_alignLeft="@+id/tv_item_notification_fromuser"
        android:layout_toLeftOf="@+id/tv_item_notification_count"
        android:layout_marginRight="10dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginTop="3dp"
        android:textSize="12sp"
        android:text="xxxx" />

    <TextView
        android:id="@+id/tv_item_notification_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/tv_item_notification_fromuser"
        android:layout_alignParentRight="true"
        android:textSize="9sp"
        android:text="17:00" />

    <TextView
        android:id="@+id/tv_item_notification_count"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_below="@+id/tv_item_notification_time"
        android:layout_alignRight="@+id/tv_item_notification_time"
        android:layout_marginTop="5dp"
        android:background="@drawable/bg_circle_red"
        android:gravity="center"
        android:text="1"
        android:textSize="10sp"
        android:visibility="visible"
        android:textColor="@color/white" />

</RelativeLayout>