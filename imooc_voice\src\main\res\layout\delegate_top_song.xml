<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <android.support.design.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <android.support.design.widget.AppBarLayout
            android:id="@+id/appbar_gedan_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/gray"
            app:elevation="0dp">

            <android.support.design.widget.CollapsingToolbarLayout
                android:id="@+id/collapsing_toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <ImageView
                    android:id="@+id/iv_top_song_img_cover"
                    android:layout_width="match_parent"
                    android:layout_height="150dp"
                    android:scaleType="fitXY"
                    android:src="@mipmap/top_china" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="15dp"
                    android:layout_marginTop="140dp"
                    android:background="@drawable/bg_dailyrecommend" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="25dp"
                    android:background="@color/white"
                    android:orientation="horizontal"
                    android:layout_marginTop="155dp"
                    app:layout_scrollFlags="scroll">

                    <ImageView
                        android:layout_width="23dp"
                        android:layout_height="23dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="15dp"
                        android:src="@drawable/ic_play_gray" />

                    <TextView
                        android:id="@+id/tv_top_song_all_play"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="15dp"
                        android:text="全部播放"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/tv_top_song_num"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1" />

                    <ImageView
                        android:id="@+id/select"
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:layout_gravity="center|clip_vertical"
                        android:layout_marginRight="5dp"
                        android:background="?android:attr/selectableItemBackground"
                        android:focusable="false"
                        android:gravity="end"
                        android:src="@drawable/ic_menu_black" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginRight="15dp"
                        android:gravity="center"
                        android:text="多选"
                        android:textColor="@color/black" />

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="5dp"
                    android:background="@color/white"
                    android:orientation="horizontal"
                    android:layout_marginTop="180dp"/>
            </android.support.design.widget.CollapsingToolbarLayout>


        </android.support.design.widget.AppBarLayout>

        <FrameLayout
            android:id="@+id/loadframe"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />


    </android.support.design.widget.CoordinatorLayout>
</LinearLayout>