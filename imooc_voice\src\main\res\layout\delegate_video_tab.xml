<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <net.lucode.hackware.magicindicator.MagicIndicator
        android:id="@+id/magic_indicator_tab"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginTop="15dp"
        android:layout_marginLeft="10dp"
        android:background="?android:attr/selectableItemBackground" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/app_background" />

    <android.support.v4.view.ViewPager
        android:id="@+id/view_pager_tab"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="10" />
</LinearLayout>