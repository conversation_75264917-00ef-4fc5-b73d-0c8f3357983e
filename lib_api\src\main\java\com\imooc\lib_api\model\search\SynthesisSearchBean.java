package com.imooc.lib_api.model.search;


import com.imooc.lib_api.model.song.SongDetailBean;

import java.util.List;

public class SynthesisSearchBean {

    /**
     * result : {"song":{"moreText":"查看更多同名歌曲","highText":null,"more":true,"songs":[{"name":"<PERSON><PERSON><PERSON>","id":27917548,"pst":0,"t":0,"ar":[{"id":48514,"name":"Boz Scaggs","tns":[],"alias":[]}],"alia":[],"pop":90,"st":0,"rt":"","fee":8,"v":6,"crbt":null,"cf":"","al":{"id":2694934,"name":"The Essential Boz Scaggs","picUrl":"http://p2.music.126.net/_YmTuIMoG0dh2OJSOE-LPw==/17829680556434243.jpg","tns":[],"pic_str":"17829680556434243","pic":17829680556434244},"dt":354746,"h":{"br":320000,"fid":0,"size":14220536,"vd":-2.65076E-4},"m":{"br":160000,"fid":0,"size":7123590,"vd":0.0324002},"l":{"br":96000,"fid":0,"size":4284811,"vd":-2.65076E-4},"a":null,"cd":"2","no":1,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":1,"s_id":0,"mark":0,"rtype":0,"rurl":null,"mst":9,"cp":7001,"mv":0,"publishTime":1382976000007,"officialTags":[],"privilege":{"id":27917548,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":256},"alg":"alg_song_basic_d"},{"name":"Save My Soul","id":34072632,"pst":0,"t":0,"ar":[{"id":60943,"name":"JoJo","tns":[],"alias":[]}],"alia":[],"pop":80,"st":0,"rt":null,"fee":8,"v":8,"crbt":null,"cf":"","al":{"id":3270985,"name":"III.","picUrl":"http://p2.music.126.net/wOsQ_2u6XrhESTsiN3KirQ==/7974757838028840.jpg","tns":[],"pic":7974757838028840},"dt":224908,"h":{"br":320000,"fid":0,"size":8996614,"vd":-1.67},"m":{"br":160000,"fid":0,"size":4498329,"vd":-1.25},"l":{"br":96000,"fid":0,"size":2699015,"vd":-1.31},"a":null,"cd":"1","no":2,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":0,"s_id":0,"mark":0,"rtype":0,"rurl":null,"mst":9,"cp":7002,"mv":5269161,"publishTime":1440086400007,"officialTags":[],"privilege":{"id":34072632,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":256},"alg":"alg_song_basic_d"},{"name":"JOJO","id":484324172,"pst":0,"t":0,"ar":[{"id":********,"name":"西藏病人","tns":[],"alias":[]}],"alia":[],"pop":85,"st":0,"rt":null,"fee":0,"v":62,"crbt":null,"cf":"","al":{"id":35355954,"name":"转经道上的屠夫","picUrl":"http://p2.music.126.net/Swy59brVeGMJE3a0FKuCKg==/109951162925144969.jpg","tns":[],"pic_str":"109951162925144969","pic":109951162925144980},"dt":132999,"h":{"br":320000,"fid":0,"size":5322754,"vd":0},"m":{"br":192000,"fid":0,"size":3193670,"vd":0},"l":{"br":128000,"fid":0,"size":2129128,"vd":0},"a":null,"cd":"1","no":7,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":2,"s_id":0,"mark":64,"rtype":0,"rurl":null,"mst":9,"cp":0,"mv":0,"publishTime":1491921682146,"officialTags":[],"privilege":{"id":484324172,"fee":0,"payed":0,"st":0,"pl":999000,"dl":999000,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":999000,"toast":false,"flag":130},"alg":"alg_song_basic_d"},{"name":"Cry On My Shoulder","id":17194024,"pst":0,"t":0,"ar":[{"id":152734,"name":"Deutschland sucht den Superstar","tns":[],"alias":[]},{"id":104700,"name":"Various Artists","tns":[],"alias":["欧美群星"],"alia":["欧美群星"]}],"alia":[],"pop":100,"st":0,"rt":"","fee":8,"v":21,"crbt":null,"cf":"","al":{"id":1584599,"name":"United","picUrl":"http://p2.music.126.net/_1SSamf87l4mo77TZiWCWQ==/576144092962639.jpg","tns":[],"pic":576144092962639},"dt":236000,"h":{"br":320000,"fid":0,"size":9453358,"vd":-24700},"m":{"br":192000,"fid":0,"size":5672081,"vd":-22000},"l":{"br":128000,"fid":0,"size":3781442,"vd":-20500},"a":null,"cd":"1","no":3,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":1,"s_id":0,"mark":0,"rtype":0,"rurl":null,"mst":9,"cp":7002,"mv":0,"publishTime":1044806400000,"officialTags":[],"privilege":{"id":17194024,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":256},"alg":"alg_song_basic_d"},{"name":"Vibe.","id":*********,"pst":0,"t":0,"ar":[{"id":60943,"name":"JoJo","tns":[],"alias":[]}],"alia":[],"pop":90,"st":0,"rt":null,"fee":8,"v":52,"crbt":null,"cf":"","al":{"id":34875732,"name":"Mad Love. (Deluxe)","picUrl":"http://p2.music.126.net/c9ebY8Y_-CDds08sq1DNfQ==/109951163276011542.jpg","tns":[],"pic_str":"109951163276011542","pic":109951163276011540},"dt":187480,"h":{"br":320000,"fid":0,"size":7500321,"vd":-1.0E-4},"m":{"br":192000,"fid":0,"size":4500210,"vd":-1.0E-4},"l":{"br":128000,"fid":0,"size":3000155,"vd":-1.0E-4},"a":null,"cd":"1","no":6,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":2,"s_id":0,"mark":8192,"rtype":0,"rurl":null,"mst":9,"cp":7002,"mv":0,"publishTime":*************,"officialTags":[],"privilege":{"id":*********,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":260},"alg":"alg_song_basic_d"}]},"code":200,"djRadio":{"moreText":"查看全部","djRadios":[{"id":*********,"dj":{"defaultAvatar":false,"province":130000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/PO8TsjfLTdbG-igdGFSBLw==/109951163602792360.jpg","accountStatus":0,"gender":1,"city":130200,"birthday":************,"userId":********,"userType":0,"nickname":"郭-小嘉","signature":"名士文可公","description":"","detailDescription":"","avatarImgId":109951163602792350,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951163602792360","backgroundImgIdStr":"****************","avatarImgId_str":"109951163602792360"},"name":"JOJO的奇妙冒险·天堂之眼bgm集","picUrl":"http://p2.music.126.net/1clU9O807xMbCTKHUarG9w==/109951163120085182.jpg","desc":"因为抽风和补档，顺序可能有点问题大家见谅","subCount":1033,"programCount":57,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":1551188233994,"lastProgramName":"EOH·角色选择","lastProgramId":2059743782,"picId":109951163120085180,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic_d","commentCount":0}],"more":true},"rec_type":null,"artist":{"artists":[{"id":48514,"name":"Boz Scaggs","picUrl":"http://p2.music.126.net/DHo_dBKUU5uRpBe1FeWEHw==/***************.jpg","alias":[],"albumSize":29,"picId":***************,"img1v1Url":"http://p2.music.126.net/6e8fyO1MMhcG7YYYsMOxsA==/***************.jpg","img1v1":***************,"mvSize":0,"followed":false,"alg":"alg_artist_basic_d","trans":null,"accountId":*********},{"id":60943,"name":"JoJo","picUrl":"http://p2.music.126.net/sNBvINATXmjEFXQ9wS92sg==/***************.jpg","alias":[],"albumSize":44,"picId":***************,"img1v1Url":"http://p2.music.126.net/64DFPu_th-OsgDUm9PdsDw==/***************.jpg","img1v1":***************,"mvSize":12,"followed":false,"alg":"alg_artist_basic_d","trans":null},{"id":********,"name":"西藏病人","picUrl":"http://p2.music.126.net/wkPui_S4APuwza5NjULrJw==/109951162869491151.jpg","alias":[],"albumSize":3,"picId":109951162869491150,"img1v1Url":"http://p2.music.126.net/XzrAkQKMYFLT-neYnsqu1A==/109951162869490290.jpg","accountId":*********,"img1v1":109951162869490290,"mvSize":0,"followed":false,"alg":"alg_artist_basic_d","trans":null}],"more":false},"playList":{"moreText":"查看全部","more":true,"playLists":[{"id":**********,"name":"荒木飞吕彦耳边响起【曲目已整】JOJO音乐梗","coverImgUrl":"http://p2.music.126.net/RhF1Of6EnzjIMEWoMpz81g==/109951164272965937.jpg","creator":{"nickname":"大家来看我JOJO立","userId":*********,"userType":300,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":170,"userId":*********,"playCount":343449,"bookCount":6138,"description":"★歌曲评论区勿刷JO梗\n1个人私心致大乔\n幻影之血\n8JOJO来源\n9艾琳娜\n10迪奥\n11齐贝林\n12SPW\n战斗潮流\n13丝吉Q\n14丽萨丽萨\n15艾哲红石\n16桑塔纳\n17瓦乌姆\n18艾西迪西\n19卡兹\n星辰斗士\n2021空条承太郎（误\n22迪奥\n23阿布德尔\n24波鲁那雷夫\n25伊奇\n26死神13\n27恩雅\n28亚空瘴气\n29看门鸟\n30恋人\n31欧因波因哥\n32巴斯提\n不灭钻石\n3335东方仗助\n3638吉良吉影\n39吉良吉广\n40安杰罗\n41虹村形兆\n42虹村亿泰\n43康一\n44由花子\n45小林玉美\n46胖重\n47岸边露伴\n48康一的狗\n49东尼欧\n50喷上裕也\n51未起隆\n52静\n53间田敏和\n54钢田一丰大\n55音石明\n56乙雅三\n57猫草\n黄金之风\n5859乔鲁诺乔巴拿\n6061迪亚波罗\n62多比欧\n63布加拉提\n64阿帕基\n65米斯达\n66福葛\n67纳兰迦\n68特里休\n69乌龟\n70银镇\n71卢卡\n72波尔波\n73滚石\n74镜中人\n75手艺工作\n76小脚\n77壮烈成仁\n78沙滩男孩\n79娃娃脸\n80金属制品\n81面部特写\n82冲击\n8384BIG\n85青春岁月\n86绿洲\n石之海\n8788空条徐伦\n89绿婴\n9092普奇神父\n93绿海豚街\n94安波里欧\n9596安娜苏\n97艾梅斯\n98天气预报\n99FF\n100奎丝\n101地狱高速公路\n102盎格鲁\n103跳跃闪电\n104行星波动\n105恶魔枷锁\n106107章节名\n飙马野郎\n108110乔尼乔斯达\n111114法尼瓦伦泰\n115117迪亚哥\n118119杰洛齐贝林\n120122曼登提姆\n123124波克洛克\n125128HP\n129林果\n130131砂男\n132133罗宾逊\n134135芬芳家族\n136137高乔\n138奥耶格摩巴\n139波特派哈特\n140雨男\n141甜糖山\n142十一人众\n143南北战争\n144金属气球\n145巧克力迪斯科\n146147威卡毕博\n148149马金特\n福音\n150东方定助\n151广濑康穗\n152吉良京\n153东方宪助\n154东方常敏\n155东方大弥\n156东方剑\n157卡蕾拉\n158笹目樱二郎\n159安定街\n160八木山夜露\n161大年寺山爱唱\n162费克斯兄弟\n163田最环\n164奇迹之人\n165多洛米蒂\n166豆铣礼\n167168城市游击队\n169170穷汤姆","highQuality":false,"track":{"name":"Time To Say Goodbye","id":3405842,"position":4,"alias":[],"status":0,"fee":8,"copyrightId":14002,"disc":"1","no":4,"artists":[{"name":"Sarah Brightman","id":74618,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"The Very Best of Sarah Brightman 1990-2000","id":345234,"type":"专辑","size":16,"picId":789449348785243,"blurPicUrl":"http://p2.music.126.net/GyywegDyasFj_KAgps7VIg==/789449348785243.jpg","companyId":0,"pic":789449348785243,"picUrl":"http://p2.music.126.net/GyywegDyasFj_KAgps7VIg==/789449348785243.jpg","publishTime":991584000007,"description":"","tags":"","company":"Atlantic UK","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":1,"copyrightId":5003,"commentThreadId":"R_AL_3_345234","artists":[{"name":"Sarah Brightman","id":74618,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":244689,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"600902000002230987","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_3405842","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"mvid":0,"bMusic":{"name":"Time To Say Goodbye","id":21988403,"size":2949479,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":244689,"volumeDelta":-1.67},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":"Time To Say Goodbye","id":21988401,"size":9794690,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":244689,"volumeDelta":-2.09},"mMusic":{"name":"Time To Say Goodbye","id":21988402,"size":4905528,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":244689,"volumeDelta":-1.66},"lMusic":{"name":"Time To Say Goodbye","id":21988403,"size":2949479,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":244689,"volumeDelta":-1.67}},"alg":null},{"id":2008543401,"name":"JOJO 黄金之风替身名岀处","coverImgUrl":"http://p2.music.126.net/YyZOneUXnfqjobDE1-2fhQ==/109951163629092105.jpg","creator":{"nickname":"Loser-Jerry","userId":537494168,"userType":0,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":46,"userId":537494168,"playCount":97934,"bookCount":972,"description":"翻译可能有不同\n黄金体验 -《The Gold Experience》(专辑)____Prince\n\n钢链手指-《Sticky Finger》(专辑）___The Rolling Stones\n黑色安息日-《Black Sabbath》Black Sabbath\n忧伤蓝调-The Moody Blues\n柔软机器-Soft Machine\n性感手枪-Sex Pistol (四个人的乐队）\n手艺工作-KraftWerk\n小脚-Little Feet\n史密斯飞船-Aerosmith\n镜中人-Man in the Mirror__Michael Jackson\n紫烟-Purple Haze__Jimi Hendrix\n沙滩男孩-Beaches Boy\n","highQuality":false,"track":{"name":"裏切り者のレクイエム","id":1352199795,"position":0,"alias":["TV动画《JOJO的奇妙冒险 黄金之风》OP2"],"status":0,"fee":0,"copyrightId":663018,"disc":"01","no":1,"artists":[{"name":"ハセガワダイスケ","id":1025284,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"裏切り者のレクイエム","id":75859706,"type":"EP/Single","size":3,"picId":109951163930146740,"blurPicUrl":"http://p2.music.126.net/vH_S4MfzS5S6ISr59aJ5rA==/109951163930146742.jpg","companyId":0,"pic":109951163930146740,"picUrl":"http://p2.music.126.net/vH_S4MfzS5S6ISr59aJ5rA==/109951163930146742.jpg","publishTime":1552665600000,"description":"","tags":"","company":"Warner Bros. Entertainment","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":0,"copyrightId":663018,"commentThreadId":"R_AL_3_75859706","artists":[{"name":"ハセガワダイスケ","id":1025284,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"picId_str":"109951163930146742"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":210592,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_1352199795","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":0,"mvid":0,"bMusic":{"name":null,"id":3739648316,"size":3370466,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":210592,"volumeDelta":-20500},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":null,"id":3739648314,"size":8426101,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":210592,"volumeDelta":-24600},"mMusic":{"name":null,"id":3739648315,"size":5055678,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":210592,"volumeDelta":-22000},"lMusic":{"name":null,"id":3739648316,"size":3370466,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":210592,"volumeDelta":-20500}},"alg":null},{"id":784872872,"name":"JoJo的奇妙音乐梗","coverImgUrl":"http://p2.music.126.net/bSoR7k-xMx5HjxiFK-NJzw==/19021551160741329.jpg","creator":{"nickname":"JoJo的奇喵猫险","userId":124196736,"userType":0,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":39,"userId":124196736,"playCount":1038322,"bookCount":22801,"description":"荒木老师主业漫画家，副业服装设计，兴趣爱好分享音乐wryyyyy\n1.来吧！普奇神父！ 2.茸茸的黄金体验\n3.歌名是D4C- 歌手是柱之男艾斯迪斯\n4..一二部ed 5.三部ed 6.四部ed\n7.大弥的加州大床\n8-10.吉良吉影的杀皇、枯萎穿心、败者吃尘\n11.盎格鲁的波西米亚狂想曲\n12.神父的天堂zhi造\n13.七页無駄哥的青春岁月 14.赛可的绿洲\n15.音石明的辛红辣椒 16.dio的骇人恶兽\n17.四蛋的软又湿 18.面具雨男的替身\n19.荒木老师的替身不解释 20.南北战争\n21.艾梅斯的kiss 22.巧克力迪斯可\n23.歌名是曼登提姆的替身 歌手是漆黑意志乔尼\n24.特里休的辣妹 25.加丘的白色相簿 26.五部的乌龟\n27.康穗的佩斯利公园 28.疯狂钻石\n29.(☞゜ω゜)☞：乔乔，我不想再做人了！\n30.被长者反弹死的泪眼卢卡 31.布姐的钢链手指\n32.镜中人 33.老板的罪恶王者 34.石之自由\n35.歌名是咕咕娃娃 36.自残狂魔的替身\n37.骇游天外\n38.歌手是放弃思考的卡兹sama\n39.诸君！我们意大利再见！ヾ(￣▽￣)Bye~Bye~\n\n＝=> To be continued","highQuality":false,"track":{"name":"Jolene","id":2423350,"position":1,"alias":[],"status":0,"fee":8,"copyrightId":7001,"disc":"1","no":1,"artists":[{"name":"Dolly Parton","id":53258,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"Jolene","id":244392,"type":"专辑","size":10,"picId":885106860413997,"blurPicUrl":"http://p2.music.126.net/_i4bJ9blVdcV6fs49mPHoA==/885106860413997.jpg","companyId":0,"pic":885106860413997,"picUrl":"http://p2.music.126.net/_i4bJ9blVdcV6fs49mPHoA==/885106860413997.jpg","publishTime":131990400007,"description":"","tags":"","company":"Buddha","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":1,"copyrightId":7001,"commentThreadId":"R_AL_3_244392","artists":[{"name":"Dolly Parton","id":53258,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]},"starred":false,"popularity":95,"score":95,"starredNum":0,"duration":162482,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":"cc8790a6f3486f49c3d2e0998b6da823","audition":null,"copyFrom":"","commentThreadId":"R_SO_4_2423350","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"mvid":0,"bMusic":{"name":"Jolene","id":16897903,"size":1972381,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":162482,"volumeDelta":0.255276},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":"Jolene","id":16897901,"size":6517896,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":162482,"volumeDelta":-2.65076E-4},"mMusic":{"name":"Jolene","id":16897902,"size":3271398,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":162482,"volumeDelta":0.343626},"lMusic":{"name":"Jolene","id":16897903,"size":1972381,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":162482,"volumeDelta":0.255276}},"alg":null},{"id":2502683,"name":"JOJO角色/替身名称来源","coverImgUrl":"http://p2.music.126.net/QeAepm08QnRuX6vHXLfpjQ==/14454179858758010.jpg","creator":{"nickname":"visiona3r","userId":1268814,"userType":0,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":195,"userId":1268814,"playCount":948421,"bookCount":31844,"description":"一-七部完成！本意是探索荒木老师的音乐品味，因此除了名称来源以外，也加入了漫画人物介绍中他们喜欢的乐队歌曲等。【参考：我爱摇滚乐】","highQuality":false,"track":{"name":"カーニバル","id":28914363,"position":1,"alias":[],"status":0,"fee":0,"copyrightId":663018,"disc":"1","no":1,"artists":[{"name":"otetsu","id":15169,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},{"name":"GUMI","id":188274,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"Carnival","id":2943113,"type":"专辑","size":9,"picId":8928034417591572,"blurPicUrl":"http://p1.music.126.net/Z1YfhRBgdys8b36owCLZcQ==/8928034417591572.jpg","companyId":0,"pic":8928034417591572,"picUrl":"http://p1.music.126.net/Z1YfhRBgdys8b36owCLZcQ==/8928034417591572.jpg","publishTime":1307808000000,"description":"","tags":"","company":"THE VOC@LOiD M@STER 16","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_2943113","artists":[{"name":"otetsu","id":15169,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]},"starred":false,"popularity":65,"score":65,"starredNum":0,"duration":217756,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_28914363","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"mvid":0,"bMusic":{"name":"カーニバル","id":49521872,"size":2614764,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":217756,"volumeDelta":-5.05},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":"カーニバル","id":49521873,"size":6970909,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":256000,"playTime":217756,"volumeDelta":-5.4},"mMusic":{"name":"カーニバル","id":49521871,"size":4357236,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":217756,"volumeDelta":-4.97},"lMusic":{"name":"カーニバル","id":49521872,"size":2614764,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":217756,"volumeDelta":-5.05}},"alg":null},{"id":26809094,"name":"【JOJO】荒木老师推荐十张专辑（70年代）","coverImgUrl":"http://p2.music.126.net/AZEMp4gx7v1m5wiqJeE_kg==/2922501906671039.jpg","creator":{"nickname":"VZZOTL93sh","userId":3764061,"userType":200,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":115,"userId":3764061,"playCount":419406,"bookCount":19812,"description":"【搬运】（年代久远，不分先后）整理自台湾中文版漫画封面的话。网易好厉害，这10张老碟都有。封面win7自带1分钟鼠绘。","highQuality":false,"track":{"name":"Sing","id":17141614,"position":1,"alias":[],"status":0,"fee":8,"copyrightId":7003,"disc":"1","no":1,"artists":[{"name":"Carpenters","id":89363,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"Now & Then","id":1578695,"type":"专辑","size":15,"picId":595935302262467,"blurPicUrl":"http://p2.music.126.net/vEmRAUfchWx6z4r24MTBsg==/595935302262467.jpg","companyId":0,"pic":595935302262467,"picUrl":"http://p2.music.126.net/vEmRAUfchWx6z4r24MTBsg==/595935302262467.jpg","publishTime":105724800007,"description":"","tags":"","company":"Digital Distribution Estonia","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":3,"copyrightId":7003,"commentThreadId":"R_AL_3_1578695","artists":[{"name":"Carpenters","id":89363,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]},"starred":false,"popularity":75,"score":75,"starredNum":0,"duration":199602,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_17141614","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"mvid":0,"bMusic":{"name":"Sing","id":10650994,"size":2438782,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":199602,"volumeDelta":2.21206},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":"Sing","id":10650992,"size":8022925,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":199602,"volumeDelta":1.9331},"mMusic":{"name":"Sing","id":10650993,"size":4034550,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":199602,"volumeDelta":2.2374},"lMusic":{"name":"Sing","id":10650994,"size":2438782,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":199602,"volumeDelta":2.21206}},"alg":null}]},"album":{"moreText":"查看全部","albums":[{"name":"The Essential Boz Scaggs","id":2694934,"type":"专辑","size":32,"picId":17829680556434244,"blurPicUrl":"http://p4.music.126.net/_YmTuIMoG0dh2OJSOE-LPw==/17829680556434243.jpg","companyId":0,"pic":17829680556434244,"picUrl":"http://p3.music.126.net/_YmTuIMoG0dh2OJSOE-LPw==/17829680556434243.jpg","publishTime":1383004800000,"description":"","tags":"","company":"索尼音乐","briefDesc":"","artist":{"name":"Boz Scaggs","id":48514,"picId":***************,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p4.music.126.net/DHo_dBKUU5uRpBe1FeWEHw==/***************.jpg","img1v1Url":"http://p3.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":29,"alias":[],"trans":"","musicSize":381,"topicPerson":0,"img1v1Id_str":"18686200114669622","alia":[]},"songs":null,"alias":[],"status":0,"copyrightId":7001,"commentThreadId":"R_AL_3_2694934","artists":[{"name":"Boz Scaggs","id":48514,"picId":0,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0,"img1v1Id_str":"18686200114669622"}],"paid":false,"onSale":false,"picId_str":"17829680556434243","alg":"alg_album_basic_d"},{"name":"III.","id":3270985,"type":"EP/Single","size":3,"picId":7974757838028840,"blurPicUrl":"http://p3.music.126.net/wOsQ_2u6XrhESTsiN3KirQ==/7974757838028840.jpg","companyId":0,"pic":7974757838028840,"picUrl":"http://p4.music.126.net/wOsQ_2u6XrhESTsiN3KirQ==/7974757838028840.jpg","publishTime":1440086400007,"description":"","tags":"","company":"Atlantic Recording ","briefDesc":"","artist":{"name":"JoJo","id":60943,"picId":***************,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/sNBvINATXmjEFXQ9wS92sg==/***************.jpg","img1v1Url":"http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":44,"alias":[],"trans":"","musicSize":367,"topicPerson":0,"img1v1Id_str":"18686200114669622","alia":[]},"songs":null,"alias":[],"status":3,"copyrightId":0,"commentThreadId":"R_AL_3_3270985","artists":[{"name":"JoJo","id":60943,"picId":0,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0,"img1v1Id_str":"18686200114669622"}],"paid":false,"onSale":false,"alg":"alg_album_basic_d"},{"name":"转经道上的屠夫","id":35355954,"type":"专辑","size":8,"picId":109951162925144980,"blurPicUrl":"http://p3.music.126.net/Swy59brVeGMJE3a0FKuCKg==/109951162925144969.jpg","companyId":0,"pic":109951162925144980,"picUrl":"http://p3.music.126.net/Swy59brVeGMJE3a0FKuCKg==/109951162925144969.jpg","publishTime":1491921682146,"description":"","tags":"","company":null,"briefDesc":"","artist":{"name":"西藏病人","id":********,"picId":109951162869491150,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/wkPui_S4APuwza5NjULrJw==/109951162869491151.jpg","img1v1Url":"http://p3.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":3,"alias":[],"trans":"","musicSize":24,"topicPerson":0,"picId_str":"109951162869491151","img1v1Id_str":"18686200114669622","alia":[]},"songs":null,"alias":[],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_35355954","artists":[{"name":"西藏病人","id":********,"picId":0,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0,"img1v1Id_str":"18686200114669622"}],"paid":false,"onSale":false,"picId_str":"109951162925144969","alg":"alg_album_basic_d"}],"more":true},"video":{"moreText":"查看全部","more":true,"videos":[{"coverUrl":"http://p2.music.126.net/R6pCjd9qmH4LQm1idWOZig==/109951163888857522.jpg","title":"《jojo的奇妙冒险》各部BOSS的精彩操作（大误）P3","durationms":468695,"playTime":742404,"type":1,"creator":[{"userId":1484858585,"userName":"TwoFlyCats"}],"aliaName":null,"transName":null,"vid":"E7324156176B933029D10CB5E92E5B3F","markTypes":[],"alg":"alg_video_basic_d"},{"coverUrl":"http://p2.music.126.net/grzV0_AtUPoyTT-0lEuJFQ==/109951163918650420.jpg","title":"一起来跟着黑帮摇//","durationms":59000,"playTime":1165453,"type":1,"creator":[{"userId":266801423,"userName":"我还没玩死"}],"aliaName":null,"transName":null,"vid":"B48CB3849AC193321699BA76DCE3B822","markTypes":[],"alg":"alg_video_basic_d"},{"coverUrl":"http://p2.music.126.net/JwynWqljv9tc92D8cONR1g==/109951163639035741.jpg","title":"【红房子】Jojo性感高跟Jazz编舞小野猫经典曲Buttons","durationms":287105,"playTime":2530726,"type":1,"creator":[{"userId":469558241,"userName":"千叶小美"}],"aliaName":null,"transName":null,"vid":"B7BBC6977873723CF3A4D4946D558A8D","markTypes":[101,111],"alg":"alg_video_basic_d"},{"coverUrl":"http://p2.music.126.net/T2ypQQsWZw6HXx_h45TrbA==/109951163905058891.jpg","title":"《jojo的奇妙冒险》各部BOSS的精彩操作（大误）P4","durationms":147320,"playTime":283180,"type":1,"creator":[{"userId":1484858585,"userName":"TwoFlyCats"}],"aliaName":null,"transName":null,"vid":"3F028FB2D67183CEE90DD30FAEF918F5","markTypes":[],"alg":"alg_video_basic_d"},{"coverUrl":"http://p2.music.126.net/ygHygKm0ZBvsgue8G2osEQ==/109951164098918413.jpg","title":"jojo的奇妙冒险黄金之风：接受父子二人组的七页木大吧！","durationms":47760,"playTime":333186,"type":1,"creator":[{"userId":1484858585,"userName":"TwoFlyCats"}],"aliaName":null,"transName":null,"vid":"31ADD5E5E855117905B6CC28F9774CB2","markTypes":[],"alg":"alg_video_basic_d"}]},"sim_query":{"sim_querys":[{"keyword":"阿姨压一压","alg":"default"},{"keyword":"黑帮摇","alg":"default"},{"keyword":"Awake","alg":"default"},{"keyword":"Taylor Swift新歌","alg":"default"},{"keyword":"哪吒","alg":"default"},{"keyword":"画","alg":"default"}],"more":false},"rec_query":[null],"user":{"moreText":"查看全部","more":true,"users":[{"defaultAvatar":false,"province":310000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/MlJ9Pnz7iFi7n8Dqw4aezg==/****************.jpg","accountStatus":0,"gender":1,"city":310108,"birthday":*************,"userId":3701993,"userType":0,"nickname":"___JoJo","signature":"uoye voli","description":"","detailDescription":"","avatarImgId":****************,"backgroundImgId":109951162868126480,"backgroundUrl":"http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"****************","backgroundImgIdStr":"109951162868126486","alg":"alg_user_basic_d"}]},"order":["playList","song","video","sim_query","artist","album","djRadio","user"]}
     * code : 200
     */

    private ResultBean result;
    private int code;

    public ResultBean getResult() {
        return result;
    }

    public void setResult(ResultBean result) {
        this.result = result;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public static class ResultBean {
        /**
         * song : {"moreText":"查看更多同名歌曲","highText":null,"more":true,"songs":[{"name":"JoJo","id":27917548,"pst":0,"t":0,"ar":[{"id":48514,"name":"Boz Scaggs","tns":[],"alias":[]}],"alia":[],"pop":90,"st":0,"rt":"","fee":8,"v":6,"crbt":null,"cf":"","al":{"id":2694934,"name":"The Essential Boz Scaggs","picUrl":"http://p2.music.126.net/_YmTuIMoG0dh2OJSOE-LPw==/17829680556434243.jpg","tns":[],"pic_str":"17829680556434243","pic":17829680556434244},"dt":354746,"h":{"br":320000,"fid":0,"size":14220536,"vd":-2.65076E-4},"m":{"br":160000,"fid":0,"size":7123590,"vd":0.0324002},"l":{"br":96000,"fid":0,"size":4284811,"vd":-2.65076E-4},"a":null,"cd":"2","no":1,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":1,"s_id":0,"mark":0,"rtype":0,"rurl":null,"mst":9,"cp":7001,"mv":0,"publishTime":1382976000007,"officialTags":[],"privilege":{"id":27917548,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":256},"alg":"alg_song_basic_d"},{"name":"Save My Soul","id":34072632,"pst":0,"t":0,"ar":[{"id":60943,"name":"JoJo","tns":[],"alias":[]}],"alia":[],"pop":80,"st":0,"rt":null,"fee":8,"v":8,"crbt":null,"cf":"","al":{"id":3270985,"name":"III.","picUrl":"http://p2.music.126.net/wOsQ_2u6XrhESTsiN3KirQ==/7974757838028840.jpg","tns":[],"pic":7974757838028840},"dt":224908,"h":{"br":320000,"fid":0,"size":8996614,"vd":-1.67},"m":{"br":160000,"fid":0,"size":4498329,"vd":-1.25},"l":{"br":96000,"fid":0,"size":2699015,"vd":-1.31},"a":null,"cd":"1","no":2,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":0,"s_id":0,"mark":0,"rtype":0,"rurl":null,"mst":9,"cp":7002,"mv":5269161,"publishTime":1440086400007,"officialTags":[],"privilege":{"id":34072632,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":256},"alg":"alg_song_basic_d"},{"name":"JOJO","id":484324172,"pst":0,"t":0,"ar":[{"id":********,"name":"西藏病人","tns":[],"alias":[]}],"alia":[],"pop":85,"st":0,"rt":null,"fee":0,"v":62,"crbt":null,"cf":"","al":{"id":35355954,"name":"转经道上的屠夫","picUrl":"http://p2.music.126.net/Swy59brVeGMJE3a0FKuCKg==/109951162925144969.jpg","tns":[],"pic_str":"109951162925144969","pic":109951162925144980},"dt":132999,"h":{"br":320000,"fid":0,"size":5322754,"vd":0},"m":{"br":192000,"fid":0,"size":3193670,"vd":0},"l":{"br":128000,"fid":0,"size":2129128,"vd":0},"a":null,"cd":"1","no":7,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":2,"s_id":0,"mark":64,"rtype":0,"rurl":null,"mst":9,"cp":0,"mv":0,"publishTime":1491921682146,"officialTags":[],"privilege":{"id":484324172,"fee":0,"payed":0,"st":0,"pl":999000,"dl":999000,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":999000,"toast":false,"flag":130},"alg":"alg_song_basic_d"},{"name":"Cry On My Shoulder","id":17194024,"pst":0,"t":0,"ar":[{"id":152734,"name":"Deutschland sucht den Superstar","tns":[],"alias":[]},{"id":104700,"name":"Various Artists","tns":[],"alias":["欧美群星"],"alia":["欧美群星"]}],"alia":[],"pop":100,"st":0,"rt":"","fee":8,"v":21,"crbt":null,"cf":"","al":{"id":1584599,"name":"United","picUrl":"http://p2.music.126.net/_1SSamf87l4mo77TZiWCWQ==/576144092962639.jpg","tns":[],"pic":576144092962639},"dt":236000,"h":{"br":320000,"fid":0,"size":9453358,"vd":-24700},"m":{"br":192000,"fid":0,"size":5672081,"vd":-22000},"l":{"br":128000,"fid":0,"size":3781442,"vd":-20500},"a":null,"cd":"1","no":3,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":1,"s_id":0,"mark":0,"rtype":0,"rurl":null,"mst":9,"cp":7002,"mv":0,"publishTime":1044806400000,"officialTags":[],"privilege":{"id":17194024,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":256},"alg":"alg_song_basic_d"},{"name":"Vibe.","id":*********,"pst":0,"t":0,"ar":[{"id":60943,"name":"JoJo","tns":[],"alias":[]}],"alia":[],"pop":90,"st":0,"rt":null,"fee":8,"v":52,"crbt":null,"cf":"","al":{"id":34875732,"name":"Mad Love. (Deluxe)","picUrl":"http://p2.music.126.net/c9ebY8Y_-CDds08sq1DNfQ==/109951163276011542.jpg","tns":[],"pic_str":"109951163276011542","pic":109951163276011540},"dt":187480,"h":{"br":320000,"fid":0,"size":7500321,"vd":-1.0E-4},"m":{"br":192000,"fid":0,"size":4500210,"vd":-1.0E-4},"l":{"br":128000,"fid":0,"size":3000155,"vd":-1.0E-4},"a":null,"cd":"1","no":6,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":2,"s_id":0,"mark":8192,"rtype":0,"rurl":null,"mst":9,"cp":7002,"mv":0,"publishTime":*************,"officialTags":[],"privilege":{"id":*********,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":260},"alg":"alg_song_basic_d"}]}
         * code : 200
         * djRadio : {"moreText":"查看全部","djRadios":[{"id":*********,"dj":{"defaultAvatar":false,"province":130000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/PO8TsjfLTdbG-igdGFSBLw==/109951163602792360.jpg","accountStatus":0,"gender":1,"city":130200,"birthday":************,"userId":********,"userType":0,"nickname":"郭-小嘉","signature":"名士文可公","description":"","detailDescription":"","avatarImgId":109951163602792350,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951163602792360","backgroundImgIdStr":"****************","avatarImgId_str":"109951163602792360"},"name":"JOJO的奇妙冒险·天堂之眼bgm集","picUrl":"http://p2.music.126.net/1clU9O807xMbCTKHUarG9w==/109951163120085182.jpg","desc":"因为抽风和补档，顺序可能有点问题大家见谅","subCount":1033,"programCount":57,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":1551188233994,"lastProgramName":"EOH·角色选择","lastProgramId":2059743782,"picId":109951163120085180,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic_d","commentCount":0}],"more":true}
         * rec_type : null
         * artist : {"artists":[{"id":48514,"name":"Boz Scaggs","picUrl":"http://p2.music.126.net/DHo_dBKUU5uRpBe1FeWEHw==/***************.jpg","alias":[],"albumSize":29,"picId":***************,"img1v1Url":"http://p2.music.126.net/6e8fyO1MMhcG7YYYsMOxsA==/***************.jpg","img1v1":***************,"mvSize":0,"followed":false,"alg":"alg_artist_basic_d","trans":null},{"id":60943,"name":"JoJo","picUrl":"http://p2.music.126.net/sNBvINATXmjEFXQ9wS92sg==/***************.jpg","alias":[],"albumSize":44,"picId":***************,"img1v1Url":"http://p2.music.126.net/64DFPu_th-OsgDUm9PdsDw==/***************.jpg","img1v1":***************,"mvSize":12,"followed":false,"alg":"alg_artist_basic_d","trans":null},{"id":********,"name":"西藏病人","picUrl":"http://p2.music.126.net/wkPui_S4APuwza5NjULrJw==/109951162869491151.jpg","alias":[],"albumSize":3,"picId":109951162869491150,"img1v1Url":"http://p2.music.126.net/XzrAkQKMYFLT-neYnsqu1A==/109951162869490290.jpg","accountId":*********,"img1v1":109951162869490290,"mvSize":0,"followed":false,"alg":"alg_artist_basic_d","trans":null}],"more":false}
         * playList : {"moreText":"查看全部","more":true,"playLists":[{"id":**********,"name":"荒木飞吕彦耳边响起【曲目已整】JOJO音乐梗","coverImgUrl":"http://p2.music.126.net/RhF1Of6EnzjIMEWoMpz81g==/109951164272965937.jpg","creator":{"nickname":"大家来看我JOJO立","userId":*********,"userType":300,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":170,"userId":*********,"playCount":343449,"bookCount":6138,"description":"★歌曲评论区勿刷JO梗\n1个人私心致大乔\n幻影之血\n8JOJO来源\n9艾琳娜\n10迪奥\n11齐贝林\n12SPW\n战斗潮流\n13丝吉Q\n14丽萨丽萨\n15艾哲红石\n16桑塔纳\n17瓦乌姆\n18艾西迪西\n19卡兹\n星辰斗士\n2021空条承太郎（误\n22迪奥\n23阿布德尔\n24波鲁那雷夫\n25伊奇\n26死神13\n27恩雅\n28亚空瘴气\n29看门鸟\n30恋人\n31欧因波因哥\n32巴斯提\n不灭钻石\n3335东方仗助\n3638吉良吉影\n39吉良吉广\n40安杰罗\n41虹村形兆\n42虹村亿泰\n43康一\n44由花子\n45小林玉美\n46胖重\n47岸边露伴\n48康一的狗\n49东尼欧\n50喷上裕也\n51未起隆\n52静\n53间田敏和\n54钢田一丰大\n55音石明\n56乙雅三\n57猫草\n黄金之风\n5859乔鲁诺乔巴拿\n6061迪亚波罗\n62多比欧\n63布加拉提\n64阿帕基\n65米斯达\n66福葛\n67纳兰迦\n68特里休\n69乌龟\n70银镇\n71卢卡\n72波尔波\n73滚石\n74镜中人\n75手艺工作\n76小脚\n77壮烈成仁\n78沙滩男孩\n79娃娃脸\n80金属制品\n81面部特写\n82冲击\n8384BIG\n85青春岁月\n86绿洲\n石之海\n8788空条徐伦\n89绿婴\n9092普奇神父\n93绿海豚街\n94安波里欧\n9596安娜苏\n97艾梅斯\n98天气预报\n99FF\n100奎丝\n101地狱高速公路\n102盎格鲁\n103跳跃闪电\n104行星波动\n105恶魔枷锁\n106107章节名\n飙马野郎\n108110乔尼乔斯达\n111114法尼瓦伦泰\n115117迪亚哥\n118119杰洛齐贝林\n120122曼登提姆\n123124波克洛克\n125128HP\n129林果\n130131砂男\n132133罗宾逊\n134135芬芳家族\n136137高乔\n138奥耶格摩巴\n139波特派哈特\n140雨男\n141甜糖山\n142十一人众\n143南北战争\n144金属气球\n145巧克力迪斯科\n146147威卡毕博\n148149马金特\n福音\n150东方定助\n151广濑康穗\n152吉良京\n153东方宪助\n154东方常敏\n155东方大弥\n156东方剑\n157卡蕾拉\n158笹目樱二郎\n159安定街\n160八木山夜露\n161大年寺山爱唱\n162费克斯兄弟\n163田最环\n164奇迹之人\n165多洛米蒂\n166豆铣礼\n167168城市游击队\n169170穷汤姆","highQuality":false,"track":{"name":"Time To Say Goodbye","id":3405842,"position":4,"alias":[],"status":0,"fee":8,"copyrightId":14002,"disc":"1","no":4,"artists":[{"name":"Sarah Brightman","id":74618,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"The Very Best of Sarah Brightman 1990-2000","id":345234,"type":"专辑","size":16,"picId":789449348785243,"blurPicUrl":"http://p2.music.126.net/GyywegDyasFj_KAgps7VIg==/789449348785243.jpg","companyId":0,"pic":789449348785243,"picUrl":"http://p2.music.126.net/GyywegDyasFj_KAgps7VIg==/789449348785243.jpg","publishTime":991584000007,"description":"","tags":"","company":"Atlantic UK","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":1,"copyrightId":5003,"commentThreadId":"R_AL_3_345234","artists":[{"name":"Sarah Brightman","id":74618,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":244689,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"600902000002230987","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_3405842","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"mvid":0,"bMusic":{"name":"Time To Say Goodbye","id":21988403,"size":2949479,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":244689,"volumeDelta":-1.67},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":"Time To Say Goodbye","id":21988401,"size":9794690,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":244689,"volumeDelta":-2.09},"mMusic":{"name":"Time To Say Goodbye","id":21988402,"size":4905528,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":244689,"volumeDelta":-1.66},"lMusic":{"name":"Time To Say Goodbye","id":21988403,"size":2949479,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":244689,"volumeDelta":-1.67}},"alg":null},{"id":2008543401,"name":"JOJO 黄金之风替身名岀处","coverImgUrl":"http://p2.music.126.net/YyZOneUXnfqjobDE1-2fhQ==/109951163629092105.jpg","creator":{"nickname":"Loser-Jerry","userId":537494168,"userType":0,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":46,"userId":537494168,"playCount":97934,"bookCount":972,"description":"翻译可能有不同\n黄金体验 -《The Gold Experience》(专辑)____Prince\n\n钢链手指-《Sticky Finger》(专辑）___The Rolling Stones\n黑色安息日-《Black Sabbath》Black Sabbath\n忧伤蓝调-The Moody Blues\n柔软机器-Soft Machine\n性感手枪-Sex Pistol (四个人的乐队）\n手艺工作-KraftWerk\n小脚-Little Feet\n史密斯飞船-Aerosmith\n镜中人-Man in the Mirror__Michael Jackson\n紫烟-Purple Haze__Jimi Hendrix\n沙滩男孩-Beaches Boy\n","highQuality":false,"track":{"name":"裏切り者のレクイエム","id":1352199795,"position":0,"alias":["TV动画《JOJO的奇妙冒险 黄金之风》OP2"],"status":0,"fee":0,"copyrightId":663018,"disc":"01","no":1,"artists":[{"name":"ハセガワダイスケ","id":1025284,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"裏切り者のレクイエム","id":75859706,"type":"EP/Single","size":3,"picId":109951163930146740,"blurPicUrl":"http://p2.music.126.net/vH_S4MfzS5S6ISr59aJ5rA==/109951163930146742.jpg","companyId":0,"pic":109951163930146740,"picUrl":"http://p2.music.126.net/vH_S4MfzS5S6ISr59aJ5rA==/109951163930146742.jpg","publishTime":1552665600000,"description":"","tags":"","company":"Warner Bros. Entertainment","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":0,"copyrightId":663018,"commentThreadId":"R_AL_3_75859706","artists":[{"name":"ハセガワダイスケ","id":1025284,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"picId_str":"109951163930146742"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":210592,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_1352199795","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":0,"mvid":0,"bMusic":{"name":null,"id":3739648316,"size":3370466,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":210592,"volumeDelta":-20500},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":null,"id":3739648314,"size":8426101,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":210592,"volumeDelta":-24600},"mMusic":{"name":null,"id":3739648315,"size":5055678,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":210592,"volumeDelta":-22000},"lMusic":{"name":null,"id":3739648316,"size":3370466,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":210592,"volumeDelta":-20500}},"alg":null},{"id":784872872,"name":"JoJo的奇妙音乐梗","coverImgUrl":"http://p2.music.126.net/bSoR7k-xMx5HjxiFK-NJzw==/19021551160741329.jpg","creator":{"nickname":"JoJo的奇喵猫险","userId":124196736,"userType":0,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":39,"userId":124196736,"playCount":1038322,"bookCount":22801,"description":"荒木老师主业漫画家，副业服装设计，兴趣爱好分享音乐wryyyyy\n1.来吧！普奇神父！ 2.茸茸的黄金体验\n3.歌名是D4C- 歌手是柱之男艾斯迪斯\n4..一二部ed 5.三部ed 6.四部ed\n7.大弥的加州大床\n8-10.吉良吉影的杀皇、枯萎穿心、败者吃尘\n11.盎格鲁的波西米亚狂想曲\n12.神父的天堂zhi造\n13.七页無駄哥的青春岁月 14.赛可的绿洲\n15.音石明的辛红辣椒 16.dio的骇人恶兽\n17.四蛋的软又湿 18.面具雨男的替身\n19.荒木老师的替身不解释 20.南北战争\n21.艾梅斯的kiss 22.巧克力迪斯可\n23.歌名是曼登提姆的替身 歌手是漆黑意志乔尼\n24.特里休的辣妹 25.加丘的白色相簿 26.五部的乌龟\n27.康穗的佩斯利公园 28.疯狂钻石\n29.(☞゜ω゜)☞：乔乔，我不想再做人了！\n30.被长者反弹死的泪眼卢卡 31.布姐的钢链手指\n32.镜中人 33.老板的罪恶王者 34.石之自由\n35.歌名是咕咕娃娃 36.自残狂魔的替身\n37.骇游天外\n38.歌手是放弃思考的卡兹sama\n39.诸君！我们意大利再见！ヾ(￣▽￣)Bye~Bye~\n\n＝=> To be continued","highQuality":false,"track":{"name":"Jolene","id":2423350,"position":1,"alias":[],"status":0,"fee":8,"copyrightId":7001,"disc":"1","no":1,"artists":[{"name":"Dolly Parton","id":53258,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"Jolene","id":244392,"type":"专辑","size":10,"picId":885106860413997,"blurPicUrl":"http://p2.music.126.net/_i4bJ9blVdcV6fs49mPHoA==/885106860413997.jpg","companyId":0,"pic":885106860413997,"picUrl":"http://p2.music.126.net/_i4bJ9blVdcV6fs49mPHoA==/885106860413997.jpg","publishTime":131990400007,"description":"","tags":"","company":"Buddha","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":1,"copyrightId":7001,"commentThreadId":"R_AL_3_244392","artists":[{"name":"Dolly Parton","id":53258,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]},"starred":false,"popularity":95,"score":95,"starredNum":0,"duration":162482,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":"cc8790a6f3486f49c3d2e0998b6da823","audition":null,"copyFrom":"","commentThreadId":"R_SO_4_2423350","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"mvid":0,"bMusic":{"name":"Jolene","id":16897903,"size":1972381,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":162482,"volumeDelta":0.255276},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":"Jolene","id":16897901,"size":6517896,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":162482,"volumeDelta":-2.65076E-4},"mMusic":{"name":"Jolene","id":16897902,"size":3271398,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":162482,"volumeDelta":0.343626},"lMusic":{"name":"Jolene","id":16897903,"size":1972381,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":162482,"volumeDelta":0.255276}},"alg":null},{"id":2502683,"name":"JOJO角色/替身名称来源","coverImgUrl":"http://p2.music.126.net/QeAepm08QnRuX6vHXLfpjQ==/14454179858758010.jpg","creator":{"nickname":"visiona3r","userId":1268814,"userType":0,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":195,"userId":1268814,"playCount":948421,"bookCount":31844,"description":"一-七部完成！本意是探索荒木老师的音乐品味，因此除了名称来源以外，也加入了漫画人物介绍中他们喜欢的乐队歌曲等。【参考：我爱摇滚乐】","highQuality":false,"track":{"name":"カーニバル","id":28914363,"position":1,"alias":[],"status":0,"fee":0,"copyrightId":663018,"disc":"1","no":1,"artists":[{"name":"otetsu","id":15169,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},{"name":"GUMI","id":188274,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"Carnival","id":2943113,"type":"专辑","size":9,"picId":8928034417591572,"blurPicUrl":"http://p1.music.126.net/Z1YfhRBgdys8b36owCLZcQ==/8928034417591572.jpg","companyId":0,"pic":8928034417591572,"picUrl":"http://p1.music.126.net/Z1YfhRBgdys8b36owCLZcQ==/8928034417591572.jpg","publishTime":1307808000000,"description":"","tags":"","company":"THE VOC@LOiD M@STER 16","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_2943113","artists":[{"name":"otetsu","id":15169,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]},"starred":false,"popularity":65,"score":65,"starredNum":0,"duration":217756,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_28914363","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"mvid":0,"bMusic":{"name":"カーニバル","id":49521872,"size":2614764,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":217756,"volumeDelta":-5.05},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":"カーニバル","id":49521873,"size":6970909,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":256000,"playTime":217756,"volumeDelta":-5.4},"mMusic":{"name":"カーニバル","id":49521871,"size":4357236,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":217756,"volumeDelta":-4.97},"lMusic":{"name":"カーニバル","id":49521872,"size":2614764,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":217756,"volumeDelta":-5.05}},"alg":null},{"id":26809094,"name":"【JOJO】荒木老师推荐十张专辑（70年代）","coverImgUrl":"http://p2.music.126.net/AZEMp4gx7v1m5wiqJeE_kg==/2922501906671039.jpg","creator":{"nickname":"VZZOTL93sh","userId":3764061,"userType":200,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":115,"userId":3764061,"playCount":419406,"bookCount":19812,"description":"【搬运】（年代久远，不分先后）整理自台湾中文版漫画封面的话。网易好厉害，这10张老碟都有。封面win7自带1分钟鼠绘。","highQuality":false,"track":{"name":"Sing","id":17141614,"position":1,"alias":[],"status":0,"fee":8,"copyrightId":7003,"disc":"1","no":1,"artists":[{"name":"Carpenters","id":89363,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"Now & Then","id":1578695,"type":"专辑","size":15,"picId":595935302262467,"blurPicUrl":"http://p2.music.126.net/vEmRAUfchWx6z4r24MTBsg==/595935302262467.jpg","companyId":0,"pic":595935302262467,"picUrl":"http://p2.music.126.net/vEmRAUfchWx6z4r24MTBsg==/595935302262467.jpg","publishTime":105724800007,"description":"","tags":"","company":"Digital Distribution Estonia","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":3,"copyrightId":7003,"commentThreadId":"R_AL_3_1578695","artists":[{"name":"Carpenters","id":89363,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]},"starred":false,"popularity":75,"score":75,"starredNum":0,"duration":199602,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_17141614","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"mvid":0,"bMusic":{"name":"Sing","id":10650994,"size":2438782,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":199602,"volumeDelta":2.21206},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":"Sing","id":10650992,"size":8022925,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":199602,"volumeDelta":1.9331},"mMusic":{"name":"Sing","id":10650993,"size":4034550,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":199602,"volumeDelta":2.2374},"lMusic":{"name":"Sing","id":10650994,"size":2438782,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":199602,"volumeDelta":2.21206}},"alg":null}]}
         * album : {"moreText":"查看全部","albums":[{"name":"The Essential Boz Scaggs","id":2694934,"type":"专辑","size":32,"picId":17829680556434244,"blurPicUrl":"http://p4.music.126.net/_YmTuIMoG0dh2OJSOE-LPw==/17829680556434243.jpg","companyId":0,"pic":17829680556434244,"picUrl":"http://p3.music.126.net/_YmTuIMoG0dh2OJSOE-LPw==/17829680556434243.jpg","publishTime":1383004800000,"description":"","tags":"","company":"索尼音乐","briefDesc":"","artist":{"name":"Boz Scaggs","id":48514,"picId":***************,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p4.music.126.net/DHo_dBKUU5uRpBe1FeWEHw==/***************.jpg","img1v1Url":"http://p3.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":29,"alias":[],"trans":"","musicSize":381,"topicPerson":0,"img1v1Id_str":"18686200114669622","alia":[]},"songs":null,"alias":[],"status":0,"copyrightId":7001,"commentThreadId":"R_AL_3_2694934","artists":[{"name":"Boz Scaggs","id":48514,"picId":0,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0,"img1v1Id_str":"18686200114669622"}],"paid":false,"onSale":false,"picId_str":"17829680556434243","alg":"alg_album_basic_d"},{"name":"III.","id":3270985,"type":"EP/Single","size":3,"picId":7974757838028840,"blurPicUrl":"http://p3.music.126.net/wOsQ_2u6XrhESTsiN3KirQ==/7974757838028840.jpg","companyId":0,"pic":7974757838028840,"picUrl":"http://p4.music.126.net/wOsQ_2u6XrhESTsiN3KirQ==/7974757838028840.jpg","publishTime":1440086400007,"description":"","tags":"","company":"Atlantic Recording ","briefDesc":"","artist":{"name":"JoJo","id":60943,"picId":***************,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/sNBvINATXmjEFXQ9wS92sg==/***************.jpg","img1v1Url":"http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":44,"alias":[],"trans":"","musicSize":367,"topicPerson":0,"img1v1Id_str":"18686200114669622","alia":[]},"songs":null,"alias":[],"status":3,"copyrightId":0,"commentThreadId":"R_AL_3_3270985","artists":[{"name":"JoJo","id":60943,"picId":0,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0,"img1v1Id_str":"18686200114669622"}],"paid":false,"onSale":false,"alg":"alg_album_basic_d"},{"name":"转经道上的屠夫","id":35355954,"type":"专辑","size":8,"picId":109951162925144980,"blurPicUrl":"http://p3.music.126.net/Swy59brVeGMJE3a0FKuCKg==/109951162925144969.jpg","companyId":0,"pic":109951162925144980,"picUrl":"http://p3.music.126.net/Swy59brVeGMJE3a0FKuCKg==/109951162925144969.jpg","publishTime":1491921682146,"description":"","tags":"","company":null,"briefDesc":"","artist":{"name":"西藏病人","id":********,"picId":109951162869491150,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/wkPui_S4APuwza5NjULrJw==/109951162869491151.jpg","img1v1Url":"http://p3.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":3,"alias":[],"trans":"","musicSize":24,"topicPerson":0,"picId_str":"109951162869491151","img1v1Id_str":"18686200114669622","alia":[]},"songs":null,"alias":[],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_35355954","artists":[{"name":"西藏病人","id":********,"picId":0,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0,"img1v1Id_str":"18686200114669622"}],"paid":false,"onSale":false,"picId_str":"109951162925144969","alg":"alg_album_basic_d"}],"more":true}
         * video : {"moreText":"查看全部","more":true,"videos":[{"coverUrl":"http://p2.music.126.net/R6pCjd9qmH4LQm1idWOZig==/109951163888857522.jpg","title":"《jojo的奇妙冒险》各部BOSS的精彩操作（大误）P3","durationms":468695,"playTime":742404,"type":1,"creator":[{"userId":1484858585,"userName":"TwoFlyCats"}],"aliaName":null,"transName":null,"vid":"E7324156176B933029D10CB5E92E5B3F","markTypes":[],"alg":"alg_video_basic_d"},{"coverUrl":"http://p2.music.126.net/grzV0_AtUPoyTT-0lEuJFQ==/109951163918650420.jpg","title":"一起来跟着黑帮摇//","durationms":59000,"playTime":1165453,"type":1,"creator":[{"userId":266801423,"userName":"我还没玩死"}],"aliaName":null,"transName":null,"vid":"B48CB3849AC193321699BA76DCE3B822","markTypes":[],"alg":"alg_video_basic_d"},{"coverUrl":"http://p2.music.126.net/JwynWqljv9tc92D8cONR1g==/109951163639035741.jpg","title":"【红房子】Jojo性感高跟Jazz编舞小野猫经典曲Buttons","durationms":287105,"playTime":2530726,"type":1,"creator":[{"userId":469558241,"userName":"千叶小美"}],"aliaName":null,"transName":null,"vid":"B7BBC6977873723CF3A4D4946D558A8D","markTypes":[101,111],"alg":"alg_video_basic_d"},{"coverUrl":"http://p2.music.126.net/T2ypQQsWZw6HXx_h45TrbA==/109951163905058891.jpg","title":"《jojo的奇妙冒险》各部BOSS的精彩操作（大误）P4","durationms":147320,"playTime":283180,"type":1,"creator":[{"userId":1484858585,"userName":"TwoFlyCats"}],"aliaName":null,"transName":null,"vid":"3F028FB2D67183CEE90DD30FAEF918F5","markTypes":[],"alg":"alg_video_basic_d"},{"coverUrl":"http://p2.music.126.net/ygHygKm0ZBvsgue8G2osEQ==/109951164098918413.jpg","title":"jojo的奇妙冒险黄金之风：接受父子二人组的七页木大吧！","durationms":47760,"playTime":333186,"type":1,"creator":[{"userId":1484858585,"userName":"TwoFlyCats"}],"aliaName":null,"transName":null,"vid":"31ADD5E5E855117905B6CC28F9774CB2","markTypes":[],"alg":"alg_video_basic_d"}]}
         * sim_query : {"sim_querys":[{"keyword":"阿姨压一压","alg":"default"},{"keyword":"黑帮摇","alg":"default"},{"keyword":"Awake","alg":"default"},{"keyword":"Taylor Swift新歌","alg":"default"},{"keyword":"哪吒","alg":"default"},{"keyword":"画","alg":"default"}],"more":false}
         * rec_query : [null]
         * user : {"moreText":"查看全部","more":true,"users":[{"defaultAvatar":false,"province":310000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/MlJ9Pnz7iFi7n8Dqw4aezg==/****************.jpg","accountStatus":0,"gender":1,"city":310108,"birthday":*************,"userId":3701993,"userType":0,"nickname":"___JoJo","signature":"uoye voli","description":"","detailDescription":"","avatarImgId":****************,"backgroundImgId":109951162868126480,"backgroundUrl":"http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"****************","backgroundImgIdStr":"109951162868126486","alg":"alg_user_basic_d"}]}
         * order : ["playList","song","video","sim_query","artist","album","djRadio","user"]
         */
        private int code;
        private SongBean song;
        private DjRadioBean djRadio;
        private Object rec_type;
        private ArtistBean artist;
        private PlayListBean playList;
        private AlbumBeanX album;
        private VideoBean video;
        private SimQueryBean sim_query;
        private UserBean user;
        private List<String> rec_query;
        private List<String> order;

        public SongBean getSong() {
            return song;
        }

        public void setSong(SongBean song) {
            this.song = song;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public DjRadioBean getDjRadio() {
            return djRadio;
        }

        public void setDjRadio(DjRadioBean djRadio) {
            this.djRadio = djRadio;
        }

        public Object getRec_type() {
            return rec_type;
        }

        public void setRec_type(Object rec_type) {
            this.rec_type = rec_type;
        }

        public ArtistBean getArtist() {
            return artist;
        }

        public void setArtist(ArtistBean artist) {
            this.artist = artist;
        }

        public PlayListBean getPlayList() {
            return playList;
        }

        public void setPlayList(PlayListBean playList) {
            this.playList = playList;
        }

        public AlbumBeanX getAlbum() {
            return album;
        }

        public void setAlbum(AlbumBeanX album) {
            this.album = album;
        }

        public VideoBean getVideo() {
            return video;
        }

        public void setVideo(VideoBean video) {
            this.video = video;
        }

        public SimQueryBean getSim_query() {
            return sim_query;
        }

        public void setSim_query(SimQueryBean sim_query) {
            this.sim_query = sim_query;
        }

        public UserBean getUser() {
            return user;
        }

        public void setUser(UserBean user) {
            this.user = user;
        }

        public List<String> getRec_query() {
            return rec_query;
        }

        public void setRec_query(List<String> rec_query) {
            this.rec_query = rec_query;
        }

        public List<String> getOrder() {
            return order;
        }

        public void setOrder(List<String> order) {
            this.order = order;
        }

        public static class SongBean {
            /**
             * moreText : 查看更多同名歌曲
             * highText : null
             * more : true
             * songs : [{"name":"JoJo","id":27917548,"pst":0,"t":0,"ar":[{"id":48514,"name":"Boz Scaggs","tns":[],"alias":[]}],"alia":[],"pop":90,"st":0,"rt":"","fee":8,"v":6,"crbt":null,"cf":"","al":{"id":2694934,"name":"The Essential Boz Scaggs","picUrl":"http://p2.music.126.net/_YmTuIMoG0dh2OJSOE-LPw==/17829680556434243.jpg","tns":[],"pic_str":"17829680556434243","pic":17829680556434244},"dt":354746,"h":{"br":320000,"fid":0,"size":14220536,"vd":-2.65076E-4},"m":{"br":160000,"fid":0,"size":7123590,"vd":0.0324002},"l":{"br":96000,"fid":0,"size":4284811,"vd":-2.65076E-4},"a":null,"cd":"2","no":1,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":1,"s_id":0,"mark":0,"rtype":0,"rurl":null,"mst":9,"cp":7001,"mv":0,"publishTime":1382976000007,"officialTags":[],"privilege":{"id":27917548,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":128000,"toast":false,"flag":256},"alg":"alg_song_basic_d"},{"name":"Save My Soul","id":34072632,"pst":0,"t":0,"ar":[{"id":60943,"name":"JoJo","tns":[],"alias":[]}],"alia":[],"pop":80,"st":0,"rt":null,"fee":8,"v":8,"crbt":null,"cf":"","al":{"id":3270985,"name":"III.","picUrl":"http://p2.music.126.net/wOsQ_2u6XrhESTsiN3KirQ==/7974757838028840.jpg","tns":[],"pic":7974757838028840},"dt":224908,"h":{"br":320000,"fid":0,"size":8996614,"vd":-1.67},"m":{"br":160000,"fid":0,"size":4498329,"vd":-1.25},"l":{"br":96000,"fid":0,"size":2699015,"vd":-1.31},"a":null,"cd":"1","no":2,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":0,"s_id":0,"mark":0,"rtype":0,"rurl":null,"mst":9,"cp":7002,"mv":5269161,"publishTime":1440086400007,"officialTags":[],"privilege":{"id":34072632,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":256},"alg":"alg_song_basic_d"},{"name":"JOJO","id":484324172,"pst":0,"t":0,"ar":[{"id":********,"name":"西藏病人","tns":[],"alias":[]}],"alia":[],"pop":85,"st":0,"rt":null,"fee":0,"v":62,"crbt":null,"cf":"","al":{"id":35355954,"name":"转经道上的屠夫","picUrl":"http://p2.music.126.net/Swy59brVeGMJE3a0FKuCKg==/109951162925144969.jpg","tns":[],"pic_str":"109951162925144969","pic":109951162925144980},"dt":132999,"h":{"br":320000,"fid":0,"size":5322754,"vd":0},"m":{"br":192000,"fid":0,"size":3193670,"vd":0},"l":{"br":128000,"fid":0,"size":2129128,"vd":0},"a":null,"cd":"1","no":7,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":2,"s_id":0,"mark":64,"rtype":0,"rurl":null,"mst":9,"cp":0,"mv":0,"publishTime":1491921682146,"officialTags":[],"privilege":{"id":484324172,"fee":0,"payed":0,"st":0,"pl":999000,"dl":999000,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":999000,"fl":999000,"toast":false,"flag":130},"alg":"alg_song_basic_d"},{"name":"Cry On My Shoulder","id":17194024,"pst":0,"t":0,"ar":[{"id":152734,"name":"Deutschland sucht den Superstar","tns":[],"alias":[]},{"id":104700,"name":"Various Artists","tns":[],"alias":["欧美群星"],"alia":["欧美群星"]}],"alia":[],"pop":100,"st":0,"rt":"","fee":8,"v":21,"crbt":null,"cf":"","al":{"id":1584599,"name":"United","picUrl":"http://p2.music.126.net/_1SSamf87l4mo77TZiWCWQ==/576144092962639.jpg","tns":[],"pic":576144092962639},"dt":236000,"h":{"br":320000,"fid":0,"size":9453358,"vd":-24700},"m":{"br":192000,"fid":0,"size":5672081,"vd":-22000},"l":{"br":128000,"fid":0,"size":3781442,"vd":-20500},"a":null,"cd":"1","no":3,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":1,"s_id":0,"mark":0,"rtype":0,"rurl":null,"mst":9,"cp":7002,"mv":0,"publishTime":1044806400000,"officialTags":[],"privilege":{"id":17194024,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":256},"alg":"alg_song_basic_d"},{"name":"Vibe.","id":*********,"pst":0,"t":0,"ar":[{"id":60943,"name":"JoJo","tns":[],"alias":[]}],"alia":[],"pop":90,"st":0,"rt":null,"fee":8,"v":52,"crbt":null,"cf":"","al":{"id":34875732,"name":"Mad Love. (Deluxe)","picUrl":"http://p2.music.126.net/c9ebY8Y_-CDds08sq1DNfQ==/109951163276011542.jpg","tns":[],"pic_str":"109951163276011542","pic":109951163276011540},"dt":187480,"h":{"br":320000,"fid":0,"size":7500321,"vd":-1.0E-4},"m":{"br":192000,"fid":0,"size":4500210,"vd":-1.0E-4},"l":{"br":128000,"fid":0,"size":3000155,"vd":-1.0E-4},"a":null,"cd":"1","no":6,"rtUrl":null,"ftype":0,"rtUrls":[],"djId":0,"copyright":2,"s_id":0,"mark":8192,"rtype":0,"rurl":null,"mst":9,"cp":7002,"mv":0,"publishTime":*************,"officialTags":[],"privilege":{"id":*********,"fee":8,"payed":0,"st":0,"pl":128000,"dl":0,"sp":7,"cp":1,"subp":1,"cs":false,"maxbr":320000,"fl":128000,"toast":false,"flag":260},"alg":"alg_song_basic_d"}]
             */

            private String moreText;
            private Object highText;
            private boolean more;
            private List<SongDetailBean.SongsBean> songs;

            public String getMoreText() {
                return moreText;
            }

            public void setMoreText(String moreText) {
                this.moreText = moreText;
            }

            public Object getHighText() {
                return highText;
            }

            public void setHighText(Object highText) {
                this.highText = highText;
            }

            public boolean isMore() {
                return more;
            }

            public void setMore(boolean more) {
                this.more = more;
            }

            public void setSongs(List<SongDetailBean.SongsBean> songs) {
                this.songs = songs;
            }

            public List<SongDetailBean.SongsBean> getSongs() {
                return songs;
            }
        }

        public static class DjRadioBean {
            /**
             * moreText : 查看全部
             * djRadios : [{"id":*********,"dj":{"defaultAvatar":false,"province":130000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/PO8TsjfLTdbG-igdGFSBLw==/109951163602792360.jpg","accountStatus":0,"gender":1,"city":130200,"birthday":************,"userId":********,"userType":0,"nickname":"郭-小嘉","signature":"名士文可公","description":"","detailDescription":"","avatarImgId":109951163602792350,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951163602792360","backgroundImgIdStr":"****************","avatarImgId_str":"109951163602792360"},"name":"JOJO的奇妙冒险·天堂之眼bgm集","picUrl":"http://p2.music.126.net/1clU9O807xMbCTKHUarG9w==/109951163120085182.jpg","desc":"因为抽风和补档，顺序可能有点问题大家见谅","subCount":1033,"programCount":57,"createTime":*************,"categoryId":3001,"category":"二次元","radioFeeType":0,"feeScope":0,"buyed":false,"videos":null,"finished":false,"underShelf":false,"purchaseCount":0,"price":0,"originalPrice":0,"discountPrice":null,"lastProgramCreateTime":1551188233994,"lastProgramName":"EOH·角色选择","lastProgramId":2059743782,"picId":109951163120085180,"rcmdText":null,"composeVideo":false,"shareCount":0,"rcmdtext":null,"likedCount":0,"alg":"alg_djradio_basic_d","commentCount":0}]
             * more : true
             */

            private String moreText;
            private boolean more;
            private List<DjRadiosBean> djRadios;

            public String getMoreText() {
                return moreText;
            }

            public void setMoreText(String moreText) {
                this.moreText = moreText;
            }

            public boolean isMore() {
                return more;
            }

            public void setMore(boolean more) {
                this.more = more;
            }

            public List<DjRadiosBean> getDjRadios() {
                return djRadios;
            }

            public void setDjRadios(List<DjRadiosBean> djRadios) {
                this.djRadios = djRadios;
            }

            public static class DjRadiosBean {
                /**
                 * id : *********
                 * dj : {"defaultAvatar":false,"province":130000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/PO8TsjfLTdbG-igdGFSBLw==/109951163602792360.jpg","accountStatus":0,"gender":1,"city":130200,"birthday":************,"userId":********,"userType":0,"nickname":"郭-小嘉","signature":"名士文可公","description":"","detailDescription":"","avatarImgId":109951163602792350,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"109951163602792360","backgroundImgIdStr":"****************","avatarImgId_str":"109951163602792360"}
                 * name : JOJO的奇妙冒险·天堂之眼bgm集
                 * picUrl : http://p2.music.126.net/1clU9O807xMbCTKHUarG9w==/109951163120085182.jpg
                 * desc : 因为抽风和补档，顺序可能有点问题大家见谅
                 * subCount : 1033
                 * programCount : 57
                 * createTime : *************
                 * categoryId : 3001
                 * category : 二次元
                 * radioFeeType : 0
                 * feeScope : 0
                 * buyed : false
                 * videos : null
                 * finished : false
                 * underShelf : false
                 * purchaseCount : 0
                 * price : 0
                 * originalPrice : 0
                 * discountPrice : null
                 * lastProgramCreateTime : 1551188233994
                 * lastProgramName : EOH·角色选择
                 * lastProgramId : 2059743782
                 * picId : 109951163120085180
                 * rcmdText : null
                 * composeVideo : false
                 * shareCount : 0
                 * rcmdtext : null
                 * likedCount : 0
                 * alg : alg_djradio_basic_d
                 * commentCount : 0
                 */

                private long id;
                private DjBean dj;
                private String name;
                private String picUrl;
                private String desc;
                private int subCount;
                private int programCount;
                private long createTime;
                private int categoryId;
                private String category;
                private int radioFeeType;
                private int feeScope;
                private boolean buyed;
                private Object videos;
                private boolean finished;
                private boolean underShelf;
                private int purchaseCount;
                private int price;
                private int originalPrice;
                private Object discountPrice;
                private long lastProgramCreateTime;
                private String lastProgramName;
                private int lastProgramId;
                private long picId;
                private Object rcmdText;
                private boolean composeVideo;
                private int shareCount;
                private Object rcmdtext;
                private int likedCount;
                private String alg;
                private int commentCount;

                public long getId() {
                    return id;
                }

                public void setId(long id) {
                    this.id = id;
                }

                public DjBean getDj() {
                    return dj;
                }

                public void setDj(DjBean dj) {
                    this.dj = dj;
                }

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public String getPicUrl() {
                    return picUrl;
                }

                public void setPicUrl(String picUrl) {
                    this.picUrl = picUrl;
                }

                public String getDesc() {
                    return desc;
                }

                public void setDesc(String desc) {
                    this.desc = desc;
                }

                public int getSubCount() {
                    return subCount;
                }

                public void setSubCount(int subCount) {
                    this.subCount = subCount;
                }

                public int getProgramCount() {
                    return programCount;
                }

                public void setProgramCount(int programCount) {
                    this.programCount = programCount;
                }

                public long getCreateTime() {
                    return createTime;
                }

                public void setCreateTime(long createTime) {
                    this.createTime = createTime;
                }

                public int getCategoryId() {
                    return categoryId;
                }

                public void setCategoryId(int categoryId) {
                    this.categoryId = categoryId;
                }

                public String getCategory() {
                    return category;
                }

                public void setCategory(String category) {
                    this.category = category;
                }

                public int getRadioFeeType() {
                    return radioFeeType;
                }

                public void setRadioFeeType(int radioFeeType) {
                    this.radioFeeType = radioFeeType;
                }

                public int getFeeScope() {
                    return feeScope;
                }

                public void setFeeScope(int feeScope) {
                    this.feeScope = feeScope;
                }

                public boolean isBuyed() {
                    return buyed;
                }

                public void setBuyed(boolean buyed) {
                    this.buyed = buyed;
                }

                public Object getVideos() {
                    return videos;
                }

                public void setVideos(Object videos) {
                    this.videos = videos;
                }

                public boolean isFinished() {
                    return finished;
                }

                public void setFinished(boolean finished) {
                    this.finished = finished;
                }

                public boolean isUnderShelf() {
                    return underShelf;
                }

                public void setUnderShelf(boolean underShelf) {
                    this.underShelf = underShelf;
                }

                public int getPurchaseCount() {
                    return purchaseCount;
                }

                public void setPurchaseCount(int purchaseCount) {
                    this.purchaseCount = purchaseCount;
                }

                public int getPrice() {
                    return price;
                }

                public void setPrice(int price) {
                    this.price = price;
                }

                public int getOriginalPrice() {
                    return originalPrice;
                }

                public void setOriginalPrice(int originalPrice) {
                    this.originalPrice = originalPrice;
                }

                public Object getDiscountPrice() {
                    return discountPrice;
                }

                public void setDiscountPrice(Object discountPrice) {
                    this.discountPrice = discountPrice;
                }

                public long getLastProgramCreateTime() {
                    return lastProgramCreateTime;
                }

                public void setLastProgramCreateTime(long lastProgramCreateTime) {
                    this.lastProgramCreateTime = lastProgramCreateTime;
                }

                public String getLastProgramName() {
                    return lastProgramName;
                }

                public void setLastProgramName(String lastProgramName) {
                    this.lastProgramName = lastProgramName;
                }

                public int getLastProgramId() {
                    return lastProgramId;
                }

                public void setLastProgramId(int lastProgramId) {
                    this.lastProgramId = lastProgramId;
                }

                public long getPicId() {
                    return picId;
                }

                public void setPicId(long picId) {
                    this.picId = picId;
                }

                public Object getRcmdText() {
                    return rcmdText;
                }

                public void setRcmdText(Object rcmdText) {
                    this.rcmdText = rcmdText;
                }

                public boolean isComposeVideo() {
                    return composeVideo;
                }

                public void setComposeVideo(boolean composeVideo) {
                    this.composeVideo = composeVideo;
                }

                public int getShareCount() {
                    return shareCount;
                }

                public void setShareCount(int shareCount) {
                    this.shareCount = shareCount;
                }

                public Object getRcmdtext() {
                    return rcmdtext;
                }

                public void setRcmdtext(Object rcmdtext) {
                    this.rcmdtext = rcmdtext;
                }

                public int getLikedCount() {
                    return likedCount;
                }

                public void setLikedCount(int likedCount) {
                    this.likedCount = likedCount;
                }

                public String getAlg() {
                    return alg;
                }

                public void setAlg(String alg) {
                    this.alg = alg;
                }

                public int getCommentCount() {
                    return commentCount;
                }

                public void setCommentCount(int commentCount) {
                    this.commentCount = commentCount;
                }

                public static class DjBean {
                    /**
                     * defaultAvatar : false
                     * province : 130000
                     * authStatus : 0
                     * followed : false
                     * avatarUrl : http://p1.music.126.net/PO8TsjfLTdbG-igdGFSBLw==/109951163602792360.jpg
                     * accountStatus : 0
                     * gender : 1
                     * city : 130200
                     * birthday : ************
                     * userId : ********
                     * userType : 0
                     * nickname : 郭-小嘉
                     * signature : 名士文可公
                     * description :
                     * detailDescription :
                     * avatarImgId : 109951163602792350
                     * backgroundImgId : ****************
                     * backgroundUrl : http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg
                     * authority : 0
                     * mutual : false
                     * expertTags : null
                     * experts : null
                     * djStatus : 10
                     * vipType : 0
                     * remarkName : null
                     * avatarImgIdStr : 109951163602792360
                     * backgroundImgIdStr : ****************
                     * avatarImgId_str : 109951163602792360
                     */

                    private boolean defaultAvatar;
                    private int province;
                    private int authStatus;
                    private boolean followed;
                    private String avatarUrl;
                    private int accountStatus;
                    private int gender;
                    private int city;
                    private long birthday;
                    private String userId;
                    private int userType;
                    private String nickname;
                    private String signature;
                    private String description;
                    private String detailDescription;
                    private long avatarImgId;
                    private long backgroundImgId;
                    private String backgroundUrl;
                    private int authority;
                    private boolean mutual;
                    private Object expertTags;
                    private Object experts;
                    private int djStatus;
                    private int vipType;
                    private Object remarkName;
                    private String avatarImgIdStr;
                    private String backgroundImgIdStr;
                    private String avatarImgId_str;

                    public boolean isDefaultAvatar() {
                        return defaultAvatar;
                    }

                    public void setDefaultAvatar(boolean defaultAvatar) {
                        this.defaultAvatar = defaultAvatar;
                    }

                    public int getProvince() {
                        return province;
                    }

                    public void setProvince(int province) {
                        this.province = province;
                    }

                    public int getAuthStatus() {
                        return authStatus;
                    }

                    public void setAuthStatus(int authStatus) {
                        this.authStatus = authStatus;
                    }

                    public boolean isFollowed() {
                        return followed;
                    }

                    public void setFollowed(boolean followed) {
                        this.followed = followed;
                    }

                    public String getAvatarUrl() {
                        return avatarUrl;
                    }

                    public void setAvatarUrl(String avatarUrl) {
                        this.avatarUrl = avatarUrl;
                    }

                    public int getAccountStatus() {
                        return accountStatus;
                    }

                    public void setAccountStatus(int accountStatus) {
                        this.accountStatus = accountStatus;
                    }

                    public int getGender() {
                        return gender;
                    }

                    public void setGender(int gender) {
                        this.gender = gender;
                    }

                    public int getCity() {
                        return city;
                    }

                    public void setCity(int city) {
                        this.city = city;
                    }

                    public long getBirthday() {
                        return birthday;
                    }

                    public void setBirthday(long birthday) {
                        this.birthday = birthday;
                    }

                    public String getUserId() {
                        return userId;
                    }

                    public void setUserId(String userId) {
                        this.userId = userId;
                    }

                    public int getUserType() {
                        return userType;
                    }

                    public void setUserType(int userType) {
                        this.userType = userType;
                    }

                    public String getNickname() {
                        return nickname;
                    }

                    public void setNickname(String nickname) {
                        this.nickname = nickname;
                    }

                    public String getSignature() {
                        return signature;
                    }

                    public void setSignature(String signature) {
                        this.signature = signature;
                    }

                    public String getDescription() {
                        return description;
                    }

                    public void setDescription(String description) {
                        this.description = description;
                    }

                    public String getDetailDescription() {
                        return detailDescription;
                    }

                    public void setDetailDescription(String detailDescription) {
                        this.detailDescription = detailDescription;
                    }

                    public long getAvatarImgId() {
                        return avatarImgId;
                    }

                    public void setAvatarImgId(long avatarImgId) {
                        this.avatarImgId = avatarImgId;
                    }

                    public long getBackgroundImgId() {
                        return backgroundImgId;
                    }

                    public void setBackgroundImgId(long backgroundImgId) {
                        this.backgroundImgId = backgroundImgId;
                    }

                    public String getBackgroundUrl() {
                        return backgroundUrl;
                    }

                    public void setBackgroundUrl(String backgroundUrl) {
                        this.backgroundUrl = backgroundUrl;
                    }

                    public int getAuthority() {
                        return authority;
                    }

                    public void setAuthority(int authority) {
                        this.authority = authority;
                    }

                    public boolean isMutual() {
                        return mutual;
                    }

                    public void setMutual(boolean mutual) {
                        this.mutual = mutual;
                    }

                    public Object getExpertTags() {
                        return expertTags;
                    }

                    public void setExpertTags(Object expertTags) {
                        this.expertTags = expertTags;
                    }

                    public Object getExperts() {
                        return experts;
                    }

                    public void setExperts(Object experts) {
                        this.experts = experts;
                    }

                    public int getDjStatus() {
                        return djStatus;
                    }

                    public void setDjStatus(int djStatus) {
                        this.djStatus = djStatus;
                    }

                    public int getVipType() {
                        return vipType;
                    }

                    public void setVipType(int vipType) {
                        this.vipType = vipType;
                    }

                    public Object getRemarkName() {
                        return remarkName;
                    }

                    public void setRemarkName(Object remarkName) {
                        this.remarkName = remarkName;
                    }

                    public String getAvatarImgIdStr() {
                        return avatarImgIdStr;
                    }

                    public void setAvatarImgIdStr(String avatarImgIdStr) {
                        this.avatarImgIdStr = avatarImgIdStr;
                    }

                    public String getBackgroundImgIdStr() {
                        return backgroundImgIdStr;
                    }

                    public void setBackgroundImgIdStr(String backgroundImgIdStr) {
                        this.backgroundImgIdStr = backgroundImgIdStr;
                    }

                    public String getAvatarImgId_str() {
                        return avatarImgId_str;
                    }

                    public void setAvatarImgId_str(String avatarImgId_str) {
                        this.avatarImgId_str = avatarImgId_str;
                    }
                }
            }
        }

        public static class ArtistBean {
            /**
             * artists : [{"id":48514,"name":"Boz Scaggs","picUrl":"http://p2.music.126.net/DHo_dBKUU5uRpBe1FeWEHw==/***************.jpg","alias":[],"albumSize":29,"picId":***************,"img1v1Url":"http://p2.music.126.net/6e8fyO1MMhcG7YYYsMOxsA==/***************.jpg","img1v1":***************,"mvSize":0,"followed":false,"alg":"alg_artist_basic_d","trans":null},{"id":60943,"name":"JoJo","picUrl":"http://p2.music.126.net/sNBvINATXmjEFXQ9wS92sg==/***************.jpg","alias":[],"albumSize":44,"picId":***************,"img1v1Url":"http://p2.music.126.net/64DFPu_th-OsgDUm9PdsDw==/***************.jpg","img1v1":***************,"mvSize":12,"followed":false,"alg":"alg_artist_basic_d","trans":null},{"id":********,"name":"西藏病人","picUrl":"http://p2.music.126.net/wkPui_S4APuwza5NjULrJw==/109951162869491151.jpg","alias":[],"albumSize":3,"picId":109951162869491150,"img1v1Url":"http://p2.music.126.net/XzrAkQKMYFLT-neYnsqu1A==/109951162869490290.jpg","accountId":*********,"img1v1":109951162869490290,"mvSize":0,"followed":false,"alg":"alg_artist_basic_d","trans":null}]
             * more : false
             */

            private boolean more;
            private List<ArtistsBean> artists;

            public boolean isMore() {
                return more;
            }

            public void setMore(boolean more) {
                this.more = more;
            }

            public List<ArtistsBean> getArtists() {
                return artists;
            }

            public void setArtists(List<ArtistsBean> artists) {
                this.artists = artists;
            }

            public static class ArtistsBean {
                /**
                 * id : 48514
                 * name : Boz Scaggs
                 * picUrl : http://p2.music.126.net/DHo_dBKUU5uRpBe1FeWEHw==/***************.jpg
                 * alias : []
                 * albumSize : 29
                 * picId : ***************
                 * img1v1Url : http://p2.music.126.net/6e8fyO1MMhcG7YYYsMOxsA==/***************.jpg
                 * img1v1 : ***************
                 * mvSize : 0
                 * followed : false
                 * alg : alg_artist_basic_d
                 * trans : null
                 * accountId : *********
                 */

                private long id;
                private String name;
                private String picUrl;
                private int albumSize;
                private long picId;
                private String img1v1Url;
                private long img1v1;
                private int mvSize;
                private boolean followed;
                private String alg;
                private Object trans;
                private String accountId;
                private List<String> alias;

                public String getId() {
                    return String.valueOf(id);
                }

                public void setId(long id) {
                    this.id = id;
                }

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public String getPicUrl() {
                    return picUrl;
                }

                public void setPicUrl(String picUrl) {
                    this.picUrl = picUrl;
                }

                public int getAlbumSize() {
                    return albumSize;
                }

                public void setAlbumSize(int albumSize) {
                    this.albumSize = albumSize;
                }

                public long getPicId() {
                    return picId;
                }

                public void setPicId(long picId) {
                    this.picId = picId;
                }

                public String getImg1v1Url() {
                    return img1v1Url;
                }

                public void setImg1v1Url(String img1v1Url) {
                    this.img1v1Url = img1v1Url;
                }

                public long getImg1v1() {
                    return img1v1;
                }

                public void setImg1v1(long img1v1) {
                    this.img1v1 = img1v1;
                }

                public int getMvSize() {
                    return mvSize;
                }

                public void setMvSize(int mvSize) {
                    this.mvSize = mvSize;
                }

                public boolean isFollowed() {
                    return followed;
                }

                public void setFollowed(boolean followed) {
                    this.followed = followed;
                }

                public String getAlg() {
                    return alg;
                }

                public void setAlg(String alg) {
                    this.alg = alg;
                }

                public Object getTrans() {
                    return trans;
                }

                public void setTrans(Object trans) {
                    this.trans = trans;
                }

                public String getAccountId() {
                    return accountId;
                }

                public void setAccountId(String accountId) {
                    this.accountId = accountId;
                }

                public List<String> getAlias() {
                    return alias;
                }

                public void setAlias(List<String> alias) {
                    this.alias = alias;
                }
            }
        }

        public static class PlayListBean {
            /**
             * moreText : 查看全部
             * more : true
             * playLists : [{"id":**********,"name":"荒木飞吕彦耳边响起【曲目已整】JOJO音乐梗","coverImgUrl":"http://p2.music.126.net/RhF1Of6EnzjIMEWoMpz81g==/109951164272965937.jpg","creator":{"nickname":"大家来看我JOJO立","userId":*********,"userType":300,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":170,"userId":*********,"playCount":343449,"bookCount":6138,"description":"★歌曲评论区勿刷JO梗\n1个人私心致大乔\n幻影之血\n8JOJO来源\n9艾琳娜\n10迪奥\n11齐贝林\n12SPW\n战斗潮流\n13丝吉Q\n14丽萨丽萨\n15艾哲红石\n16桑塔纳\n17瓦乌姆\n18艾西迪西\n19卡兹\n星辰斗士\n2021空条承太郎（误\n22迪奥\n23阿布德尔\n24波鲁那雷夫\n25伊奇\n26死神13\n27恩雅\n28亚空瘴气\n29看门鸟\n30恋人\n31欧因波因哥\n32巴斯提\n不灭钻石\n3335东方仗助\n3638吉良吉影\n39吉良吉广\n40安杰罗\n41虹村形兆\n42虹村亿泰\n43康一\n44由花子\n45小林玉美\n46胖重\n47岸边露伴\n48康一的狗\n49东尼欧\n50喷上裕也\n51未起隆\n52静\n53间田敏和\n54钢田一丰大\n55音石明\n56乙雅三\n57猫草\n黄金之风\n5859乔鲁诺乔巴拿\n6061迪亚波罗\n62多比欧\n63布加拉提\n64阿帕基\n65米斯达\n66福葛\n67纳兰迦\n68特里休\n69乌龟\n70银镇\n71卢卡\n72波尔波\n73滚石\n74镜中人\n75手艺工作\n76小脚\n77壮烈成仁\n78沙滩男孩\n79娃娃脸\n80金属制品\n81面部特写\n82冲击\n8384BIG\n85青春岁月\n86绿洲\n石之海\n8788空条徐伦\n89绿婴\n9092普奇神父\n93绿海豚街\n94安波里欧\n9596安娜苏\n97艾梅斯\n98天气预报\n99FF\n100奎丝\n101地狱高速公路\n102盎格鲁\n103跳跃闪电\n104行星波动\n105恶魔枷锁\n106107章节名\n飙马野郎\n108110乔尼乔斯达\n111114法尼瓦伦泰\n115117迪亚哥\n118119杰洛齐贝林\n120122曼登提姆\n123124波克洛克\n125128HP\n129林果\n130131砂男\n132133罗宾逊\n134135芬芳家族\n136137高乔\n138奥耶格摩巴\n139波特派哈特\n140雨男\n141甜糖山\n142十一人众\n143南北战争\n144金属气球\n145巧克力迪斯科\n146147威卡毕博\n148149马金特\n福音\n150东方定助\n151广濑康穗\n152吉良京\n153东方宪助\n154东方常敏\n155东方大弥\n156东方剑\n157卡蕾拉\n158笹目樱二郎\n159安定街\n160八木山夜露\n161大年寺山爱唱\n162费克斯兄弟\n163田最环\n164奇迹之人\n165多洛米蒂\n166豆铣礼\n167168城市游击队\n169170穷汤姆","highQuality":false,"track":{"name":"Time To Say Goodbye","id":3405842,"position":4,"alias":[],"status":0,"fee":8,"copyrightId":14002,"disc":"1","no":4,"artists":[{"name":"Sarah Brightman","id":74618,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"The Very Best of Sarah Brightman 1990-2000","id":345234,"type":"专辑","size":16,"picId":789449348785243,"blurPicUrl":"http://p2.music.126.net/GyywegDyasFj_KAgps7VIg==/789449348785243.jpg","companyId":0,"pic":789449348785243,"picUrl":"http://p2.music.126.net/GyywegDyasFj_KAgps7VIg==/789449348785243.jpg","publishTime":991584000007,"description":"","tags":"","company":"Atlantic UK","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":1,"copyrightId":5003,"commentThreadId":"R_AL_3_345234","artists":[{"name":"Sarah Brightman","id":74618,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":244689,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"600902000002230987","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_3405842","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"mvid":0,"bMusic":{"name":"Time To Say Goodbye","id":21988403,"size":2949479,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":244689,"volumeDelta":-1.67},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":"Time To Say Goodbye","id":21988401,"size":9794690,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":244689,"volumeDelta":-2.09},"mMusic":{"name":"Time To Say Goodbye","id":21988402,"size":4905528,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":244689,"volumeDelta":-1.66},"lMusic":{"name":"Time To Say Goodbye","id":21988403,"size":2949479,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":244689,"volumeDelta":-1.67}},"alg":null},{"id":2008543401,"name":"JOJO 黄金之风替身名岀处","coverImgUrl":"http://p2.music.126.net/YyZOneUXnfqjobDE1-2fhQ==/109951163629092105.jpg","creator":{"nickname":"Loser-Jerry","userId":537494168,"userType":0,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":46,"userId":537494168,"playCount":97934,"bookCount":972,"description":"翻译可能有不同\n黄金体验 -《The Gold Experience》(专辑)____Prince\n\n钢链手指-《Sticky Finger》(专辑）___The Rolling Stones\n黑色安息日-《Black Sabbath》Black Sabbath\n忧伤蓝调-The Moody Blues\n柔软机器-Soft Machine\n性感手枪-Sex Pistol (四个人的乐队）\n手艺工作-KraftWerk\n小脚-Little Feet\n史密斯飞船-Aerosmith\n镜中人-Man in the Mirror__Michael Jackson\n紫烟-Purple Haze__Jimi Hendrix\n沙滩男孩-Beaches Boy\n","highQuality":false,"track":{"name":"裏切り者のレクイエム","id":1352199795,"position":0,"alias":["TV动画《JOJO的奇妙冒险 黄金之风》OP2"],"status":0,"fee":0,"copyrightId":663018,"disc":"01","no":1,"artists":[{"name":"ハセガワダイスケ","id":1025284,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"裏切り者のレクイエム","id":75859706,"type":"EP/Single","size":3,"picId":109951163930146740,"blurPicUrl":"http://p2.music.126.net/vH_S4MfzS5S6ISr59aJ5rA==/109951163930146742.jpg","companyId":0,"pic":109951163930146740,"picUrl":"http://p2.music.126.net/vH_S4MfzS5S6ISr59aJ5rA==/109951163930146742.jpg","publishTime":1552665600000,"description":"","tags":"","company":"Warner Bros. Entertainment","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":0,"copyrightId":663018,"commentThreadId":"R_AL_3_75859706","artists":[{"name":"ハセガワダイスケ","id":1025284,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"picId_str":"109951163930146742"},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":210592,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_1352199795","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":0,"mvid":0,"bMusic":{"name":null,"id":3739648316,"size":3370466,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":210592,"volumeDelta":-20500},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":null,"id":3739648314,"size":8426101,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":210592,"volumeDelta":-24600},"mMusic":{"name":null,"id":3739648315,"size":5055678,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":192000,"playTime":210592,"volumeDelta":-22000},"lMusic":{"name":null,"id":3739648316,"size":3370466,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":128000,"playTime":210592,"volumeDelta":-20500}},"alg":null},{"id":784872872,"name":"JoJo的奇妙音乐梗","coverImgUrl":"http://p2.music.126.net/bSoR7k-xMx5HjxiFK-NJzw==/19021551160741329.jpg","creator":{"nickname":"JoJo的奇喵猫险","userId":124196736,"userType":0,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":39,"userId":124196736,"playCount":1038322,"bookCount":22801,"description":"荒木老师主业漫画家，副业服装设计，兴趣爱好分享音乐wryyyyy\n1.来吧！普奇神父！ 2.茸茸的黄金体验\n3.歌名是D4C- 歌手是柱之男艾斯迪斯\n4..一二部ed 5.三部ed 6.四部ed\n7.大弥的加州大床\n8-10.吉良吉影的杀皇、枯萎穿心、败者吃尘\n11.盎格鲁的波西米亚狂想曲\n12.神父的天堂zhi造\n13.七页無駄哥的青春岁月 14.赛可的绿洲\n15.音石明的辛红辣椒 16.dio的骇人恶兽\n17.四蛋的软又湿 18.面具雨男的替身\n19.荒木老师的替身不解释 20.南北战争\n21.艾梅斯的kiss 22.巧克力迪斯可\n23.歌名是曼登提姆的替身 歌手是漆黑意志乔尼\n24.特里休的辣妹 25.加丘的白色相簿 26.五部的乌龟\n27.康穗的佩斯利公园 28.疯狂钻石\n29.(☞゜ω゜)☞：乔乔，我不想再做人了！\n30.被长者反弹死的泪眼卢卡 31.布姐的钢链手指\n32.镜中人 33.老板的罪恶王者 34.石之自由\n35.歌名是咕咕娃娃 36.自残狂魔的替身\n37.骇游天外\n38.歌手是放弃思考的卡兹sama\n39.诸君！我们意大利再见！ヾ(￣▽￣)Bye~Bye~\n\n＝=> To be continued","highQuality":false,"track":{"name":"Jolene","id":2423350,"position":1,"alias":[],"status":0,"fee":8,"copyrightId":7001,"disc":"1","no":1,"artists":[{"name":"Dolly Parton","id":53258,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"Jolene","id":244392,"type":"专辑","size":10,"picId":885106860413997,"blurPicUrl":"http://p2.music.126.net/_i4bJ9blVdcV6fs49mPHoA==/885106860413997.jpg","companyId":0,"pic":885106860413997,"picUrl":"http://p2.music.126.net/_i4bJ9blVdcV6fs49mPHoA==/885106860413997.jpg","publishTime":131990400007,"description":"","tags":"","company":"Buddha","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":1,"copyrightId":7001,"commentThreadId":"R_AL_3_244392","artists":[{"name":"Dolly Parton","id":53258,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]},"starred":false,"popularity":95,"score":95,"starredNum":0,"duration":162482,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":"cc8790a6f3486f49c3d2e0998b6da823","audition":null,"copyFrom":"","commentThreadId":"R_SO_4_2423350","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"mvid":0,"bMusic":{"name":"Jolene","id":16897903,"size":1972381,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":162482,"volumeDelta":0.255276},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":"Jolene","id":16897901,"size":6517896,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":162482,"volumeDelta":-2.65076E-4},"mMusic":{"name":"Jolene","id":16897902,"size":3271398,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":162482,"volumeDelta":0.343626},"lMusic":{"name":"Jolene","id":16897903,"size":1972381,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":162482,"volumeDelta":0.255276}},"alg":null},{"id":2502683,"name":"JOJO角色/替身名称来源","coverImgUrl":"http://p2.music.126.net/QeAepm08QnRuX6vHXLfpjQ==/14454179858758010.jpg","creator":{"nickname":"visiona3r","userId":1268814,"userType":0,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":195,"userId":1268814,"playCount":948421,"bookCount":31844,"description":"一-七部完成！本意是探索荒木老师的音乐品味，因此除了名称来源以外，也加入了漫画人物介绍中他们喜欢的乐队歌曲等。【参考：我爱摇滚乐】","highQuality":false,"track":{"name":"カーニバル","id":28914363,"position":1,"alias":[],"status":0,"fee":0,"copyrightId":663018,"disc":"1","no":1,"artists":[{"name":"otetsu","id":15169,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},{"name":"GUMI","id":188274,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"Carnival","id":2943113,"type":"专辑","size":9,"picId":8928034417591572,"blurPicUrl":"http://p1.music.126.net/Z1YfhRBgdys8b36owCLZcQ==/8928034417591572.jpg","companyId":0,"pic":8928034417591572,"picUrl":"http://p1.music.126.net/Z1YfhRBgdys8b36owCLZcQ==/8928034417591572.jpg","publishTime":1307808000000,"description":"","tags":"","company":"THE VOC@LOiD M@STER 16","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_2943113","artists":[{"name":"otetsu","id":15169,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]},"starred":false,"popularity":65,"score":65,"starredNum":0,"duration":217756,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_28914363","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":2,"mvid":0,"bMusic":{"name":"カーニバル","id":49521872,"size":2614764,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":217756,"volumeDelta":-5.05},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":"カーニバル","id":49521873,"size":6970909,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":256000,"playTime":217756,"volumeDelta":-5.4},"mMusic":{"name":"カーニバル","id":49521871,"size":4357236,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":217756,"volumeDelta":-4.97},"lMusic":{"name":"カーニバル","id":49521872,"size":2614764,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":217756,"volumeDelta":-5.05}},"alg":null},{"id":26809094,"name":"【JOJO】荒木老师推荐十张专辑（70年代）","coverImgUrl":"http://p2.music.126.net/AZEMp4gx7v1m5wiqJeE_kg==/2922501906671039.jpg","creator":{"nickname":"VZZOTL93sh","userId":3764061,"userType":200,"authStatus":0,"expertTags":null,"experts":null},"subscribed":false,"trackCount":115,"userId":3764061,"playCount":419406,"bookCount":19812,"description":"【搬运】（年代久远，不分先后）整理自台湾中文版漫画封面的话。网易好厉害，这10张老碟都有。封面win7自带1分钟鼠绘。","highQuality":false,"track":{"name":"Sing","id":17141614,"position":1,"alias":[],"status":0,"fee":8,"copyrightId":7003,"disc":"1","no":1,"artists":[{"name":"Carpenters","id":89363,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"Now & Then","id":1578695,"type":"专辑","size":15,"picId":595935302262467,"blurPicUrl":"http://p2.music.126.net/vEmRAUfchWx6z4r24MTBsg==/595935302262467.jpg","companyId":0,"pic":595935302262467,"picUrl":"http://p2.music.126.net/vEmRAUfchWx6z4r24MTBsg==/595935302262467.jpg","publishTime":105724800007,"description":"","tags":"","company":"Digital Distribution Estonia","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":3,"copyrightId":7003,"commentThreadId":"R_AL_3_1578695","artists":[{"name":"Carpenters","id":89363,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]},"starred":false,"popularity":75,"score":75,"starredNum":0,"duration":199602,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_17141614","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"mvid":0,"bMusic":{"name":"Sing","id":10650994,"size":2438782,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":199602,"volumeDelta":2.21206},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":"Sing","id":10650992,"size":8022925,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":199602,"volumeDelta":1.9331},"mMusic":{"name":"Sing","id":10650993,"size":4034550,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":199602,"volumeDelta":2.2374},"lMusic":{"name":"Sing","id":10650994,"size":2438782,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":199602,"volumeDelta":2.21206}},"alg":null}]
             */

            private String moreText;
            private boolean more;
            private List<PlayListsBean> playLists;

            public String getMoreText() {
                return moreText;
            }

            public void setMoreText(String moreText) {
                this.moreText = moreText;
            }

            public boolean isMore() {
                return more;
            }

            public void setMore(boolean more) {
                this.more = more;
            }

            public List<PlayListsBean> getPlayLists() {
                return playLists;
            }

            public void setPlayLists(List<PlayListsBean> playLists) {
                this.playLists = playLists;
            }

            public static class PlayListsBean {
                /**
                 * id : **********
                 * name : 荒木飞吕彦耳边响起【曲目已整】JOJO音乐梗
                 * coverImgUrl : http://p2.music.126.net/RhF1Of6EnzjIMEWoMpz81g==/109951164272965937.jpg
                 * creator : {"nickname":"大家来看我JOJO立","userId":*********,"userType":300,"authStatus":0,"expertTags":null,"experts":null}
                 * subscribed : false
                 * trackCount : 170
                 * userId : *********
                 * playCount : 343449
                 * bookCount : 6138
                 * description : ★歌曲评论区勿刷JO梗
                 1个人私心致大乔
                 幻影之血
                 8JOJO来源
                 9艾琳娜
                 10迪奥
                 11齐贝林
                 12SPW
                 战斗潮流
                 13丝吉Q
                 14丽萨丽萨
                 15艾哲红石
                 16桑塔纳
                 17瓦乌姆
                 18艾西迪西
                 19卡兹
                 星辰斗士
                 2021空条承太郎（误
                 22迪奥
                 23阿布德尔
                 24波鲁那雷夫
                 25伊奇
                 26死神13
                 27恩雅
                 28亚空瘴气
                 29看门鸟
                 30恋人
                 31欧因波因哥
                 32巴斯提
                 不灭钻石
                 3335东方仗助
                 3638吉良吉影
                 39吉良吉广
                 40安杰罗
                 41虹村形兆
                 42虹村亿泰
                 43康一
                 44由花子
                 45小林玉美
                 46胖重
                 47岸边露伴
                 48康一的狗
                 49东尼欧
                 50喷上裕也
                 51未起隆
                 52静
                 53间田敏和
                 54钢田一丰大
                 55音石明
                 56乙雅三
                 57猫草
                 黄金之风
                 5859乔鲁诺乔巴拿
                 6061迪亚波罗
                 62多比欧
                 63布加拉提
                 64阿帕基
                 65米斯达
                 66福葛
                 67纳兰迦
                 68特里休
                 69乌龟
                 70银镇
                 71卢卡
                 72波尔波
                 73滚石
                 74镜中人
                 75手艺工作
                 76小脚
                 77壮烈成仁
                 78沙滩男孩
                 79娃娃脸
                 80金属制品
                 81面部特写
                 82冲击
                 8384BIG
                 85青春岁月
                 86绿洲
                 石之海
                 8788空条徐伦
                 89绿婴
                 9092普奇神父
                 93绿海豚街
                 94安波里欧
                 9596安娜苏
                 97艾梅斯
                 98天气预报
                 99FF
                 100奎丝
                 101地狱高速公路
                 102盎格鲁
                 103跳跃闪电
                 104行星波动
                 105恶魔枷锁
                 106107章节名
                 飙马野郎
                 108110乔尼乔斯达
                 111114法尼瓦伦泰
                 115117迪亚哥
                 118119杰洛齐贝林
                 120122曼登提姆
                 123124波克洛克
                 125128HP
                 129林果
                 130131砂男
                 132133罗宾逊
                 134135芬芳家族
                 136137高乔
                 138奥耶格摩巴
                 139波特派哈特
                 140雨男
                 141甜糖山
                 142十一人众
                 143南北战争
                 144金属气球
                 145巧克力迪斯科
                 146147威卡毕博
                 148149马金特
                 福音
                 150东方定助
                 151广濑康穗
                 152吉良京
                 153东方宪助
                 154东方常敏
                 155东方大弥
                 156东方剑
                 157卡蕾拉
                 158笹目樱二郎
                 159安定街
                 160八木山夜露
                 161大年寺山爱唱
                 162费克斯兄弟
                 163田最环
                 164奇迹之人
                 165多洛米蒂
                 166豆铣礼
                 167168城市游击队
                 169170穷汤姆
                 * highQuality : false
                 * track : {"name":"Time To Say Goodbye","id":3405842,"position":4,"alias":[],"status":0,"fee":8,"copyrightId":14002,"disc":"1","no":4,"artists":[{"name":"Sarah Brightman","id":74618,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"The Very Best of Sarah Brightman 1990-2000","id":345234,"type":"专辑","size":16,"picId":789449348785243,"blurPicUrl":"http://p2.music.126.net/GyywegDyasFj_KAgps7VIg==/789449348785243.jpg","companyId":0,"pic":789449348785243,"picUrl":"http://p2.music.126.net/GyywegDyasFj_KAgps7VIg==/789449348785243.jpg","publishTime":991584000007,"description":"","tags":"","company":"Atlantic UK","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":1,"copyrightId":5003,"commentThreadId":"R_AL_3_345234","artists":[{"name":"Sarah Brightman","id":74618,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]},"starred":false,"popularity":100,"score":100,"starredNum":0,"duration":244689,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":"600902000002230987","crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_3405842","rtUrl":null,"ftype":0,"rtUrls":[],"copyright":1,"mvid":0,"bMusic":{"name":"Time To Say Goodbye","id":21988403,"size":2949479,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":244689,"volumeDelta":-1.67},"mp3Url":null,"rtype":0,"rurl":null,"hMusic":{"name":"Time To Say Goodbye","id":21988401,"size":9794690,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":244689,"volumeDelta":-2.09},"mMusic":{"name":"Time To Say Goodbye","id":21988402,"size":4905528,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":244689,"volumeDelta":-1.66},"lMusic":{"name":"Time To Say Goodbye","id":21988403,"size":2949479,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":244689,"volumeDelta":-1.67}}
                 * alg : null
                 */

                private long id;
                private String name;
                private String coverImgUrl;
                private CreatorBean creator;
                private boolean subscribed;
                private int trackCount;
                private String userId;
                private int playCount;
                private int bookCount;
                private String description;
                private Object highQuality;
                private TrackBean track;
                private Object alg;

                public long getId() {
                    return id;
                }

                public void setId(long id) {
                    this.id = id;
                }

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public String getCoverImgUrl() {
                    return coverImgUrl;
                }

                public void setCoverImgUrl(String coverImgUrl) {
                    this.coverImgUrl = coverImgUrl;
                }

                public CreatorBean getCreator() {
                    return creator;
                }

                public void setCreator(CreatorBean creator) {
                    this.creator = creator;
                }

                public boolean isSubscribed() {
                    return subscribed;
                }

                public void setSubscribed(boolean subscribed) {
                    this.subscribed = subscribed;
                }

                public int getTrackCount() {
                    return trackCount;
                }

                public void setTrackCount(int trackCount) {
                    this.trackCount = trackCount;
                }

                public String getUserId() {
                    return userId;
                }

                public void setUserId(String userId) {
                    this.userId = userId;
                }

                public int getPlayCount() {
                    return playCount;
                }

                public void setPlayCount(int playCount) {
                    this.playCount = playCount;
                }

                public int getBookCount() {
                    return bookCount;
                }

                public void setBookCount(int bookCount) {
                    this.bookCount = bookCount;
                }

                public String getDescription() {
                    return description;
                }

                public void setDescription(String description) {
                    this.description = description;
                }


                public void setHighQuality(boolean highQuality) {
                    this.highQuality = highQuality;
                }

                public TrackBean getTrack() {
                    return track;
                }

                public void setTrack(TrackBean track) {
                    this.track = track;
                }

                public Object getAlg() {
                    return alg;
                }

                public void setAlg(Object alg) {
                    this.alg = alg;
                }

                public static class CreatorBean {
                    /**
                     * nickname : 大家来看我JOJO立
                     * userId : *********
                     * userType : 300
                     * authStatus : 0
                     * expertTags : null
                     * experts : null
                     */

                    private String nickname;
                    private long userId;
                    private int userType;
                    private int authStatus;
                    private Object expertTags;
                    private Object experts;

                    public String getNickname() {
                        return nickname;
                    }

                    public void setNickname(String nickname) {
                        this.nickname = nickname;
                    }

                    public long getUserId() {
                        return userId;
                    }

                    public void setUserId(long userId) {
                        this.userId = userId;
                    }

                    public int getUserType() {
                        return userType;
                    }

                    public void setUserType(int userType) {
                        this.userType = userType;
                    }

                    public int getAuthStatus() {
                        return authStatus;
                    }

                    public void setAuthStatus(int authStatus) {
                        this.authStatus = authStatus;
                    }

                    public Object getExpertTags() {
                        return expertTags;
                    }

                    public void setExpertTags(Object expertTags) {
                        this.expertTags = expertTags;
                    }

                    public Object getExperts() {
                        return experts;
                    }

                    public void setExperts(Object experts) {
                        this.experts = experts;
                    }
                }

                public static class TrackBean {
                    /**
                     * name : Time To Say Goodbye
                     * id : 3405842
                     * position : 4
                     * alias : []
                     * status : 0
                     * fee : 8
                     * copyrightId : 14002
                     * disc : 1
                     * no : 4
                     * artists : [{"name":"Sarah Brightman","id":74618,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]
                     * album : {"name":"The Very Best of Sarah Brightman 1990-2000","id":345234,"type":"专辑","size":16,"picId":789449348785243,"blurPicUrl":"http://p2.music.126.net/GyywegDyasFj_KAgps7VIg==/789449348785243.jpg","companyId":0,"pic":789449348785243,"picUrl":"http://p2.music.126.net/GyywegDyasFj_KAgps7VIg==/789449348785243.jpg","publishTime":991584000007,"description":"","tags":"","company":"Atlantic UK","briefDesc":"","artist":{"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":1,"copyrightId":5003,"commentThreadId":"R_AL_3_345234","artists":[{"name":"Sarah Brightman","id":74618,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]}
                     * starred : false
                     * popularity : 100
                     * score : 100
                     * starredNum : 0
                     * duration : 244689
                     * playedNum : 0
                     * dayPlays : 0
                     * hearTime : 0
                     * ringtone : 600902000002230987
                     * crbt : null
                     * audition : null
                     * copyFrom :
                     * commentThreadId : R_SO_4_3405842
                     * rtUrl : null
                     * ftype : 0
                     * rtUrls : []
                     * copyright : 1
                     * mvid : 0
                     * bMusic : {"name":"Time To Say Goodbye","id":21988403,"size":2949479,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":244689,"volumeDelta":-1.67}
                     * mp3Url : null
                     * rtype : 0
                     * rurl : null
                     * hMusic : {"name":"Time To Say Goodbye","id":21988401,"size":9794690,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":320000,"playTime":244689,"volumeDelta":-2.09}
                     * mMusic : {"name":"Time To Say Goodbye","id":21988402,"size":4905528,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":160000,"playTime":244689,"volumeDelta":-1.66}
                     * lMusic : {"name":"Time To Say Goodbye","id":21988403,"size":2949479,"extension":"mp3","sr":44100,"dfsId":0,"bitrate":96000,"playTime":244689,"volumeDelta":-1.67}
                     */

                    private String name;
                    private long id;
                    private int position;
                    private int status;
                    private int fee;
                    private int copyrightId;
                    private String disc;
                    private int no;
                    private AlbumBean album;
                    private boolean starred;
                    private int popularity;
                    private int score;
                    private int starredNum;
                    private int duration;
                    private int playedNum;
                    private int dayPlays;
                    private int hearTime;
                    private String ringtone;
                    private Object crbt;
                    private Object audition;
                    private String copyFrom;
                    private String commentThreadId;
                    private Object rtUrl;
                    private int ftype;
                    private int copyright;
                    private int mvid;
                    private BMusicBean bMusic;
                    private Object mp3Url;
                    private int rtype;
                    private Object rurl;
                    private HMusicBean hMusic;
                    private MMusicBean mMusic;
                    private LMusicBean lMusic;
                    private List<?> alias;
                    private List<ArtistsBeanXX> artists;
                    private List<?> rtUrls;

                    public String getName() {
                        return name;
                    }

                    public void setName(String name) {
                        this.name = name;
                    }

                    public long getId() {
                        return id;
                    }

                    public void setId(long id) {
                        this.id = id;
                    }

                    public int getPosition() {
                        return position;
                    }

                    public void setPosition(int position) {
                        this.position = position;
                    }

                    public int getStatus() {
                        return status;
                    }

                    public void setStatus(int status) {
                        this.status = status;
                    }

                    public int getFee() {
                        return fee;
                    }

                    public void setFee(int fee) {
                        this.fee = fee;
                    }

                    public int getCopyrightId() {
                        return copyrightId;
                    }

                    public void setCopyrightId(int copyrightId) {
                        this.copyrightId = copyrightId;
                    }

                    public String getDisc() {
                        return disc;
                    }

                    public void setDisc(String disc) {
                        this.disc = disc;
                    }

                    public int getNo() {
                        return no;
                    }

                    public void setNo(int no) {
                        this.no = no;
                    }

                    public AlbumBean getAlbum() {
                        return album;
                    }

                    public void setAlbum(AlbumBean album) {
                        this.album = album;
                    }

                    public boolean isStarred() {
                        return starred;
                    }

                    public void setStarred(boolean starred) {
                        this.starred = starred;
                    }

                    public int getPopularity() {
                        return popularity;
                    }

                    public void setPopularity(int popularity) {
                        this.popularity = popularity;
                    }

                    public int getScore() {
                        return score;
                    }

                    public void setScore(int score) {
                        this.score = score;
                    }

                    public int getStarredNum() {
                        return starredNum;
                    }

                    public void setStarredNum(int starredNum) {
                        this.starredNum = starredNum;
                    }

                    public int getDuration() {
                        return duration;
                    }

                    public void setDuration(int duration) {
                        this.duration = duration;
                    }

                    public int getPlayedNum() {
                        return playedNum;
                    }

                    public void setPlayedNum(int playedNum) {
                        this.playedNum = playedNum;
                    }

                    public int getDayPlays() {
                        return dayPlays;
                    }

                    public void setDayPlays(int dayPlays) {
                        this.dayPlays = dayPlays;
                    }

                    public int getHearTime() {
                        return hearTime;
                    }

                    public void setHearTime(int hearTime) {
                        this.hearTime = hearTime;
                    }

                    public String getRingtone() {
                        return ringtone;
                    }

                    public void setRingtone(String ringtone) {
                        this.ringtone = ringtone;
                    }

                    public Object getCrbt() {
                        return crbt;
                    }

                    public void setCrbt(Object crbt) {
                        this.crbt = crbt;
                    }

                    public Object getAudition() {
                        return audition;
                    }

                    public void setAudition(Object audition) {
                        this.audition = audition;
                    }

                    public String getCopyFrom() {
                        return copyFrom;
                    }

                    public void setCopyFrom(String copyFrom) {
                        this.copyFrom = copyFrom;
                    }

                    public String getCommentThreadId() {
                        return commentThreadId;
                    }

                    public void setCommentThreadId(String commentThreadId) {
                        this.commentThreadId = commentThreadId;
                    }

                    public Object getRtUrl() {
                        return rtUrl;
                    }

                    public void setRtUrl(Object rtUrl) {
                        this.rtUrl = rtUrl;
                    }

                    public int getFtype() {
                        return ftype;
                    }

                    public void setFtype(int ftype) {
                        this.ftype = ftype;
                    }

                    public int getCopyright() {
                        return copyright;
                    }

                    public void setCopyright(int copyright) {
                        this.copyright = copyright;
                    }

                    public int getMvid() {
                        return mvid;
                    }

                    public void setMvid(int mvid) {
                        this.mvid = mvid;
                    }

                    public BMusicBean getBMusic() {
                        return bMusic;
                    }

                    public void setBMusic(BMusicBean bMusic) {
                        this.bMusic = bMusic;
                    }

                    public Object getMp3Url() {
                        return mp3Url;
                    }

                    public void setMp3Url(Object mp3Url) {
                        this.mp3Url = mp3Url;
                    }

                    public int getRtype() {
                        return rtype;
                    }

                    public void setRtype(int rtype) {
                        this.rtype = rtype;
                    }

                    public Object getRurl() {
                        return rurl;
                    }

                    public void setRurl(Object rurl) {
                        this.rurl = rurl;
                    }

                    public HMusicBean getHMusic() {
                        return hMusic;
                    }

                    public void setHMusic(HMusicBean hMusic) {
                        this.hMusic = hMusic;
                    }

                    public MMusicBean getMMusic() {
                        return mMusic;
                    }

                    public void setMMusic(MMusicBean mMusic) {
                        this.mMusic = mMusic;
                    }

                    public LMusicBean getLMusic() {
                        return lMusic;
                    }

                    public void setLMusic(LMusicBean lMusic) {
                        this.lMusic = lMusic;
                    }

                    public List<?> getAlias() {
                        return alias;
                    }

                    public void setAlias(List<?> alias) {
                        this.alias = alias;
                    }

                    public List<ArtistsBeanXX> getArtists() {
                        return artists;
                    }

                    public void setArtists(List<ArtistsBeanXX> artists) {
                        this.artists = artists;
                    }

                    public List<?> getRtUrls() {
                        return rtUrls;
                    }

                    public void setRtUrls(List<?> rtUrls) {
                        this.rtUrls = rtUrls;
                    }

                    public static class AlbumBean {
                        /**
                         * name : The Very Best of Sarah Brightman 1990-2000
                         * id : 345234
                         * type : 专辑
                         * size : 16
                         * picId : 789449348785243
                         * blurPicUrl : http://p2.music.126.net/GyywegDyasFj_KAgps7VIg==/789449348785243.jpg
                         * companyId : 0
                         * pic : 789449348785243
                         * picUrl : http://p2.music.126.net/GyywegDyasFj_KAgps7VIg==/789449348785243.jpg
                         * publishTime : 991584000007
                         * description :
                         * tags :
                         * company : Atlantic UK
                         * briefDesc :
                         * artist : {"name":"","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}
                         * songs : []
                         * alias : []
                         * status : 1
                         * copyrightId : 5003
                         * commentThreadId : R_AL_3_345234
                         * artists : [{"name":"Sarah Brightman","id":74618,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]
                         */

                        private String name;
                        private long id;
                        private String type;
                        private int size;
                        private long picId;
                        private String blurPicUrl;
                        private int companyId;
                        private long pic;
                        private String picUrl;
                        private long publishTime;
                        private String description;
                        private String tags;
                        private String company;
                        private String briefDesc;
                        private ArtistBeanX artist;
                        private int status;
                        private int copyrightId;
                        private String commentThreadId;
                        private List<?> songs;
                        private List<?> alias;
                        private List<ArtistsBeanX> artists;

                        public String getName() {
                            return name;
                        }

                        public void setName(String name) {
                            this.name = name;
                        }

                        public long getId() {
                            return id;
                        }

                        public void setId(long id) {
                            this.id = id;
                        }

                        public String getType() {
                            return type;
                        }

                        public void setType(String type) {
                            this.type = type;
                        }

                        public int getSize() {
                            return size;
                        }

                        public void setSize(int size) {
                            this.size = size;
                        }

                        public long getPicId() {
                            return picId;
                        }

                        public void setPicId(long picId) {
                            this.picId = picId;
                        }

                        public String getBlurPicUrl() {
                            return blurPicUrl;
                        }

                        public void setBlurPicUrl(String blurPicUrl) {
                            this.blurPicUrl = blurPicUrl;
                        }

                        public int getCompanyId() {
                            return companyId;
                        }

                        public void setCompanyId(int companyId) {
                            this.companyId = companyId;
                        }

                        public long getPic() {
                            return pic;
                        }

                        public void setPic(long pic) {
                            this.pic = pic;
                        }

                        public String getPicUrl() {
                            return picUrl;
                        }

                        public void setPicUrl(String picUrl) {
                            this.picUrl = picUrl;
                        }

                        public long getPublishTime() {
                            return publishTime;
                        }

                        public void setPublishTime(long publishTime) {
                            this.publishTime = publishTime;
                        }

                        public String getDescription() {
                            return description;
                        }

                        public void setDescription(String description) {
                            this.description = description;
                        }

                        public String getTags() {
                            return tags;
                        }

                        public void setTags(String tags) {
                            this.tags = tags;
                        }

                        public String getCompany() {
                            return company;
                        }

                        public void setCompany(String company) {
                            this.company = company;
                        }

                        public String getBriefDesc() {
                            return briefDesc;
                        }

                        public void setBriefDesc(String briefDesc) {
                            this.briefDesc = briefDesc;
                        }

                        public ArtistBeanX getArtist() {
                            return artist;
                        }

                        public void setArtist(ArtistBeanX artist) {
                            this.artist = artist;
                        }

                        public int getStatus() {
                            return status;
                        }

                        public void setStatus(int status) {
                            this.status = status;
                        }

                        public int getCopyrightId() {
                            return copyrightId;
                        }

                        public void setCopyrightId(int copyrightId) {
                            this.copyrightId = copyrightId;
                        }

                        public String getCommentThreadId() {
                            return commentThreadId;
                        }

                        public void setCommentThreadId(String commentThreadId) {
                            this.commentThreadId = commentThreadId;
                        }

                        public List<?> getSongs() {
                            return songs;
                        }

                        public void setSongs(List<?> songs) {
                            this.songs = songs;
                        }

                        public List<?> getAlias() {
                            return alias;
                        }

                        public void setAlias(List<?> alias) {
                            this.alias = alias;
                        }

                        public List<ArtistsBeanX> getArtists() {
                            return artists;
                        }

                        public void setArtists(List<ArtistsBeanX> artists) {
                            this.artists = artists;
                        }

                        public static class ArtistBeanX {
                            /**
                             * name :
                             * id : 0
                             * picId : 0
                             * img1v1Id : 0
                             * briefDesc :
                             * picUrl : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                             * img1v1Url : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                             * albumSize : 0
                             * alias : []
                             * trans :
                             * musicSize : 0
                             */

                            private String name;
                            private long id;
                            private int picId;
                            private int img1v1Id;
                            private String briefDesc;
                            private String picUrl;
                            private String img1v1Url;
                            private int albumSize;
                            private String trans;
                            private int musicSize;
                            private List<?> alias;

                            public String getName() {
                                return name;
                            }

                            public void setName(String name) {
                                this.name = name;
                            }

                            public long getId() {
                                return id;
                            }

                            public void setId(long id) {
                                this.id = id;
                            }

                            public int getPicId() {
                                return picId;
                            }

                            public void setPicId(int picId) {
                                this.picId = picId;
                            }

                            public int getImg1v1Id() {
                                return img1v1Id;
                            }

                            public void setImg1v1Id(int img1v1Id) {
                                this.img1v1Id = img1v1Id;
                            }

                            public String getBriefDesc() {
                                return briefDesc;
                            }

                            public void setBriefDesc(String briefDesc) {
                                this.briefDesc = briefDesc;
                            }

                            public String getPicUrl() {
                                return picUrl;
                            }

                            public void setPicUrl(String picUrl) {
                                this.picUrl = picUrl;
                            }

                            public String getImg1v1Url() {
                                return img1v1Url;
                            }

                            public void setImg1v1Url(String img1v1Url) {
                                this.img1v1Url = img1v1Url;
                            }

                            public int getAlbumSize() {
                                return albumSize;
                            }

                            public void setAlbumSize(int albumSize) {
                                this.albumSize = albumSize;
                            }

                            public String getTrans() {
                                return trans;
                            }

                            public void setTrans(String trans) {
                                this.trans = trans;
                            }

                            public int getMusicSize() {
                                return musicSize;
                            }

                            public void setMusicSize(int musicSize) {
                                this.musicSize = musicSize;
                            }

                            public List<?> getAlias() {
                                return alias;
                            }

                            public void setAlias(List<?> alias) {
                                this.alias = alias;
                            }
                        }

                        public static class ArtistsBeanX {
                            /**
                             * name : Sarah Brightman
                             * id : 74618
                             * picId : 0
                             * img1v1Id : 0
                             * briefDesc :
                             * picUrl : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                             * img1v1Url : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                             * albumSize : 0
                             * alias : []
                             * trans :
                             * musicSize : 0
                             */

                            private String name;
                            private long id;
                            private int picId;
                            private int img1v1Id;
                            private String briefDesc;
                            private String picUrl;
                            private String img1v1Url;
                            private int albumSize;
                            private String trans;
                            private int musicSize;
                            private List<?> alias;

                            public String getName() {
                                return name;
                            }

                            public void setName(String name) {
                                this.name = name;
                            }

                            public long getId() {
                                return id;
                            }

                            public void setId(long id) {
                                this.id = id;
                            }

                            public int getPicId() {
                                return picId;
                            }

                            public void setPicId(int picId) {
                                this.picId = picId;
                            }

                            public int getImg1v1Id() {
                                return img1v1Id;
                            }

                            public void setImg1v1Id(int img1v1Id) {
                                this.img1v1Id = img1v1Id;
                            }

                            public String getBriefDesc() {
                                return briefDesc;
                            }

                            public void setBriefDesc(String briefDesc) {
                                this.briefDesc = briefDesc;
                            }

                            public String getPicUrl() {
                                return picUrl;
                            }

                            public void setPicUrl(String picUrl) {
                                this.picUrl = picUrl;
                            }

                            public String getImg1v1Url() {
                                return img1v1Url;
                            }

                            public void setImg1v1Url(String img1v1Url) {
                                this.img1v1Url = img1v1Url;
                            }

                            public int getAlbumSize() {
                                return albumSize;
                            }

                            public void setAlbumSize(int albumSize) {
                                this.albumSize = albumSize;
                            }

                            public String getTrans() {
                                return trans;
                            }

                            public void setTrans(String trans) {
                                this.trans = trans;
                            }

                            public int getMusicSize() {
                                return musicSize;
                            }

                            public void setMusicSize(int musicSize) {
                                this.musicSize = musicSize;
                            }

                            public List<?> getAlias() {
                                return alias;
                            }

                            public void setAlias(List<?> alias) {
                                this.alias = alias;
                            }
                        }
                    }

                    public static class BMusicBean {
                        /**
                         * name : Time To Say Goodbye
                         * id : 21988403
                         * size : 2949479
                         * extension : mp3
                         * sr : 44100
                         * dfsId : 0
                         * bitrate : 96000
                         * playTime : 244689
                         * volumeDelta : -1.67
                         */

                        private String name;
                        private long id;
                        private int size;
                        private String extension;
                        private int sr;
                        private int dfsId;
                        private int bitrate;
                        private int playTime;
                        private double volumeDelta;

                        public String getName() {
                            return name;
                        }

                        public void setName(String name) {
                            this.name = name;
                        }

                        public long getId() {
                            return id;
                        }

                        public void setId(long id) {
                            this.id = id;
                        }

                        public int getSize() {
                            return size;
                        }

                        public void setSize(int size) {
                            this.size = size;
                        }

                        public String getExtension() {
                            return extension;
                        }

                        public void setExtension(String extension) {
                            this.extension = extension;
                        }

                        public int getSr() {
                            return sr;
                        }

                        public void setSr(int sr) {
                            this.sr = sr;
                        }

                        public int getDfsId() {
                            return dfsId;
                        }

                        public void setDfsId(int dfsId) {
                            this.dfsId = dfsId;
                        }

                        public int getBitrate() {
                            return bitrate;
                        }

                        public void setBitrate(int bitrate) {
                            this.bitrate = bitrate;
                        }

                        public int getPlayTime() {
                            return playTime;
                        }

                        public void setPlayTime(int playTime) {
                            this.playTime = playTime;
                        }

                        public double getVolumeDelta() {
                            return volumeDelta;
                        }

                        public void setVolumeDelta(double volumeDelta) {
                            this.volumeDelta = volumeDelta;
                        }
                    }

                    public static class HMusicBean {
                        /**
                         * name : Time To Say Goodbye
                         * id : 21988401
                         * size : 9794690
                         * extension : mp3
                         * sr : 44100
                         * dfsId : 0
                         * bitrate : 320000
                         * playTime : 244689
                         * volumeDelta : -2.09
                         */

                        private String name;
                        private String id;
                        private int size;
                        private String extension;
                        private int sr;
                        private int dfsId;
                        private int bitrate;
                        private int playTime;
                        private double volumeDelta;

                        public String getName() {
                            return name;
                        }

                        public void setName(String name) {
                            this.name = name;
                        }

                        public String getId() {
                            return id;
                        }

                        public void setId(String id) {
                            this.id = id;
                        }

                        public int getSize() {
                            return size;
                        }

                        public void setSize(int size) {
                            this.size = size;
                        }

                        public String getExtension() {
                            return extension;
                        }

                        public void setExtension(String extension) {
                            this.extension = extension;
                        }

                        public int getSr() {
                            return sr;
                        }

                        public void setSr(int sr) {
                            this.sr = sr;
                        }

                        public int getDfsId() {
                            return dfsId;
                        }

                        public void setDfsId(int dfsId) {
                            this.dfsId = dfsId;
                        }

                        public int getBitrate() {
                            return bitrate;
                        }

                        public void setBitrate(int bitrate) {
                            this.bitrate = bitrate;
                        }

                        public int getPlayTime() {
                            return playTime;
                        }

                        public void setPlayTime(int playTime) {
                            this.playTime = playTime;
                        }

                        public double getVolumeDelta() {
                            return volumeDelta;
                        }

                        public void setVolumeDelta(double volumeDelta) {
                            this.volumeDelta = volumeDelta;
                        }
                    }

                    public static class MMusicBean {
                        /**
                         * name : Time To Say Goodbye
                         * id : 21988402
                         * size : 4905528
                         * extension : mp3
                         * sr : 44100
                         * dfsId : 0
                         * bitrate : 160000
                         * playTime : 244689
                         * volumeDelta : -1.66
                         */

                        private String name;
                        private String id;
                        private int size;
                        private String extension;
                        private int sr;
                        private int dfsId;
                        private int bitrate;
                        private int playTime;
                        private double volumeDelta;

                        public String getName() {
                            return name;
                        }

                        public void setName(String name) {
                            this.name = name;
                        }

                        public String getId() {
                            return id;
                        }

                        public void setId(String id) {
                            this.id = id;
                        }

                        public int getSize() {
                            return size;
                        }

                        public void setSize(int size) {
                            this.size = size;
                        }

                        public String getExtension() {
                            return extension;
                        }

                        public void setExtension(String extension) {
                            this.extension = extension;
                        }

                        public int getSr() {
                            return sr;
                        }

                        public void setSr(int sr) {
                            this.sr = sr;
                        }

                        public int getDfsId() {
                            return dfsId;
                        }

                        public void setDfsId(int dfsId) {
                            this.dfsId = dfsId;
                        }

                        public int getBitrate() {
                            return bitrate;
                        }

                        public void setBitrate(int bitrate) {
                            this.bitrate = bitrate;
                        }

                        public int getPlayTime() {
                            return playTime;
                        }

                        public void setPlayTime(int playTime) {
                            this.playTime = playTime;
                        }

                        public double getVolumeDelta() {
                            return volumeDelta;
                        }

                        public void setVolumeDelta(double volumeDelta) {
                            this.volumeDelta = volumeDelta;
                        }
                    }

                    public static class LMusicBean {
                        /**
                         * name : Time To Say Goodbye
                         * id : 21988403
                         * size : 2949479
                         * extension : mp3
                         * sr : 44100
                         * dfsId : 0
                         * bitrate : 96000
                         * playTime : 244689
                         * volumeDelta : -1.67
                         */

                        private String name;
                        private long id;
                        private int size;
                        private String extension;
                        private int sr;
                        private int dfsId;
                        private int bitrate;
                        private int playTime;
                        private double volumeDelta;

                        public String getName() {
                            return name;
                        }

                        public void setName(String name) {
                            this.name = name;
                        }

                        public long getId() {
                            return id;
                        }

                        public void setId(long id) {
                            this.id = id;
                        }

                        public int getSize() {
                            return size;
                        }

                        public void setSize(int size) {
                            this.size = size;
                        }

                        public String getExtension() {
                            return extension;
                        }

                        public void setExtension(String extension) {
                            this.extension = extension;
                        }

                        public int getSr() {
                            return sr;
                        }

                        public void setSr(int sr) {
                            this.sr = sr;
                        }

                        public int getDfsId() {
                            return dfsId;
                        }

                        public void setDfsId(int dfsId) {
                            this.dfsId = dfsId;
                        }

                        public int getBitrate() {
                            return bitrate;
                        }

                        public void setBitrate(int bitrate) {
                            this.bitrate = bitrate;
                        }

                        public int getPlayTime() {
                            return playTime;
                        }

                        public void setPlayTime(int playTime) {
                            this.playTime = playTime;
                        }

                        public double getVolumeDelta() {
                            return volumeDelta;
                        }

                        public void setVolumeDelta(double volumeDelta) {
                            this.volumeDelta = volumeDelta;
                        }
                    }

                    public static class ArtistsBeanXX {
                        /**
                         * name : Sarah Brightman
                         * id : 74618
                         * picId : 0
                         * img1v1Id : 0
                         * briefDesc :
                         * picUrl : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                         * img1v1Url : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                         * albumSize : 0
                         * alias : []
                         * trans :
                         * musicSize : 0
                         */

                        private String name;
                        private long id;
                        private int picId;
                        private int img1v1Id;
                        private String briefDesc;
                        private String picUrl;
                        private String img1v1Url;
                        private int albumSize;
                        private String trans;
                        private int musicSize;
                        private List<?> alias;

                        public String getName() {
                            return name;
                        }

                        public void setName(String name) {
                            this.name = name;
                        }

                        public long getId() {
                            return id;
                        }

                        public void setId(long id) {
                            this.id = id;
                        }

                        public int getPicId() {
                            return picId;
                        }

                        public void setPicId(int picId) {
                            this.picId = picId;
                        }

                        public int getImg1v1Id() {
                            return img1v1Id;
                        }

                        public void setImg1v1Id(int img1v1Id) {
                            this.img1v1Id = img1v1Id;
                        }

                        public String getBriefDesc() {
                            return briefDesc;
                        }

                        public void setBriefDesc(String briefDesc) {
                            this.briefDesc = briefDesc;
                        }

                        public String getPicUrl() {
                            return picUrl;
                        }

                        public void setPicUrl(String picUrl) {
                            this.picUrl = picUrl;
                        }

                        public String getImg1v1Url() {
                            return img1v1Url;
                        }

                        public void setImg1v1Url(String img1v1Url) {
                            this.img1v1Url = img1v1Url;
                        }

                        public int getAlbumSize() {
                            return albumSize;
                        }

                        public void setAlbumSize(int albumSize) {
                            this.albumSize = albumSize;
                        }

                        public String getTrans() {
                            return trans;
                        }

                        public void setTrans(String trans) {
                            this.trans = trans;
                        }

                        public int getMusicSize() {
                            return musicSize;
                        }

                        public void setMusicSize(int musicSize) {
                            this.musicSize = musicSize;
                        }

                        public List<?> getAlias() {
                            return alias;
                        }

                        public void setAlias(List<?> alias) {
                            this.alias = alias;
                        }
                    }
                }
            }
        }

        public static class AlbumBeanX {
            /**
             * moreText : 查看全部
             * albums : [{"name":"The Essential Boz Scaggs","id":2694934,"type":"专辑","size":32,"picId":17829680556434244,"blurPicUrl":"http://p4.music.126.net/_YmTuIMoG0dh2OJSOE-LPw==/17829680556434243.jpg","companyId":0,"pic":17829680556434244,"picUrl":"http://p3.music.126.net/_YmTuIMoG0dh2OJSOE-LPw==/17829680556434243.jpg","publishTime":1383004800000,"description":"","tags":"","company":"索尼音乐","briefDesc":"","artist":{"name":"Boz Scaggs","id":48514,"picId":***************,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p4.music.126.net/DHo_dBKUU5uRpBe1FeWEHw==/***************.jpg","img1v1Url":"http://p3.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":29,"alias":[],"trans":"","musicSize":381,"topicPerson":0,"img1v1Id_str":"18686200114669622","alia":[]},"songs":null,"alias":[],"status":0,"copyrightId":7001,"commentThreadId":"R_AL_3_2694934","artists":[{"name":"Boz Scaggs","id":48514,"picId":0,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0,"img1v1Id_str":"18686200114669622"}],"paid":false,"onSale":false,"picId_str":"17829680556434243","alg":"alg_album_basic_d"},{"name":"III.","id":3270985,"type":"EP/Single","size":3,"picId":7974757838028840,"blurPicUrl":"http://p3.music.126.net/wOsQ_2u6XrhESTsiN3KirQ==/7974757838028840.jpg","companyId":0,"pic":7974757838028840,"picUrl":"http://p4.music.126.net/wOsQ_2u6XrhESTsiN3KirQ==/7974757838028840.jpg","publishTime":1440086400007,"description":"","tags":"","company":"Atlantic Recording ","briefDesc":"","artist":{"name":"JoJo","id":60943,"picId":***************,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/sNBvINATXmjEFXQ9wS92sg==/***************.jpg","img1v1Url":"http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":44,"alias":[],"trans":"","musicSize":367,"topicPerson":0,"img1v1Id_str":"18686200114669622","alia":[]},"songs":null,"alias":[],"status":3,"copyrightId":0,"commentThreadId":"R_AL_3_3270985","artists":[{"name":"JoJo","id":60943,"picId":0,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0,"img1v1Id_str":"18686200114669622"}],"paid":false,"onSale":false,"alg":"alg_album_basic_d"},{"name":"转经道上的屠夫","id":35355954,"type":"专辑","size":8,"picId":109951162925144980,"blurPicUrl":"http://p3.music.126.net/Swy59brVeGMJE3a0FKuCKg==/109951162925144969.jpg","companyId":0,"pic":109951162925144980,"picUrl":"http://p3.music.126.net/Swy59brVeGMJE3a0FKuCKg==/109951162925144969.jpg","publishTime":1491921682146,"description":"","tags":"","company":null,"briefDesc":"","artist":{"name":"西藏病人","id":********,"picId":109951162869491150,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/wkPui_S4APuwza5NjULrJw==/109951162869491151.jpg","img1v1Url":"http://p3.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":3,"alias":[],"trans":"","musicSize":24,"topicPerson":0,"picId_str":"109951162869491151","img1v1Id_str":"18686200114669622","alia":[]},"songs":null,"alias":[],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_35355954","artists":[{"name":"西藏病人","id":********,"picId":0,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0,"img1v1Id_str":"18686200114669622"}],"paid":false,"onSale":false,"picId_str":"109951162925144969","alg":"alg_album_basic_d"}]
             * more : true
             */

            private String moreText;
            private boolean more;
            private List<AlbumsBean> albums;

            public String getMoreText() {
                return moreText;
            }

            public void setMoreText(String moreText) {
                this.moreText = moreText;
            }

            public boolean isMore() {
                return more;
            }

            public void setMore(boolean more) {
                this.more = more;
            }

            public List<AlbumsBean> getAlbums() {
                return albums;
            }

            public void setAlbums(List<AlbumsBean> albums) {
                this.albums = albums;
            }

            public static class AlbumsBean {
                /**
                 * name : The Essential Boz Scaggs
                 * id : 2694934
                 * type : 专辑
                 * size : 32
                 * picId : 17829680556434244
                 * blurPicUrl : http://p4.music.126.net/_YmTuIMoG0dh2OJSOE-LPw==/17829680556434243.jpg
                 * companyId : 0
                 * pic : 17829680556434244
                 * picUrl : http://p3.music.126.net/_YmTuIMoG0dh2OJSOE-LPw==/17829680556434243.jpg
                 * publishTime : 1383004800000
                 * description :
                 * tags :
                 * company : 索尼音乐
                 * briefDesc :
                 * artist : {"name":"Boz Scaggs","id":48514,"picId":***************,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p4.music.126.net/DHo_dBKUU5uRpBe1FeWEHw==/***************.jpg","img1v1Url":"http://p3.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":29,"alias":[],"trans":"","musicSize":381,"topicPerson":0,"img1v1Id_str":"18686200114669622","alia":[]}
                 * songs : null
                 * alias : []
                 * status : 0
                 * copyrightId : 7001
                 * commentThreadId : R_AL_3_2694934
                 * artists : [{"name":"Boz Scaggs","id":48514,"picId":0,"img1v1Id":18686200114669624,"briefDesc":"","picUrl":"http://p3.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0,"topicPerson":0,"img1v1Id_str":"18686200114669622"}]
                 * paid : false
                 * onSale : false
                 * picId_str : 17829680556434243
                 * alg : alg_album_basic_d
                 */

                private String name;
                private long id;
                private String type;
                private int size;
                private long picId;
                private String blurPicUrl;
                private int companyId;
                private long pic;
                private String picUrl;
                private long publishTime;
                private String description;
                private String tags;
                private String company;
                private String briefDesc;
                private ArtistBeanXX artist;
                private Object songs;
                private int status;
                private int copyrightId;
                private String commentThreadId;
                private boolean paid;
                private boolean onSale;
                private String picId_str;
                private String alg;
                private List<?> alias;
                private List<ArtistsBeanXXX> artists;

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public long getId() {
                    return id;
                }

                public void setId(long id) {
                    this.id = id;
                }

                public String getType() {
                    return type;
                }

                public void setType(String type) {
                    this.type = type;
                }

                public int getSize() {
                    return size;
                }

                public void setSize(int size) {
                    this.size = size;
                }

                public long getPicId() {
                    return picId;
                }

                public void setPicId(long picId) {
                    this.picId = picId;
                }

                public String getBlurPicUrl() {
                    return blurPicUrl;
                }

                public void setBlurPicUrl(String blurPicUrl) {
                    this.blurPicUrl = blurPicUrl;
                }

                public int getCompanyId() {
                    return companyId;
                }

                public void setCompanyId(int companyId) {
                    this.companyId = companyId;
                }

                public long getPic() {
                    return pic;
                }

                public void setPic(long pic) {
                    this.pic = pic;
                }

                public String getPicUrl() {
                    return picUrl;
                }

                public void setPicUrl(String picUrl) {
                    this.picUrl = picUrl;
                }

                public long getPublishTime() {
                    return publishTime;
                }

                public void setPublishTime(long publishTime) {
                    this.publishTime = publishTime;
                }

                public String getDescription() {
                    return description;
                }

                public void setDescription(String description) {
                    this.description = description;
                }

                public String getTags() {
                    return tags;
                }

                public void setTags(String tags) {
                    this.tags = tags;
                }

                public String getCompany() {
                    return company;
                }

                public void setCompany(String company) {
                    this.company = company;
                }

                public String getBriefDesc() {
                    return briefDesc;
                }

                public void setBriefDesc(String briefDesc) {
                    this.briefDesc = briefDesc;
                }

                public ArtistBeanXX getArtist() {
                    return artist;
                }

                public void setArtist(ArtistBeanXX artist) {
                    this.artist = artist;
                }

                public Object getSongs() {
                    return songs;
                }

                public void setSongs(Object songs) {
                    this.songs = songs;
                }

                public int getStatus() {
                    return status;
                }

                public void setStatus(int status) {
                    this.status = status;
                }

                public int getCopyrightId() {
                    return copyrightId;
                }

                public void setCopyrightId(int copyrightId) {
                    this.copyrightId = copyrightId;
                }

                public String getCommentThreadId() {
                    return commentThreadId;
                }

                public void setCommentThreadId(String commentThreadId) {
                    this.commentThreadId = commentThreadId;
                }

                public boolean isPaid() {
                    return paid;
                }

                public void setPaid(boolean paid) {
                    this.paid = paid;
                }

                public boolean isOnSale() {
                    return onSale;
                }

                public void setOnSale(boolean onSale) {
                    this.onSale = onSale;
                }

                public String getPicId_str() {
                    return picId_str;
                }

                public void setPicId_str(String picId_str) {
                    this.picId_str = picId_str;
                }

                public String getAlg() {
                    return alg;
                }

                public void setAlg(String alg) {
                    this.alg = alg;
                }

                public List<?> getAlias() {
                    return alias;
                }

                public void setAlias(List<?> alias) {
                    this.alias = alias;
                }

                public List<ArtistsBeanXXX> getArtists() {
                    return artists;
                }

                public void setArtists(List<ArtistsBeanXXX> artists) {
                    this.artists = artists;
                }

                public static class ArtistBeanXX {
                    /**
                     * name : Boz Scaggs
                     * id : 48514
                     * picId : ***************
                     * img1v1Id : 18686200114669624
                     * briefDesc :
                     * picUrl : http://p4.music.126.net/DHo_dBKUU5uRpBe1FeWEHw==/***************.jpg
                     * img1v1Url : http://p3.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg
                     * albumSize : 29
                     * alias : []
                     * trans :
                     * musicSize : 381
                     * topicPerson : 0
                     * img1v1Id_str : 18686200114669622
                     * alia : []
                     */

                    private String name;
                    private long id;
                    private long picId;
                    private long img1v1Id;
                    private String briefDesc;
                    private String picUrl;
                    private String img1v1Url;
                    private int albumSize;
                    private String trans;
                    private int musicSize;
                    private int topicPerson;
                    private String img1v1Id_str;
                    private List<?> alias;
                    private List<?> alia;

                    public String getName() {
                        return name;
                    }

                    public void setName(String name) {
                        this.name = name;
                    }

                    public long getId() {
                        return id;
                    }

                    public void setId(long id) {
                        this.id = id;
                    }

                    public long getPicId() {
                        return picId;
                    }

                    public void setPicId(long picId) {
                        this.picId = picId;
                    }

                    public long getImg1v1Id() {
                        return img1v1Id;
                    }

                    public void setImg1v1Id(long img1v1Id) {
                        this.img1v1Id = img1v1Id;
                    }

                    public String getBriefDesc() {
                        return briefDesc;
                    }

                    public void setBriefDesc(String briefDesc) {
                        this.briefDesc = briefDesc;
                    }

                    public String getPicUrl() {
                        return picUrl;
                    }

                    public void setPicUrl(String picUrl) {
                        this.picUrl = picUrl;
                    }

                    public String getImg1v1Url() {
                        return img1v1Url;
                    }

                    public void setImg1v1Url(String img1v1Url) {
                        this.img1v1Url = img1v1Url;
                    }

                    public int getAlbumSize() {
                        return albumSize;
                    }

                    public void setAlbumSize(int albumSize) {
                        this.albumSize = albumSize;
                    }

                    public String getTrans() {
                        return trans;
                    }

                    public void setTrans(String trans) {
                        this.trans = trans;
                    }

                    public int getMusicSize() {
                        return musicSize;
                    }

                    public void setMusicSize(int musicSize) {
                        this.musicSize = musicSize;
                    }

                    public int getTopicPerson() {
                        return topicPerson;
                    }

                    public void setTopicPerson(int topicPerson) {
                        this.topicPerson = topicPerson;
                    }

                    public String getImg1v1Id_str() {
                        return img1v1Id_str;
                    }

                    public void setImg1v1Id_str(String img1v1Id_str) {
                        this.img1v1Id_str = img1v1Id_str;
                    }

                    public List<?> getAlias() {
                        return alias;
                    }

                    public void setAlias(List<?> alias) {
                        this.alias = alias;
                    }

                    public List<?> getAlia() {
                        return alia;
                    }

                    public void setAlia(List<?> alia) {
                        this.alia = alia;
                    }
                }

                public static class ArtistsBeanXXX {
                    /**
                     * name : Boz Scaggs
                     * id : 48514
                     * picId : 0
                     * img1v1Id : 18686200114669624
                     * briefDesc :
                     * picUrl : http://p3.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                     * img1v1Url : http://p4.music.126.net/VnZiScyynLG7atLIZ2YPkw==/18686200114669622.jpg
                     * albumSize : 0
                     * alias : []
                     * trans :
                     * musicSize : 0
                     * topicPerson : 0
                     * img1v1Id_str : 18686200114669622
                     */

                    private String name;
                    private long id;
                    private int picId;
                    private long img1v1Id;
                    private String briefDesc;
                    private String picUrl;
                    private String img1v1Url;
                    private int albumSize;
                    private String trans;
                    private int musicSize;
                    private int topicPerson;
                    private String img1v1Id_str;
                    private List<?> alias;

                    public String getName() {
                        return name;
                    }

                    public void setName(String name) {
                        this.name = name;
                    }

                    public long getId() {
                        return id;
                    }

                    public void setId(long id) {
                        this.id = id;
                    }

                    public int getPicId() {
                        return picId;
                    }

                    public void setPicId(int picId) {
                        this.picId = picId;
                    }

                    public long getImg1v1Id() {
                        return img1v1Id;
                    }

                    public void setImg1v1Id(long img1v1Id) {
                        this.img1v1Id = img1v1Id;
                    }

                    public String getBriefDesc() {
                        return briefDesc;
                    }

                    public void setBriefDesc(String briefDesc) {
                        this.briefDesc = briefDesc;
                    }

                    public String getPicUrl() {
                        return picUrl;
                    }

                    public void setPicUrl(String picUrl) {
                        this.picUrl = picUrl;
                    }

                    public String getImg1v1Url() {
                        return img1v1Url;
                    }

                    public void setImg1v1Url(String img1v1Url) {
                        this.img1v1Url = img1v1Url;
                    }

                    public int getAlbumSize() {
                        return albumSize;
                    }

                    public void setAlbumSize(int albumSize) {
                        this.albumSize = albumSize;
                    }

                    public String getTrans() {
                        return trans;
                    }

                    public void setTrans(String trans) {
                        this.trans = trans;
                    }

                    public int getMusicSize() {
                        return musicSize;
                    }

                    public void setMusicSize(int musicSize) {
                        this.musicSize = musicSize;
                    }

                    public int getTopicPerson() {
                        return topicPerson;
                    }

                    public void setTopicPerson(int topicPerson) {
                        this.topicPerson = topicPerson;
                    }

                    public String getImg1v1Id_str() {
                        return img1v1Id_str;
                    }

                    public void setImg1v1Id_str(String img1v1Id_str) {
                        this.img1v1Id_str = img1v1Id_str;
                    }

                    public List<?> getAlias() {
                        return alias;
                    }

                    public void setAlias(List<?> alias) {
                        this.alias = alias;
                    }
                }
            }
        }

        public static class VideoBean {
            /**
             * moreText : 查看全部
             * more : true
             * videos : [{"coverUrl":"http://p2.music.126.net/R6pCjd9qmH4LQm1idWOZig==/109951163888857522.jpg","title":"《jojo的奇妙冒险》各部BOSS的精彩操作（大误）P3","durationms":468695,"playTime":742404,"type":1,"creator":[{"userId":1484858585,"userName":"TwoFlyCats"}],"aliaName":null,"transName":null,"vid":"E7324156176B933029D10CB5E92E5B3F","markTypes":[],"alg":"alg_video_basic_d"},{"coverUrl":"http://p2.music.126.net/grzV0_AtUPoyTT-0lEuJFQ==/109951163918650420.jpg","title":"一起来跟着黑帮摇//","durationms":59000,"playTime":1165453,"type":1,"creator":[{"userId":266801423,"userName":"我还没玩死"}],"aliaName":null,"transName":null,"vid":"B48CB3849AC193321699BA76DCE3B822","markTypes":[],"alg":"alg_video_basic_d"},{"coverUrl":"http://p2.music.126.net/JwynWqljv9tc92D8cONR1g==/109951163639035741.jpg","title":"【红房子】Jojo性感高跟Jazz编舞小野猫经典曲Buttons","durationms":287105,"playTime":2530726,"type":1,"creator":[{"userId":469558241,"userName":"千叶小美"}],"aliaName":null,"transName":null,"vid":"B7BBC6977873723CF3A4D4946D558A8D","markTypes":[101,111],"alg":"alg_video_basic_d"},{"coverUrl":"http://p2.music.126.net/T2ypQQsWZw6HXx_h45TrbA==/109951163905058891.jpg","title":"《jojo的奇妙冒险》各部BOSS的精彩操作（大误）P4","durationms":147320,"playTime":283180,"type":1,"creator":[{"userId":1484858585,"userName":"TwoFlyCats"}],"aliaName":null,"transName":null,"vid":"3F028FB2D67183CEE90DD30FAEF918F5","markTypes":[],"alg":"alg_video_basic_d"},{"coverUrl":"http://p2.music.126.net/ygHygKm0ZBvsgue8G2osEQ==/109951164098918413.jpg","title":"jojo的奇妙冒险黄金之风：接受父子二人组的七页木大吧！","durationms":47760,"playTime":333186,"type":1,"creator":[{"userId":1484858585,"userName":"TwoFlyCats"}],"aliaName":null,"transName":null,"vid":"31ADD5E5E855117905B6CC28F9774CB2","markTypes":[],"alg":"alg_video_basic_d"}]
             */

            private String moreText;
            private boolean more;
            private List<VideosBean> videos;

            public String getMoreText() {
                return moreText;
            }

            public void setMoreText(String moreText) {
                this.moreText = moreText;
            }

            public boolean isMore() {
                return more;
            }

            public void setMore(boolean more) {
                this.more = more;
            }

            public List<VideosBean> getVideos() {
                return videos;
            }

            public void setVideos(List<VideosBean> videos) {
                this.videos = videos;
            }

            public static class VideosBean {
                /**
                 * coverUrl : http://p2.music.126.net/R6pCjd9qmH4LQm1idWOZig==/109951163888857522.jpg
                 * title : 《jojo的奇妙冒险》各部BOSS的精彩操作（大误）P3
                 * durationms : 468695
                 * playTime : 742404
                 * type : 1
                 * creator : [{"userId":1484858585,"userName":"TwoFlyCats"}]
                 * aliaName : null
                 * transName : null
                 * vid : E7324156176B933029D10CB5E92E5B3F
                 * markTypes : []
                 * alg : alg_video_basic_d
                 */

                private String coverUrl;
                private String title;
                private int durationms;
                private int playTime;
                private int type;
                private Object aliaName;
                private Object transName;
                private String vid;
                private String alg;
                private List<CreatorBeanX> creator;
                private List<?> markTypes;

                public String getCoverUrl() {
                    return coverUrl;
                }

                public void setCoverUrl(String coverUrl) {
                    this.coverUrl = coverUrl;
                }

                public String getTitle() {
                    return title;
                }

                public void setTitle(String title) {
                    this.title = title;
                }

                public int getDurationms() {
                    return durationms;
                }

                public void setDurationms(int durationms) {
                    this.durationms = durationms;
                }

                public int getPlayTime() {
                    return playTime;
                }

                public void setPlayTime(int playTime) {
                    this.playTime = playTime;
                }

                public int getType() {
                    return type;
                }

                public void setType(int type) {
                    this.type = type;
                }

                public Object getAliaName() {
                    return aliaName;
                }

                public void setAliaName(Object aliaName) {
                    this.aliaName = aliaName;
                }

                public Object getTransName() {
                    return transName;
                }

                public void setTransName(Object transName) {
                    this.transName = transName;
                }

                public String getVid() {
                    return vid;
                }

                public void setVid(String vid) {
                    this.vid = vid;
                }

                public String getAlg() {
                    return alg;
                }

                public void setAlg(String alg) {
                    this.alg = alg;
                }

                public List<CreatorBeanX> getCreator() {
                    return creator;
                }

                public void setCreator(List<CreatorBeanX> creator) {
                    this.creator = creator;
                }

                public List<?> getMarkTypes() {
                    return markTypes;
                }

                public void setMarkTypes(List<?> markTypes) {
                    this.markTypes = markTypes;
                }

                public static class CreatorBeanX {
                    /**
                     * userId : 1484858585
                     * userName : TwoFlyCats
                     */

                    private long userId;
                    private String userName;

                    public long getUserId() {
                        return userId;
                    }

                    public void setUserId(long userId) {
                        this.userId = userId;
                    }

                    public String getUserName() {
                        return userName;
                    }

                    public void setUserName(String userName) {
                        this.userName = userName;
                    }
                }
            }
        }

        public static class SimQueryBean {
            /**
             * sim_querys : [{"keyword":"阿姨压一压","alg":"default"},{"keyword":"黑帮摇","alg":"default"},{"keyword":"Awake","alg":"default"},{"keyword":"Taylor Swift新歌","alg":"default"},{"keyword":"哪吒","alg":"default"},{"keyword":"画","alg":"default"}]
             * more : false
             */

            private boolean more;
            private List<SimQuerysBean> sim_querys;

            public boolean isMore() {
                return more;
            }

            public void setMore(boolean more) {
                this.more = more;
            }

            public List<SimQuerysBean> getSim_querys() {
                return sim_querys;
            }

            public void setSim_querys(List<SimQuerysBean> sim_querys) {
                this.sim_querys = sim_querys;
            }

            public static class SimQuerysBean {
                /**
                 * keyword : 阿姨压一压
                 * alg : default
                 */

                private String keyword;
                private String alg;

                public String getKeyword() {
                    return keyword;
                }

                public void setKeyword(String keyword) {
                    this.keyword = keyword;
                }

                public String getAlg() {
                    return alg;
                }

                public void setAlg(String alg) {
                    this.alg = alg;
                }
            }
        }

        public static class UserBean {
            /**
             * moreText : 查看全部
             * more : true
             * users : [{"defaultAvatar":false,"province":310000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/MlJ9Pnz7iFi7n8Dqw4aezg==/****************.jpg","accountStatus":0,"gender":1,"city":310108,"birthday":*************,"userId":3701993,"userType":0,"nickname":"___JoJo","signature":"uoye voli","description":"","detailDescription":"","avatarImgId":****************,"backgroundImgId":109951162868126480,"backgroundUrl":"http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":0,"vipType":0,"remarkName":null,"avatarImgIdStr":"****************","backgroundImgIdStr":"109951162868126486","alg":"alg_user_basic_d"}]
             */

            private String moreText;
            private boolean more;
            private List<UserSearchBean.ResultBean.UserprofilesBean> users;

            public String getMoreText() {
                return moreText;
            }

            public void setMoreText(String moreText) {
                this.moreText = moreText;
            }

            public boolean isMore() {
                return more;
            }

            public void setMore(boolean more) {
                this.more = more;
            }

            public List<UserSearchBean.ResultBean.UserprofilesBean> getUsers() {
                return users;
            }

            public void setUsers(List<UserSearchBean.ResultBean.UserprofilesBean> users) {
                this.users = users;
            }

            public static class UsersBean {
                /**
                 * defaultAvatar : false
                 * province : 310000
                 * authStatus : 0
                 * followed : false
                 * avatarUrl : http://p1.music.126.net/MlJ9Pnz7iFi7n8Dqw4aezg==/****************.jpg
                 * accountStatus : 0
                 * gender : 1
                 * city : 310108
                 * birthday : *************
                 * userId : 3701993
                 * userType : 0
                 * nickname : ___JoJo
                 * signature : uoye voli
                 * description :
                 * detailDescription :
                 * avatarImgId : ****************
                 * backgroundImgId : 109951162868126480
                 * backgroundUrl : http://p1.music.126.net/_f8R60U9mZ42sSNvdPn2sQ==/109951162868126486.jpg
                 * authority : 0
                 * mutual : false
                 * expertTags : null
                 * experts : null
                 * djStatus : 0
                 * vipType : 0
                 * remarkName : null
                 * avatarImgIdStr : ****************
                 * backgroundImgIdStr : 109951162868126486
                 * alg : alg_user_basic_d
                 */

                private boolean defaultAvatar;
                private int province;
                private int authStatus;
                private boolean followed;
                private String avatarUrl;
                private int accountStatus;
                private int gender;
                private int city;
                private long birthday;
                private int userId;
                private int userType;
                private String nickname;
                private String signature;
                private String description;
                private String detailDescription;
                private long avatarImgId;
                private long backgroundImgId;
                private String backgroundUrl;
                private int authority;
                private boolean mutual;
                private Object expertTags;
                private Object experts;
                private int djStatus;
                private int vipType;
                private Object remarkName;
                private String avatarImgIdStr;
                private String backgroundImgIdStr;
                private String alg;

                public boolean isDefaultAvatar() {
                    return defaultAvatar;
                }

                public void setDefaultAvatar(boolean defaultAvatar) {
                    this.defaultAvatar = defaultAvatar;
                }

                public int getProvince() {
                    return province;
                }

                public void setProvince(int province) {
                    this.province = province;
                }

                public int getAuthStatus() {
                    return authStatus;
                }

                public void setAuthStatus(int authStatus) {
                    this.authStatus = authStatus;
                }

                public boolean isFollowed() {
                    return followed;
                }

                public void setFollowed(boolean followed) {
                    this.followed = followed;
                }

                public String getAvatarUrl() {
                    return avatarUrl;
                }

                public void setAvatarUrl(String avatarUrl) {
                    this.avatarUrl = avatarUrl;
                }

                public int getAccountStatus() {
                    return accountStatus;
                }

                public void setAccountStatus(int accountStatus) {
                    this.accountStatus = accountStatus;
                }

                public int getGender() {
                    return gender;
                }

                public void setGender(int gender) {
                    this.gender = gender;
                }

                public int getCity() {
                    return city;
                }

                public void setCity(int city) {
                    this.city = city;
                }

                public long getBirthday() {
                    return birthday;
                }

                public void setBirthday(long birthday) {
                    this.birthday = birthday;
                }

                public int getUserId() {
                    return userId;
                }

                public void setUserId(int userId) {
                    this.userId = userId;
                }

                public int getUserType() {
                    return userType;
                }

                public void setUserType(int userType) {
                    this.userType = userType;
                }

                public String getNickname() {
                    return nickname;
                }

                public void setNickname(String nickname) {
                    this.nickname = nickname;
                }

                public String getSignature() {
                    return signature;
                }

                public void setSignature(String signature) {
                    this.signature = signature;
                }

                public String getDescription() {
                    return description;
                }

                public void setDescription(String description) {
                    this.description = description;
                }

                public String getDetailDescription() {
                    return detailDescription;
                }

                public void setDetailDescription(String detailDescription) {
                    this.detailDescription = detailDescription;
                }

                public long getAvatarImgId() {
                    return avatarImgId;
                }

                public void setAvatarImgId(long avatarImgId) {
                    this.avatarImgId = avatarImgId;
                }

                public long getBackgroundImgId() {
                    return backgroundImgId;
                }

                public void setBackgroundImgId(long backgroundImgId) {
                    this.backgroundImgId = backgroundImgId;
                }

                public String getBackgroundUrl() {
                    return backgroundUrl;
                }

                public void setBackgroundUrl(String backgroundUrl) {
                    this.backgroundUrl = backgroundUrl;
                }

                public int getAuthority() {
                    return authority;
                }

                public void setAuthority(int authority) {
                    this.authority = authority;
                }

                public boolean isMutual() {
                    return mutual;
                }

                public void setMutual(boolean mutual) {
                    this.mutual = mutual;
                }

                public Object getExpertTags() {
                    return expertTags;
                }

                public void setExpertTags(Object expertTags) {
                    this.expertTags = expertTags;
                }

                public Object getExperts() {
                    return experts;
                }

                public void setExperts(Object experts) {
                    this.experts = experts;
                }

                public int getDjStatus() {
                    return djStatus;
                }

                public void setDjStatus(int djStatus) {
                    this.djStatus = djStatus;
                }

                public int getVipType() {
                    return vipType;
                }

                public void setVipType(int vipType) {
                    this.vipType = vipType;
                }

                public Object getRemarkName() {
                    return remarkName;
                }

                public void setRemarkName(Object remarkName) {
                    this.remarkName = remarkName;
                }

                public String getAvatarImgIdStr() {
                    return avatarImgIdStr;
                }

                public void setAvatarImgIdStr(String avatarImgIdStr) {
                    this.avatarImgIdStr = avatarImgIdStr;
                }

                public String getBackgroundImgIdStr() {
                    return backgroundImgIdStr;
                }

                public void setBackgroundImgIdStr(String backgroundImgIdStr) {
                    this.backgroundImgIdStr = backgroundImgIdStr;
                }

                public String getAlg() {
                    return alg;
                }

                public void setAlg(String alg) {
                    this.alg = alg;
                }
            }
        }
    }
}
