<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/img_gedan_comment_back"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_margin="10dp"
            android:src="@drawable/ic_left_arrow_black" />

        <TextView
            android:id="@+id/tv_gedan_detail_comment_num"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="7"
            android:gravity="center_vertical"
            android:text="评论"
            android:textColor="@color/black"
            android:textSize="19sp" />


        <ImageView
            android:id="@+id/img_tab_more"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="15dp"
            android:src="@drawable/ic_share_black" />
    </LinearLayout>

    <android.support.v4.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/rl_gedan_comment_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="20dp">

                <ImageView
                    android:id="@+id/iv_gedan_detail_comment_img"
                    android:layout_width="73dp"
                    android:layout_height="73dp"
                    android:src="@drawable/ic_test" />
                <ImageView
                    android:id="@+id/iv_album_right_flag"
                    android:layout_width="10dp"
                    android:layout_height="73dp"
                    android:layout_marginLeft="1dp"
                    android:layout_toRightOf="@+id/iv_gedan_detail_comment_img"
                    android:src="@drawable/ic_album_attach"
                    android:visibility="gone" />
                <TextView
                    android:id="@+id/tv_gedan_detail_comment_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="25dp"
                    android:layout_marginTop="5dp"
                    android:layout_toRightOf="@+id/iv_album_right_flag"
                    android:maxLines="2"
                    android:text="音乐实验室先锋嗅觉,不独特"
                    android:textColor="@color/black"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_gedan_detail_comment_creator"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_gedan_detail_comment_title"
                    android:layout_alignLeft="@+id/tv_gedan_detail_comment_title"
                    android:layout_marginTop="10dp"
                    android:text="鱼骨与飞鸟集"
                    android:textColor="#517D98"
                    android:textSize="14sp" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:src="@drawable/ic_right_arrow_gray" />
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_marginTop="15dp"
                android:background="#F6F6F6" />

            <android.support.v7.widget.RecyclerView
                android:id="@+id/rv_gedan_comment_normal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>
    </android.support.v4.widget.NestedScrollView>
</LinearLayout>