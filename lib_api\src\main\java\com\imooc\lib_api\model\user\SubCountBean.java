package com.imooc.lib_api.model.user;

public class SubCountBean {

    private int programCount;
    private int djRadioCount;
    private int mvCount;
    private int artistCount;
    private int newProgramCount;
    private int createDjRadioCount;
    private int createdPlaylistCount;
    private int subPlaylistCount;
    private int code;

    public void setProgramCount(int programCount) {
        this.programCount = programCount;
    }

    public int getProgramCount() {
        return programCount;
    }

    public void setDjRadioCount(int djRadioCount) {
        this.djRadioCount = djRadioCount;
    }

    public int getDjRadioCount() {
        return djRadioCount;
    }

    public void setMvCount(int mvCount) {
        this.mvCount = mvCount;
    }

    public int getMvCount() {
        return mvCount;
    }

    public void setArtistCount(int artistCount) {
        this.artistCount = artistCount;
    }

    public int getArtistCount() {
        return artistCount;
    }

    public void setNewProgramCount(int newProgramCount) {
        this.newProgramCount = newProgramCount;
    }

    public int getNewProgramCount() {
        return newProgramCount;
    }

    public void setCreateDjRadioCount(int createDjRadioCount) {
        this.createDjRadioCount = createDjRadioCount;
    }

    public int getCreateDjRadioCount() {
        return createDjRadioCount;
    }

    public void setCreatedPlaylistCount(int createdPlaylistCount) {
        this.createdPlaylistCount = createdPlaylistCount;
    }

    public int getCreatedPlaylistCount() {
        return createdPlaylistCount;
    }

    public void setSubPlaylistCount(int subPlaylistCount) {
        this.subPlaylistCount = subPlaylistCount;
    }

    public int getSubPlaylistCount() {
        return subPlaylistCount;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
