<?xml version="1.0" encoding="utf-8"?>
<android.support.v4.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <android.support.v7.widget.RecyclerView
            android:id="@+id/rv_spec_mine"
            android:layout_width="match_parent"
            android:layout_height="70dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="15dp"
            android:orientation="horizontal" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/app_background" />

        <LinearLayout
            android:id="@+id/ll_mine_local_music"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/fragment_main_item_img"
                android:layout_width="25dp"
                android:layout_height="30dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center_vertical"
                android:src="@drawable/ic_music" />

            <TextView
                android:id="@+id/fragment_main_item_title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"
                android:text="本地音乐"
                android:textColor="@color/black"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/fragment_main_item_localmusic_count"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:paddingLeft="5dp"
                android:text="(8)"
                android:textSize="12sp" />
        </LinearLayout>

        <!--分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="65dp"
            android:background="@color/app_background" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/fragment_main_item_img1"
                android:layout_width="25dp"
                android:layout_height="30dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center_vertical"
                android:src="@drawable/ic_recent_play" />

            <TextView
                android:id="@+id/fragment_main_item_title1"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"
                android:text="最近播放"
                android:textColor="@color/black"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/fragment_main_item_lately_count"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:paddingLeft="5dp"
                android:text="(8)"
                android:textSize="12sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="65dp"
            android:background="@color/app_background" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/fragment_main_item_img2"
                android:layout_width="25dp"
                android:layout_height="30dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center_vertical"
                android:src="@drawable/ic_download_black" />

            <TextView
                android:id="@+id/fragment_main_item_title2"
                android:layout_width="wrap_content"
                android:layout_height="39dp"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"
                android:text="下载管理"
                android:textColor="@color/black"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/fragment_main_item_count2"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:gravity="center_vertical"
                android:paddingLeft="5dp"
                android:text="(8)"
                android:textSize="12sp" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="65dp"
            android:background="@color/app_background" />

        <LinearLayout
            android:id="@+id/ll_mine_radio"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/fragment_main_item_img3"
                android:layout_width="25dp"
                android:layout_height="30dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center_vertical"
                android:src="@drawable/ic_my_radio" />

            <TextView
                android:id="@+id/fragment_main_item_title3"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"
                android:text="我的电台"
                android:textColor="@color/black"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/fragment_main_item_radio_count"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:paddingLeft="5dp"
                android:text="(8)"
                android:textSize="12sp" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="65dp"
            android:background="@color/app_background" />

        <LinearLayout
            android:id="@+id/ll_mine_collect"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/fragment_main_item_im5"
                android:layout_width="25dp"
                android:layout_height="30dp"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center_vertical"
                android:src="@drawable/ic_my_collection" />

            <TextView
                android:id="@+id/fragment_main_item_titl5"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"
                android:text="我的收藏"
                android:textColor="@color/black"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/fragment_main_item_collect_count"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:paddingLeft="5dp"
                android:text="(0)"
                android:textSize="12sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:background="@color/app_background" />

        <android.support.v7.widget.RecyclerView
            android:id="@+id/rv_mine_create_gedan"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>
</android.support.v4.widget.NestedScrollView>