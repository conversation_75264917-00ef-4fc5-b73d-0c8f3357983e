<?xml version="1.0" encoding="utf-8"?>
<android.support.v4.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <android.support.v7.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="10dp"
            app:cardCornerRadius="5dp">

            <com.bigkoo.convenientbanner.ConvenientBanner
                android:id="@+id/banner_recycler_item"
                android:layout_width="match_parent"
                android:layout_height="130dp" />
        </android.support.v7.widget.CardView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/ll_discover_daily_recommend"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_mine_spec"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/bg_item_spec"
                    android:src="@drawable/t_dragonball_icn_daily" />

                <TextView
                    android:id="@+id/tv_spec_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:text="每日推荐"
                    android:textSize="11sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_discover_gedan"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_mine_spec1"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/bg_item_spec"
                    android:src="@drawable/t_dragonball_icn_playlist" />

                <TextView
                    android:id="@+id/tv_spec_name1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:text="歌单"
                    android:textSize="11sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_discover_rank"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_mine_spec2"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/bg_item_spec"
                    android:src="@drawable/t_dragonball_icn_rank" />

                <TextView
                    android:id="@+id/tv_spec_name2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:text="排行榜"
                    android:textSize="11sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_discover_radio"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_mine_spec3"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/bg_item_spec"
                    android:src="@drawable/t_dragonball_icn_radio" />

                <TextView
                    android:id="@+id/tv_spec_name3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:text="电台"
                    android:textSize="11sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_discover_myfm"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_mine_spec4"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/ic_mine_fm" />

                <TextView
                    android:id="@+id/tv_spec_name4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="5dp"
                    android:text="私人FM"
                    android:textSize="11sp" />

            </LinearLayout>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/app_background" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="15dp"
                android:layout_weight="8"
                android:padding="3dp"
                android:text="推荐歌单"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_discover_gedan_square"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="15dp"
                android:background="@drawable/bg_discover_gedan"
                android:text="歌单广场"
                android:textColor="@color/black"
                android:textSize="10sp" />
        </LinearLayout>

        <android.support.v7.widget.RecyclerView
            android:id="@+id/rv_discover_gedan"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_discover_new_album"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="15dp"
                android:padding="3dp"
                android:text="新碟"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_discover_new_song"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="15dp"
                android:layout_weight="3"
                android:padding="3dp"
                android:text="新歌"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_discover_more_album_song"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginRight="15dp"
                android:background="@drawable/bg_discover_gedan"
                android:text="更多新碟"
                android:textColor="@color/black"
                android:textSize="10sp" />
        </LinearLayout>

        <android.support.v7.widget.RecyclerView
            android:id="@+id/rv_discover_album_song"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="220dp" />
    </LinearLayout>
</android.support.v4.widget.NestedScrollView>