<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_delegate_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="visible">

        <ImageView
            android:id="@+id/delegate_tab_back"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_margin="10dp"
            android:src="@drawable/ic_left_arrow_black" />

        <TextView
            android:id="@+id/delegate_tab_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="7"
            android:gravity="center_vertical"
            android:textColor="@color/black"
            android:textSize="19sp" />

        <ImageView
            android:id="@+id/delegate_tab_search"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="15dp"
            android:src="@drawable/ic_search" />

        <ImageView
            android:id="@+id/delegate_tab_more"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="10dp"
            android:src="@drawable/ic_more_black" />

    </LinearLayout>

    <net.lucode.hackware.magicindicator.MagicIndicator
        android:id="@+id/delegate_magic_indicator_tab"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginTop="5dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/app_background" />

    <android.support.v4.view.ViewPager
        android:id="@+id/delegate_view_pager_tab"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="10" />
</LinearLayout>