apply plugin: 'com.android.library'
apply plugin: 'org.greenrobot.greendao'
apply plugin: 'maven-publish'



def pomName = this.getName()
def pomVersionName = "1.0.0-SNAPSHOT"
def pomDescription = "the audio library for all project"
def versionString = '1.0'
def versionNumber = 1

android {
    compileSdkVersion rootProject.android.compileSdkVersion
    namespace 'com.imooc.lib_audio'

    defaultConfig {
        minSdkVersion rootProject.android.minSdkVersion
        targetSdkVersion rootProject.android.targetSdkVersion
        versionCode versionNumber
        versionName versionString

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}
greendao {
    //其它配置保持默认,有需要再研究
    schemaVersion 1 //数据库版本号
    //daoPackage 'com.imooc.lib_audio.mediaplayer.db' //dao,master类所在包
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation this.rootProject.depsLibs.xpopup
    implementation this.rootProject.depsLibs.gson

    //占位主工程android原生库
    compileOnly this.rootProject.depsLibs.appcompat
    compileOnly this.rootProject.depsLibs.BaseRecyclerViewAdapterHelper
    compileOnly this.rootProject.depsLibs.design
    compileOnly this.rootProject.depsLibs.recyclerview
    //占位主工程的greenDao类库
    compileOnly this.rootProject.depsLibs.greendao
    //占位主工程的eventbus库
    compileOnly this.rootProject.depsLibs.eventbus
    //占位主工程的rxjava库
    compileOnly this.rootProject.depsLibs.rxjava
    compileOnly this.rootProject.depsLibs.rxandroid

    //占位主工程自己源码库
    compileOnly project(':lib_image_loader')
    compileOnly project(':lib_common_ui')
    compileOnly project(':lib_share')
    compileOnly project(':lib_api')
    compileOnly project(':lib_network')
}

// 暂时注释掉publishing配置，等编译成功后再配置
/*
afterEvaluate {
    publishing {
        publications {
            release(MavenPublication) {
                from components.release
                groupId = POM_GROUPID
                artifactId = pomName
                version = pomVersionName

                pom {
                    name = pomName
                    description = pomDescription
                    packaging = POM_PACKAGING
                }
            }
        }
        repositories {
            maven {
                url = NEXUS_REPOSITORY_URL
                credentials {
                    username = NEXUS_USERNAME
                    password = NEXUS_PASSWORD
                }
            }
        }
    }
}
*/
