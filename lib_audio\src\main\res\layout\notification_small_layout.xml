<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/image_view"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:scaleType="fitXY" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="10dp"
        android:layout_toEndOf="@id/image_view"
        android:orientation="vertical">

        <TextView
            android:id="@+id/title_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#333333"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="四块五" />

        <TextView
            android:id="@+id/tip_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:textColor="#999999"
            android:textSize="12sp"
            tools:text="隔壁老王" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/play_view"
            android:layout_width="30dp"
            android:layout_height="30dp" />


        <ImageView
            android:id="@+id/next_view"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="20dp" />

        <TextView
            android:id="@+id/ci_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:text="词"
            android:textColor="#666666"
            android:textSize="18sp" />

    </LinearLayout>
</RelativeLayout>