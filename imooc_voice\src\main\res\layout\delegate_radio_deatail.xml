<?xml version="1.0" encoding="utf-8"?>
<android.support.design.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <android.support.design.widget.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        app:elevation="0dp">

        <android.support.design.widget.CollapsingToolbarLayout
            android:layout_width="match_parent"
            android:layout_height="270dp"
            android:fitsSystemWindows="true"
            app:statusBarScrim="@android:color/transparent"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <ImageView
                android:id="@+id/iv_background_radio_detail"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_test"
                app:layout_collapseMode="parallax" />

            <ImageView
                android:id="@+id/iv_background_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                android:scaleType="centerCrop"
                app:layout_collapseMode="parallax" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="65dp"
                android:layout_gravity="bottom"
                android:layout_marginBottom="30dp"
                app:layout_collapseMode="parallax">

                <TextView
                    android:id="@+id/tv_radio_detail_title"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginRight="20dp"
                    android:maxLines="2"
                    android:ellipsize="end"
                    android:text="婉儿电台"
                    android:textColor="#f0f0f0f0"
                    android:textSize="17sp"
                    android:textStyle="bold"
                    android:typeface="monospace" />

                <TextView
                    android:id="@+id/tv_radio_detail_subscribed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_radio_detail_title"
                    android:layout_alignLeft="@+id/tv_radio_detail_title"
                    android:text="222人订阅"
                    android:textColor="#f0f0f0"
                    android:textSize="13sp"
                    android:typeface="monospace" />

                <TextView
                    android:id="@+id/tv_radio_detail_subscrib"
                    android:layout_alignParentRight="true"
                    android:layout_width="60dp"
                    android:layout_height="30dp"
                    android:layout_marginRight="20dp"
                    android:layout_marginTop="20dp"
                    android:background="@drawable/bg_round_red"
                    android:gravity="center"
                    android:text="订阅"
                    android:textSize="12sp"
                    android:textColor="#f0f0f0" />
            </RelativeLayout>


            <android.support.v7.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                app:layout_collapseMode="pin"
                app:contentInsetLeft="0dp"
                app:contentInsetStart="0dp"
                app:contentInsetEnd="0dp"
                app:maxButtonHeight="20dp"
                app:titleMargin="0dp">

                <RelativeLayout
                    android:id="@+id/rl_toolbar_gedan_detail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/img_radio_detail_back"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="10dp"
                        android:src="@drawable/ic_left_arrow_white" />
                    <TextView
                        android:id="@+id/tv_gedan_detail_toolbar_title"
                        android:layout_width="50dp"
                        android:layout_height="30dp"
                        android:layout_marginLeft="10dp"
                        android:gravity="center_vertical"
                        android:layout_toRightOf="@+id/img_radio_detail_back"
                        android:text="电台"
                        android:textColor="@color/white"
                        android:textSize="17sp" />

                </RelativeLayout>
            </android.support.v7.widget.Toolbar>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:layout_gravity="bottom"
                android:background="@drawable/bg_app_header"
                app:layout_collapseMode="pin" />
        </android.support.design.widget.CollapsingToolbarLayout>
    </android.support.design.widget.AppBarLayout>

    <RelativeLayout
        android:id="@+id/rl_play"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#ffffff"
        android:paddingBottom="40dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:id="@+id/ll_magicindicator"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            tools:ignore="RtlSymmetry"
            android:orientation="horizontal">
            <net.lucode.hackware.magicindicator.MagicIndicator
                android:id="@+id/magic_radio_indicator"
                android:layout_width="match_parent"
                android:layout_gravity="center"
                android:layout_height="wrap_content"/>
        </LinearLayout>

        <TextView
            android:id="@+id/tv_gap"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#f0f0f0"
            android:layout_below="@id/ll_magicindicator" />

        <android.support.v4.view.ViewPager
            android:id="@+id/view_pager_radio_detail"
            android:layout_below="@+id/tv_gap"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </RelativeLayout>


</android.support.design.widget.CoordinatorLayout>