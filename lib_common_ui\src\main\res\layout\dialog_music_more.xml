<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="5dp"
    android:background="@drawable/bg_dialog_music_more"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginTop="10dp">

        <ImageView
            android:id="@+id/img_album_head_dialog"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_margin="10dp"
            android:src="@drawable/ic_dialog_music" />

        <TextView
            android:id="@+id/tv_music_name_head_dialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/img_album_head_dialog"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_toRightOf="@id/img_album_head_dialog"
            android:text="歌曲:"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_artist_head_dialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_music_name_head_dialog"
            android:layout_alignLeft="@+id/tv_music_name_head_dialog"
            android:layout_marginTop="10dp"
            android:text="歌手" />
    </RelativeLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:background="@color/app_background" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <ImageView
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginLeft="25dp"
            android:src="@drawable/ic_next_song" />

        <TextView
            android:id="@+id/tv_next_dialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:gravity="center_vertical"
            android:text="下一首播放"
            android:textColor="@color/black"
            android:textSize="14sp" />
    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:layout_marginLeft="80dp"
        android:background="@color/app_background" />

    <LinearLayout
        android:id="@+id/ll_addfav_dialog"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <ImageView
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginLeft="25dp"
            android:src="@drawable/ic_collect" />

        <TextView
            android:id="@+id/tv_addfav_dialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:gravity="center_vertical"
            android:text="收藏到歌单"
            android:textColor="@color/black"
            android:textSize="14sp" />
    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:layout_marginLeft="80dp"
        android:background="@color/app_background" />

    <LinearLayout
        android:id="@+id/ll_share_dialog"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <ImageView
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginLeft="25dp"
            android:src="@drawable/ic_share_black" />

        <TextView
            android:id="@+id/tv_share_dialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:gravity="center_vertical"
            android:text="分享"
            android:textColor="@color/black"
            android:textSize="14sp" />
    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:layout_marginLeft="80dp"
        android:background="@color/app_background" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <ImageView
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginLeft="25dp"
            android:src="@drawable/ic_upload" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:gravity="center_vertical"
            android:text="上传到云盘"
            android:textColor="@color/black"
            android:textSize="14sp" />
    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:layout_marginLeft="80dp"
        android:background="@color/app_background" />

    <LinearLayout
        android:id="@+id/ll_comment_dialog"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <ImageView
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginLeft="25dp"
            android:src="@drawable/ic_comment_black" />

        <TextView
            android:id="@+id/tv_comment_dialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:gravity="center_vertical"
            android:text="评论"
            android:textColor="@color/black"
            android:textSize="14sp" />
    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:layout_marginLeft="80dp"
        android:background="@color/app_background" />

    <LinearLayout
        android:id="@+id/ll_artist_dialog"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <ImageView
            android:id="@+id/img_artist_dialog"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginLeft="25dp"
            android:src="@drawable/ic_singer" />

        <TextView
            android:id="@+id/tv_artist_dialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:gravity="center_vertical"
            android:text="歌手"
            android:textColor="@color/black"
            android:textSize="14sp" />
    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:layout_marginLeft="80dp"
        android:background="@color/app_background" />

    <LinearLayout
        android:id="@+id/ll_album_dialog"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <ImageView
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginLeft="25dp"
            android:src="@drawable/ic_album_black" />

        <TextView
            android:id="@+id/tv_album_dialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:gravity="center_vertical"
            android:text="专辑"
            android:textColor="@color/black"
            android:textSize="14sp" />
    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:layout_marginLeft="80dp"
        android:background="@color/app_background" />


    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:layout_marginLeft="80dp"
        android:background="@color/app_background" />

    <LinearLayout
        android:id="@+id/ll_delete_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:paddingBottom="10dp">

        <ImageView
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_marginLeft="25dp"
            android:src="@drawable/ic_delete_song" />

        <TextView
            android:id="@+id/tv_delete_dialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:gravity="center_vertical"
            android:text="删除"
            android:textColor="@color/black"
            android:textSize="14sp" />
    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="0.5dp"
        android:layout_marginLeft="80dp"
        android:background="@color/app_background" />
</LinearLayout>