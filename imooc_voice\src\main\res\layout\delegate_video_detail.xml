<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <android.support.v7.widget.Toolbar
        android:id="@+id/toolbar_video_detail"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        app:layout_collapseMode="pin"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetEnd="0dp"
        app:maxButtonHeight="20dp"
        app:titleMargin="0dp"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

        <RelativeLayout
            android:id="@+id/rl_toolbar_video_detail"
            android:layout_width="match_parent"
            android:layout_height="50dp">

            <ImageView
                android:id="@+id/img_video_detail_back"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginTop="10dp"
                android:layout_marginLeft="10dp"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_left_arrow_black" />


            <TextView
                android:id="@+id/tv_video_detail_toolbar_title"
                android:layout_width="200dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="10dp"
                android:layout_toRightOf="@+id/img_video_detail_back"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="17sp" />

        </RelativeLayout>
    </android.support.v7.widget.Toolbar>

    <LinearLayout
        android:id="@+id/rl_item_video_group"
        android:layout_width="match_parent"
        android:layout_height="180dp"
        android:layout_marginBottom="10dp">

        <com.imooc.lib_video.videoplayer.CustomJzVideoView
            android:id="@+id/videoplayer"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>

    <android.support.design.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <android.support.design.widget.AppBarLayout
            android:id="@+id/appbar_video_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:elevation="0dp">

            <android.support.design.widget.CollapsingToolbarLayout
                android:id="@+id/collapsing_toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_video_detail_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:layout_gravity="left"
                        android:maxLines="2"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_video_detail_playtime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="left"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:textSize="13sp"
                        android:textColor="@color/gray" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginBottom="5dp"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:id="@+id/ll_video_detail_comment"
                            android:layout_width="fill_parent"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <ImageView
                                android:id="@+id/iv_video_detail_praise"
                                android:layout_width="23dp"
                                android:layout_height="23dp"
                                android:layout_marginTop="10dp"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_parise" />

                            <TextView
                                android:id="@+id/tv_video_detail_praise_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:textColor="@color/gray"
                                android:textSize="11sp"/>
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/ll_video_collect"
                            android:layout_width="fill_parent"
                            android:layout_height="50dp"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <ImageView
                                android:id="@+id/iv_video_detail_collect"
                                android:layout_width="23dp"
                                android:layout_height="23dp"
                                android:layout_marginTop="10dp"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_collect" />

                            <TextView
                                android:id="@+id/tv_video_detail_collect_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/gray"
                                android:textSize="11sp"
                                android:layout_gravity="center_horizontal" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="50dp"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <ImageView
                                android:id="@+id/iv_video_detail_comment"
                                android:layout_width="23dp"
                                android:layout_height="23dp"
                                android:layout_marginTop="10dp"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_comment_black" />

                            <TextView
                                android:id="@+id/tv_video_detail_comment_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="11sp"
                                android:textColor="@color/gray"
                                android:layout_gravity="center_horizontal" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="fill_parent"
                            android:layout_height="50dp"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <ImageView
                                android:id="@+id/iv_video_detail_share"
                                android:layout_width="23dp"
                                android:layout_height="23dp"
                                android:layout_marginTop="10dp"
                                android:layout_gravity="center_horizontal"
                                android:src="@drawable/ic_share_black" />

                            <TextView
                                android:id="@+id/tv_video_detail_share_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/gray"
                                android:textSize="11sp"
                                android:layout_gravity="bottom|center_horizontal" />
                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/app_background"/>
                </LinearLayout>


            </android.support.design.widget.CollapsingToolbarLayout>

            <RelativeLayout
                android:id="@+id/rl_singer"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="10dp">

                <ImageView
                    android:id="@+id/iv_singer_avatar"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="12dp" />

                <TextView
                    android:id="@+id/tv_singer_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="15sp"
                    android:layout_toEndOf="@id/iv_singer_avatar"
                    android:textColor="#333333"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_singer_alias"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/tv_singer_name"
                    android:layout_alignBottom="@+id/tv_singer_name"
                    android:layout_toRightOf="@+id/tv_singer_name"
                    android:gravity="center_vertical"
                    android:textSize="15sp" />

                <LinearLayout
                    android:id="@+id/ll_singer_follow"
                    android:layout_width="65dp"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="20dp"
                    android:background="@drawable/bg_round_red_14dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="13dp"
                        android:layout_height="13dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="13dp"
                        android:layout_marginRight="3dp"
                        android:src="@drawable/ic_add_white" />

                    <TextView
                        android:id="@+id/tv_item_search_user_follow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:paddingTop="5dp"
                        android:paddingBottom="5dp"
                        android:text="关注"
                        android:textColor="@color/white"
                        android:textSize="11sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_singer_followed"
                    android:layout_width="65dp"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="20dp"
                    android:background="@drawable/bg_gray_border"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="13dp"
                        android:layout_height="13dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="10dp"
                        android:src="@drawable/ic_check" />

                    <TextView
                        android:id="@+id/tv_item_search_user_followed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingTop="5dp"
                        android:paddingBottom="5dp"
                        android:text="已关注"
                        android:textSize="11sp"
                        android:visibility="visible" />
                </LinearLayout>
            </RelativeLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="5dp"
                android:background="@color/app_background"/>
        </android.support.design.widget.AppBarLayout>


        <FrameLayout
            android:id="@+id/loadframe"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />


    </android.support.design.widget.CoordinatorLayout>
</LinearLayout>