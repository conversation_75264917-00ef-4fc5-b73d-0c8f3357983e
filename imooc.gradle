ext{

    android = [
            compileSdkVersion: 28,
            buildToolsVersion: "28.0.0",
            minSdkVersion    : 19,
            targetSdkVersion : 28,
            versionCode      : 1,
            versionName      : '1.0',
            multiDexEnabled: true ]
    depsVersion = [
        appcompat      : "28.0.0",
        support        : "25.3.1",
        magicindicator : '1.5.0',
        okhttp         : '3.3.0',
        gson           : '2.8.0',
        glide          : '4.9.0',
        glidecompiler  : '4.9.0',
        rxjava         : '2.2.9',
        rxandroid      : '2.1.1',
        eventbus       : '3.1.1',
        greendao       : '3.2.2',
        design         : '28.0.0',
        cardview       : '25.3.1',
        xpopup         : '1.8.10',
        multidex       : '1.0.1',
        easypermission : '1.0.1',
        tinypinyin     : '2.0.3',
        videoplayer    : '7.2.4',
        butterknife    : '8.4.0',
        cvbanner       : '2.0.5',
        fragmentation  : '1.2.4',
        cookiejar      : 'v1.0.1',
        butterknife_compiler               : '8.6.0',
        GroupedRecyclerViewAdapter         : '1.3.7',
        BaseRVAdapterHelper                : '2.9.22',
        view_pager_transforms              : '1.2.32@aar'

    ]
    depsLibs = [
            appcompat      : "com.android.support:appcompat-v7:${depsVersion.appcompat}",
            support        : "com.android.support:support-v4:${depsVersion.support}",
            magicindicator : "com.github.hackware1993:MagicIndicator:${depsVersion.magicindicator}",
            okhttp         : "com.squareup.okhttp3:okhttp:${depsVersion.okhttp}",
            gson           : "com.google.code.gson:gson:${depsVersion.gson}",
            glide          : "com.github.bumptech.glide:glide:${depsVersion.glide}",
            glidecompiler  : "com.github.bumptech.glide:compiler:${depsVersion.glidecompiler}",
            rxjava         : "io.reactivex.rxjava2:rxjava:${depsVersion.rxjava}",
            rxandroid      : "io.reactivex.rxjava2:rxandroid:${depsVersion.rxandroid}",
            eventbus       : "org.greenrobot:eventbus:${depsVersion.eventbus}",
            greendao       : "org.greenrobot:greendao:${depsVersion.greendao}",
            multidex       : "com.android.support:multidex:${depsVersion.multidex}",
            easypermission : "pub.devrel:easypermissions:${depsVersion.easypermission}",
            recyclerview   : "com.android.support:recyclerview-v7:${depsVersion.design}",
            butterknife    : "com.jakewharton:butterknife:${depsVersion.butterknife}",
            tinypinyin     : "com.github.promeg:tinypinyin:${depsVersion.tinypinyin}",
            videoplayer    : "cn.jzvd:jiaozivideoplayer:${depsVersion.videoplayer}",
            xpopup         : "com.lxj:xpopup:${depsVersion.xpopup}",
            fragmentation  : "me.yokeyword:fragmentation:${depsVersion.fragmentation}",
            vptransforms   : "com.ToxicBakery.viewpager.transforms:view-pager-transforms:${depsVersion.view_pager_transforms}",
            cvbanner       : "com.bigkoo:convenientbanner:${depsVersion.cvbanner}",
            design         : "com.android.support:design:${depsVersion.design}",
            cardview       : "com.android.support:cardview-v7:${depsVersion.cardview}",
            cookieJar      : "com.github.franmontiel:PersistentCookieJar:${depsVersion.cookiejar}",
            GroupedRecyclerViewAdapter            : "com.github.donkingliang:GroupedRecyclerViewAdapter:${depsVersion.GroupedRecyclerViewAdapter}",
            BaseRecyclerViewAdapterHelper         : "com.github.CymChad:BaseRecyclerViewAdapterHelper:${depsVersion.BaseRVAdapterHelper}",
            butterknife_compiler                  : "com.jakewharton:butterknife-compiler:${depsVersion.butterknife_compiler}",
    ]
}