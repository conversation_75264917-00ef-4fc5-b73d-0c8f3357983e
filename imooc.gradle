ext{

    android = [
            compileSdkVersion: 36,
            buildToolsVersion: "36.0.0",
            minSdkVersion    : 21,
            targetSdkVersion : 36,
            versionCode      : 1,
            versionName      : '1.0',
            multiDexEnabled: true ]
    depsVersion = [
        appcompat      : "1.7.0",
        support        : "1.0.0",
        magicindicator : '1.5.0',
        okhttp         : '4.12.0',
        gson           : '2.11.0',
        glide          : '4.16.0',
        glidecompiler  : '4.16.0',
        rxjava         : '3.1.9',
        rxandroid      : '3.0.2',
        eventbus       : '3.3.1',
        greendao       : '3.3.0',
        design         : '1.12.0',
        cardview       : '1.0.0',
        xpopup         : '2.10.0',
        multidex       : '2.0.1',
        easypermission : '3.0.0',
        tinypinyin     : '2.0.3',
        videoplayer    : '7.7.0',
        butterknife    : '10.2.3',
        cvbanner       : '2.0.5',
        fragmentation  : '1.3.6',
        cookiejar      : 'v1.0.1',
        butterknife_compiler               : '10.2.3',
        GroupedRecyclerViewAdapter         : '1.3.7',
        BaseRVAdapterHelper                : '3.0.11',
        view_pager_transforms              : '1.2.32@aar',
        recyclerview   : '1.3.2',
        constraintlayout : '2.2.0'

    ]
    depsLibs = [
            appcompat      : "androidx.appcompat:appcompat:${depsVersion.appcompat}",
            support        : "androidx.legacy:legacy-support-v4:${depsVersion.support}",
            magicindicator : "com.github.hackware1993:MagicIndicator:${depsVersion.magicindicator}",
            okhttp         : "com.squareup.okhttp3:okhttp:${depsVersion.okhttp}",
            gson           : "com.google.code.gson:gson:${depsVersion.gson}",
            glide          : "com.github.bumptech.glide:glide:${depsVersion.glide}",
            glidecompiler  : "com.github.bumptech.glide:compiler:${depsVersion.glidecompiler}",
            rxjava         : "io.reactivex.rxjava3:rxjava:${depsVersion.rxjava}",
            rxandroid      : "io.reactivex.rxjava3:rxandroid:${depsVersion.rxandroid}",
            eventbus       : "org.greenrobot:eventbus:${depsVersion.eventbus}",
            greendao       : "org.greenrobot:greendao:${depsVersion.greendao}",
            multidex       : "androidx.multidex:multidex:${depsVersion.multidex}",
            easypermission : "pub.devrel:easypermissions:${depsVersion.easypermission}",
            recyclerview   : "androidx.recyclerview:recyclerview:${depsVersion.recyclerview}",
            butterknife    : "com.jakewharton:butterknife:${depsVersion.butterknife}",
            tinypinyin     : "com.github.promeg:tinypinyin:${depsVersion.tinypinyin}",
            videoplayer    : "cn.jzvd:jiaozivideoplayer:${depsVersion.videoplayer}",
            xpopup         : "com.lxj:xpopup:${depsVersion.xpopup}",
            fragmentation  : "me.yokeyword:fragmentation:${depsVersion.fragmentation}",
            vptransforms   : "com.ToxicBakery.viewpager.transforms:view-pager-transforms:${depsVersion.view_pager_transforms}",
            cvbanner       : "com.bigkoo:convenientbanner:${depsVersion.cvbanner}",
            design         : "com.google.android.material:material:${depsVersion.design}",
            cardview       : "androidx.cardview:cardview:${depsVersion.cardview}",
            cookieJar      : "com.github.franmontiel:PersistentCookieJar:${depsVersion.cookiejar}",
            GroupedRecyclerViewAdapter            : "com.github.donkingliang:GroupedRecyclerViewAdapter:${depsVersion.GroupedRecyclerViewAdapter}",
            BaseRecyclerViewAdapterHelper         : "com.github.CymChad:BaseRecyclerViewAdapterHelper:${depsVersion.BaseRVAdapterHelper}",
            butterknife_compiler                  : "com.jakewharton:butterknife-compiler:${depsVersion.butterknife_compiler}",
            constraintlayout                      : "androidx.constraintlayout:constraintlayout:${depsVersion.constraintlayout}",
    ]
}