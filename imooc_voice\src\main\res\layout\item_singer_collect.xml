<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rl_singer"
    android:layout_width="match_parent"
    android:layout_height="60dp">

    <ImageView
        android:id="@+id/iv_singer_avatar"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="12dp" />

    <TextView
        android:id="@+id/tv_singer_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="15sp"
        android:layout_toEndOf="@id/iv_singer_avatar"
        android:textColor="#333333"
        android:textSize="15sp" />

    <TextView
        android:id="@+id/tv_singer_alias"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/tv_singer_name"
        android:layout_alignBottom="@+id/tv_singer_name"
        android:layout_toRightOf="@+id/tv_singer_name"
        android:gravity="center_vertical"
        android:textSize="15sp" />

    <TextView
        android:id="@+id/tv_item_singer_toptext"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/iv_singer_avatar"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="10dp"
        android:layout_toRightOf="@+id/iv_singer_avatar"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="我喜欢的音乐"
        android:textColor="@color/black"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_item_singer_bottomtext"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_item_singer_toptext"
        android:layout_alignLeft="@+id/tv_item_singer_toptext"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="5dp"
        android:layout_toRightOf="@+id/iv_singer_avatar"
        android:text="10首"
        android:textSize="11sp" />

    <ImageView
        android:id="@+id/iv_item_gedan_more"
        android:layout_width="15dp"
        android:layout_height="25dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="10dp"
        android:src="@drawable/ic_more_black" />
</RelativeLayout>