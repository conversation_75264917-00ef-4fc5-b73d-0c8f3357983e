<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:layout_marginTop="10dp">
    <ImageView
        android:id="@+id/iv_radio_pay_img"
        android:layout_width="110dp"
        android:layout_height="110dp"
        android:src="@drawable/ic_test"/>
    <TextView
        android:id="@+id/tv_radio_pay_title"
        android:layout_toRightOf="@+id/iv_radio_pay_img"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="15dp"
        android:textColor="@color/black"
        android:textStyle="bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="韩嘉天读点音乐进阶训练营"/>
    <TextView
        android:id="@+id/tv_radio_pay_zuixin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_radio_pay_title"
        android:layout_alignLeft="@+id/tv_radio_pay_title"
        android:layout_marginTop="5dp"
        android:textSize="11sp"
        android:text="最新上架"/>
    <TextView
        android:id="@+id/tv_radio_pay_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_radio_pay_zuixin"
        android:layout_alignLeft="@+id/tv_radio_pay_title"
        android:layout_marginTop="5dp"
        android:textSize="11sp"
        android:text="100首古典音乐分析课"/>
    <TextView
        android:id="@+id/tv_radio_pay_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_radio_pay_desc"
        android:layout_alignLeft="@+id/tv_radio_pay_title"
        android:layout_marginTop="5dp"
        android:textColor="@color/red"
        android:text="￥99"/>
</RelativeLayout>