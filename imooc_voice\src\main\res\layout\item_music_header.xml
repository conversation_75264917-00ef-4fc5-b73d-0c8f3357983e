<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="55dp"
    android:orientation="horizontal"
    android:paddingTop="15dp"
    android:paddingBottom="15dp">

    <ImageView
        android:id="@+id/play_all"
        android:layout_width="20dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:src="@drawable/ic_play_gray" />

    <TextView
        android:id="@+id/play_all_text"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:text="播放全部"
        android:textColor="@color/text_color"
        android:textSize="15sp" />

    <TextView
        android:id="@+id/play_all_number"
        android:layout_width="0dp"
        android:layout_weight="6"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:text="(共150首)"
        android:textSize="13sp" />

    <ImageView
        android:id="@+id/select"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_gravity="center|clip_vertical"
        android:background="?android:attr/selectableItemBackground"
        android:focusable="false"
        android:gravity="end"
        android:layout_marginRight="5dp"
        android:src="@drawable/ic_menu_black" />
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:layout_marginRight="15dp"
        android:layout_gravity="center"
        android:gravity="center"
        android:text="多选"/>
</LinearLayout>