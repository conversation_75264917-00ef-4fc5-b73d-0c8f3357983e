<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/img_rank_back"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_margin="10dp"
            android:src="@drawable/ic_left_arrow_black" />

        <TextView
            android:id="@+id/tv_rank_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="7"
            android:gravity="center_vertical"
            android:text="排行榜"
            android:textColor="@color/black"
            android:textSize="19sp" />

    </LinearLayout>

    <android.support.v4.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="15dp"
                android:padding="3dp"
                android:text="官方榜"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <android.support.v7.widget.RecyclerView
                android:id="@+id/rv_rank_offical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="15dp"
                android:padding="3dp"
                android:text="其他榜"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <android.support.v7.widget.RecyclerView
                android:id="@+id/rv_rank_normal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </LinearLayout>
    </android.support.v4.widget.NestedScrollView>
</LinearLayout>