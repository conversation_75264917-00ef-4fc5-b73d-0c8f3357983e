<resources>

    <declare-styleable name="VerticalItem">
        <attr name="iconWidth" format="dimension" />
        <attr name="iconHeight" format="dimension" />
        <attr name="icon" format="reference" />
        <attr name="tipPaddingTop" format="dimension" />
        <attr name="tipPaddingRight" format="dimension" />
        <attr name="tipBg" format="reference" />
        <attr name="tipTextColor" format="color" />
        <attr name="tipTextSize" format="dimension" />
        <attr name="tipText" format="string" />
        <attr name="infoTextSize" format="dimension" />
        <attr name="infoTextColor" format="color" />
        <attr name="infoTextMarginTop" format="dimension" />
        <attr name="infoText" format="string" />
    </declare-styleable>

    <declare-styleable name="HornizeItemView">
        <attr name="paddingLeft" format="dimension" />
        <attr name="paddingRight" format="dimension" />
        <attr name="paddingTop" format="dimension" />
        <attr name="paddingBottom" format="dimension" />
        <attr name="hIconWidth" format="dimension" />
        <attr name="hIconHeight" format="dimension" />
        <attr name="hIcon" format="reference" />
        <attr name="iconPaddingRight" format="dimension" />
        <attr name="tileTextSize" format="dimension" />
        <attr name="tileTextColor" format="color" />
        <attr name="tileText" format="string" />
        <attr name="rightTextSize" format="dimension" />
        <attr name="rightTextColor" format="color" />
        <attr name="rightText" format="string" />
        <attr name="rightIcon" format="reference" />
        <attr name="rightIconWidth" format="dimension" />
        <attr name="rightIconHeight" format="dimension" />
        <attr name="rightIconMarginLeft" format="dimension" />
        <attr name="hTipTextSize" format="dimension" />
        <attr name="hTipTextColor" format="color" />
        <attr name="hTipText" format="string" />
        <attr name="hTipPaddingLeft" format="dimension" />
        <attr name="hTipVisiblity" format="boolean" />
    </declare-styleable>

    <!--CircleImageView使用-->
    <declare-styleable name="CircleImageView">
        <attr name="civ_border_width" format="dimension" />
        <attr name="civ_border_color" format="color" />
        <attr name="civ_border_overlay" format="boolean" />
        <attr name="civ_circle_background_color" format="color" />
    </declare-styleable>

    <declare-styleable name="SpreadView">
        <!--中心圆颜色-->
        <attr name="spread_center_color" format="color" />
        <!--中心圆半径-->
        <attr name="spread_radius" format="integer" />
        <!--扩散圆颜色-->
        <attr name="spread_spread_color" format="color" />
        <!--扩散间距-->
        <attr name="spread_distance" format="integer" />
        <!--扩散最大半径-->
        <attr name="spread_max_radius" format="integer" />
        <!--扩散延迟间隔-->
        <attr name="spread_delay_milliseconds" format="integer" />
    </declare-styleable>

    <declare-styleable name="RikkaRoundRectView">
        <attr name="roundRatio" format="float" />
    </declare-styleable>

    <declare-styleable name="CircleProgressButton">
        <attr name="bgColor" format="color" />
        <attr name="progressColor" format="color" />
        <attr name="viewWidth" format="dimension" />
        <attr name="viewHeight" format="dimension" />
        <attr name="viewRoundWidth" format="dimension" />
    </declare-styleable>

    <declare-styleable name="LrcView">
        <attr name="lrcTextSize" format="dimension" />
        <attr name="lrcNormalTextSize" format="dimension" />
        <attr name="lrcDividerHeight" format="dimension" />
        <attr name="lrcNormalTextColor" format="reference|color" />
        <attr name="lrcCurrentTextColor" format="reference|color" />
        <attr name="lrcTimelineTextColor" format="reference|color" />
        <attr name="lrcAnimationDuration" format="integer" />
        <attr name="lrcLabel" format="string" />
        <attr name="lrcPadding" format="dimension" />
        <attr name="lrcTimelineColor" format="reference|color" />
        <attr name="lrcTimelineHeight" format="dimension" />
        <attr name="lrcPlayDrawable" format="reference" />
        <attr name="lrcTimeTextColor" format="reference|color" />
        <attr name="lrcTimeTextSize" format="dimension" />
        <attr name="lrcTextGravity">
            <enum name="center" value="0" />
            <enum name="left" value="1" />
            <enum name="right" value="2" />
        </attr>
    </declare-styleable>
</resources>
