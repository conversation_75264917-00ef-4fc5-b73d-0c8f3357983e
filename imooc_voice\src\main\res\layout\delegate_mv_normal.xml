<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="15dp"
            android:layout_weight="8"
            android:padding="3dp"
            android:text="MV精选"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_mv_more"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="15dp"
            android:background="@drawable/bg_discover_gedan"
            android:text="更多MV"
            android:textColor="@color/black"
            android:textSize="10sp" />
    </LinearLayout>

    <android.support.v7.widget.RecyclerView
        android:id="@+id/rv_rm_chosen"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <RelativeLayout
        android:id="@+id/rl_mv_rank"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp">

        <TextView
            android:id="@+id/tv_mv_rank_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:text="排行榜"
            android:textColor="@color/black"
            android:textSize="19sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_mv_rank_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_mv_rank_title"
            android:layout_marginTop="3dp"
            android:layout_marginBottom="15dp"
            android:textSize="12sp"
            android:text="更新时间 12:00" />

        <ImageView
            android:id="@+id/iv_mv_rank_img"
            android:layout_width="70dp"
            android:layout_height="40dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true" />
    </RelativeLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="15dp"
            android:layout_weight="8"
            android:padding="3dp"
            android:text="更多精彩MV"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_mv_sort"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="15dp"
            android:background="@drawable/bg_discover_gedan"
            android:text="MV分类"
            android:textColor="@color/black"
            android:textSize="10sp" />
    </LinearLayout>
    <android.support.v7.widget.RecyclerView
        android:id="@+id/rv_mv_more"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>