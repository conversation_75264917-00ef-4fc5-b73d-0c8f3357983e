<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/img_radio_back"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_margin="10dp"
            android:src="@drawable/ic_left_arrow_black" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="7"
            android:gravity="center_vertical"
            android:text="电台"
            android:textColor="@color/black"
            android:textSize="19sp" />

    </LinearLayout>

    <android.support.v7.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="15dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="10dp"
        app:cardCornerRadius="10dp"
        app:cardUseCompatPadding="true">

        <com.bigkoo.convenientbanner.ConvenientBanner
            android:id="@+id/banner_radio_recycler_item"
            android:layout_width="match_parent"
            android:layout_height="130dp" />
    </android.support.v7.widget.CardView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="25dp"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/ll_radio_sort"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="5dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_mine_spec"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/bg_item_spec"
                android:src="@drawable/t_dragonball_icn_radio_calss" />

            <TextView
                android:id="@+id/tv_spec_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="5dp"
                android:text="电台分类"
                android:textSize="11sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_radio_rank"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="5dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_mine_spec1"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/bg_item_spec"
                android:src="@drawable/t_dragonball_icn_radio_rank" />

            <TextView
                android:id="@+id/tv_spec_name1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="5dp"
                android:text="电台排行"
                android:textSize="11sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_radio_pay"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="5dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_mine_spec2"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/bg_item_spec"
                android:src="@drawable/t_dragonball_icn_radio_boutique" />

            <TextView
                android:id="@+id/tv_spec_name2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="5dp"
                android:text="付费精品"
                android:textSize="11sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_discover_radio"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="5dp"

            android:layout_weight="1"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_mine_spec3"
                android:layout_width="45dp"
                android:layout_height="45dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/bg_item_spec"
                android:src="@drawable/t_dragonball_icn_radio" />

            <TextView
                android:id="@+id/tv_spec_name3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="5dp"
                android:text="主播学院"
                android:textSize="11sp" />

        </LinearLayout>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/loadframe"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


</LinearLayout>