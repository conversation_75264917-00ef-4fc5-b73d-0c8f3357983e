<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="bb4f37b9-980d-4c5b-9785-2eb2d3e59e3f" name="Default Changelist" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="-21" />
    <option name="y" value="45" />
    <option name="width" value="1941" />
    <option name="height" value="1101" />
  </component>
  <component name="ProjectView">
    <navigator currentView="ProjectPane" proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="PackagesPane" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="mipmap-xxhdpi" type="b2602c69:ProjectViewProjectNode" />
              <item name="mipmap-xxhdpi" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="mipmap-xxhdpi" type="b2602c69:ProjectViewProjectNode" />
              <item name="mipmap-xxhdpi" type="462c0819:PsiDirectoryNode" />
              <item name=".idea" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="Scope" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="android.sdk.path" value="D:/sdk" />
    <property name="last_opened_file_path" value="$USER_HOME$/Documents/OTAUpdates-stable/OTAUpdates-stable" />
    <property name="settings.editor.selected.configurable" value="AndroidSdkUpdater" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="bb4f37b9-980d-4c5b-9785-2eb2d3e59e3f" name="Default Changelist" comment="" />
      <created>1581038401262</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1581038401262</updated>
    </task>
    <servers />
  </component>
  <component name="ToolWindowManager">
    <frame x="-7" y="-7" width="1295" height="695" extended-state="6" />
    <layout>
      <window_info active="true" content_ui="combo" id="Project" visible="true" weight="0.32983023" />
      <window_info id="Structure" side_tool="true" />
      <window_info id="Image Layers" />
      <window_info id="Designer" />
      <window_info id="Resources Explorer" />
      <window_info id="Capture Tool" />
      <window_info id="Favorites" side_tool="true" />
      <window_info id="Captures" order="0" weight="0.25" />
      <window_info anchor="bottom" id="Version Control" />
      <window_info anchor="bottom" id="TODO" />
      <window_info anchor="bottom" id="Terminal" />
      <window_info anchor="bottom" id="Event Log" side_tool="true" />
      <window_info anchor="right" id="Capture Analysis" />
      <window_info anchor="right" id="Theme Preview" />
      <window_info anchor="right" id="Word Book" side_tool="true" />
      <window_info anchor="right" id="Palette&#9;" />
    </layout>
  </component>
  <component name="masterDetails">
    <states>
      <state key="ScopeChooserConfigurable.UI">
        <settings>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
    </states>
  </component>
</project>