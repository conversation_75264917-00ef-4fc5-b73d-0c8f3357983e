<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/rl_item_video_group"
            android:layout_width="match_parent"
            android:layout_height="180dp"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp">

            <com.imooc.lib_video.videoplayer.CustomJzVideoView
                android:id="@+id/videoplayer"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/ll_item_video_status"
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:layout_alignLeft="@+id/rl_item_video_group"
            android:layout_alignBottom="@+id/rl_item_video_group"
            android:layout_marginLeft="5dp"
            android:layout_marginBottom="5dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_item_video_btn_play_ms"
                android:layout_width="13dp"
                android:layout_height="13dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/ic_song_play_num" />

            <TextView
                android:id="@+id/tv_item_video_play_count"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:layout_weight="1"
                android:text="567万"
                android:textColor="@color/white"
                android:textSize="11sp" />

            <ImageView
                android:id="@+id/iv_item_video_btn_play_time"
                android:layout_width="18dp"
                android:layout_height="match_parent"
                android:layout_marginRight="5dp"
                android:src="@drawable/ic_video_playtime" />

            <TextView
                android:id="@+id/tv_item_video_play_duration"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginRight="10dp"
                android:gravity="center_vertical"
                android:text="01:32"
                android:textColor="@color/white"
                android:textSize="11sp" />
        </LinearLayout>


        <TextView
            android:id="@+id/tv_item_video_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/rl_item_video_group"
            android:layout_alignRight="@+id/rl_item_video_group"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:background="@drawable/border_radio_subscribed"
            android:paddingLeft="7dp"
            android:paddingTop="3dp"
            android:paddingRight="7dp"
            android:paddingBottom="3dp"
            android:text="情感"
            android:visibility="gone"
            android:textColor="@color/white"
            android:textSize="11sp" />

        <TextView
            android:id="@+id/tv_item_video_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/rl_item_video_group"
            android:layout_marginBottom="10dp"
            android:maxLines="2"
            android:text="我是中华儿女"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <View
            android:id="@+id/view_line"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_below="@+id/tv_item_video_title"
            android:background="@color/app_background" />

        <ImageView
            android:id="@+id/iv_item_video_creator_img"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_below="@+id/view_line"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:src="@drawable/ic_test" />

        <TextView
            android:id="@+id/tv_item_video_creator_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/iv_item_video_creator_img"
            android:layout_alignBottom="@+id/iv_item_video_creator_img"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:layout_toRightOf="@+id/iv_item_video_creator_img"
            android:text="用户名"
            android:textColor="@color/black" />

        <ImageView
            android:id="@+id/iv_item_gedan_comment_zan"
            android:layout_width="17dp"
            android:layout_height="17dp"
            android:layout_alignTop="@+id/iv_item_video_creator_img"
            android:layout_alignBottom="@+id/iv_item_video_creator_img"
            android:layout_marginRight="20dp"
            android:layout_toLeftOf="@+id/iv_item_icon_comment"
            android:src="@drawable/ic_parise" />

        <TextView
            android:id="@+id/tv_item_video_praised_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@+id/iv_item_gedan_comment_zan"
            android:layout_alignTop="@+id/iv_item_gedan_comment_zan"
            android:layout_marginLeft="10dp"
            android:text="1889"
            android:textSize="8sp" />

        <ImageView
            android:id="@+id/iv_item_icon_comment"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_alignTop="@+id/iv_item_video_creator_img"
            android:layout_alignBottom="@+id/iv_item_video_creator_img"
            android:layout_marginRight="25dp"
            android:layout_toLeftOf="@+id/viewpager_item_video_list_button"
            android:src="@drawable/ic_comment_gray" />

        <TextView
            android:id="@+id/tv_item_video_comment_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignLeft="@+id/iv_item_icon_comment"
            android:layout_alignTop="@+id/iv_item_icon_comment"
            android:layout_marginLeft="10dp"
            android:text="1889"
            android:textSize="8sp" />

        <ImageView
            android:id="@+id/viewpager_item_video_list_button"
            android:layout_width="15dp"
            android:layout_height="20dp"
            android:layout_alignTop="@+id/iv_item_video_creator_img"
            android:layout_alignBottom="@+id/iv_item_video_creator_img"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp"
            android:focusable="true"
            android:src="@drawable/ic_more_black" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="7dp"
        android:layout_marginTop="5dp"
        android:background="@color/app_background" />
</LinearLayout>