package com.imooc.lib_api.model.playlist;

import java.util.List;

/**
 * 榜单Bean
 */
public class TopListBean {

    /**
     * code : 200
     * list : [{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每天更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":[],"status":0,"coverImgId":18696095720518496,"updateTime":1563236699492,"playCount":2127742592,"trackCount":100,"trackNumberUpdateTime":1563236698663,"trackUpdateTime":1563265499687,"cloudTrackCount":0,"highQuality":false,"createTime":1404115136883,"subscribedCount":2075315,"totalDuration":0,"coverImgUrl":"http://p1.music.126.net/DrRIg6CrgDfVLEph9SNh7w==/18696095720518497.jpg","commentThreadId":"A_PL_0_19723756","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":1,"description":"云音乐中每天热度上升最快的100首单曲，每日更新。","ordered":true,"adType":0,"name":"云音乐飙升榜","id":19723756,"coverImgId_str":"18696095720518497","ToplistType":"S"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每天更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":[],"status":0,"coverImgId":18713687906568050,"updateTime":1563236967792,"playCount":1233653120,"trackCount":100,"trackNumberUpdateTime":1563236885402,"trackUpdateTime":1563265767928,"cloudTrackCount":0,"highQuality":false,"createTime":1378721398225,"subscribedCount":1531128,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/N2HO5xfYEqyQ8q6oxCw8IQ==/18713687906568048.jpg","commentThreadId":"A_PL_0_3779629","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":1,"description":"云音乐新歌榜：云音乐用户一周内收听所有新歌（一月内最新发行） 官方TOP排行榜，每天更新。","ordered":true,"adType":0,"name":"云音乐新歌榜","id":3779629,"coverImgId_str":"18713687906568048","ToplistType":"N"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周四更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":[],"status":0,"coverImgId":18740076185638788,"updateTime":1562812786471,"playCount":307208160,"trackCount":100,"trackNumberUpdateTime":1562812786273,"trackUpdateTime":1562899157100,"cloudTrackCount":0,"highQuality":false,"createTime":1374732325894,"subscribedCount":471150,"totalDuration":0,"coverImgUrl":"http: //p2.music.126.net/sBzD11nforcuh1jdLSgX7g==/18740076185638788.jpg","commentThreadId":"A_PL_0_2884035","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":201586,"description":"云音乐独立原创音乐人作品官方榜单，以推荐优秀原创作品为目的。每周四网易云音乐首发。申请网易音乐人：http://music.163.com/nmusician/","ordered":true,"adType":0,"name":"网易原创歌曲榜","id":2884035,"coverImgId_str":"18740076185638788","ToplistType":"O"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周四更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":[],"status":0,"coverImgId":18708190348409092,"updateTime":1562809869014,"playCount":4282663680,"trackCount":200,"trackNumberUpdateTime":1562809868947,"trackUpdateTime":1563263474634,"cloudTrackCount":0,"highQuality":false,"createTime":1378721406014,"subscribedCount":6113418,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/GhhuF6Ep5Tq9IEvLsyCN7w==/18708190348409091.jpg","commentThreadId":"A_PL_0_3778678","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":1,"description":"云音乐热歌榜：云音乐用户一周内收听所有线上歌曲 官方TOP排行榜，每周四更新。","ordered":true,"adType":0,"name":"云音乐热歌榜","id":3778678,"coverImgId_str":"18708190348409091","ToplistType":"H"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周五更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["华语","说唱"],"status":0,"coverImgId":109951163781770030,"updateTime":1562900525732,"playCount":114481752,"trackCount":50,"trackNumberUpdateTime":1562900521242,"trackUpdateTime":1563231864747,"cloudTrackCount":0,"highQuality":false,"createTime":1510290389440,"subscribedCount":329825,"totalDuration":0,"coverImgUrl":"http: //p2.music.126.net/TuGxihwbiPmoHoFGvIuu_w==/109951163781770038.jpg","commentThreadId":"A_PL_0_991319590","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":1,"description":"云音乐原创说唱音乐人作品官方榜单，每周五更新。以云音乐用户一周播放热度为主，收录3个月内发行的原创说唱作品，按照综合数据排名取前50名。申请网易音乐人：http://music.163.com/nmusician","ordered":true,"adType":0,"name":"江小白YOLO云音乐说唱榜","id":991319590,"coverImgId_str":"109951163781770038"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周四更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["古典"],"status":0,"coverImgId":18681802069355170,"updateTime":1562812212704,"playCount":32742936,"trackCount":100,"trackNumberUpdateTime":1562812208560,"trackUpdateTime":1563186864105,"cloudTrackCount":0,"highQuality":false,"createTime":1430968920537,"subscribedCount":251592,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/BzSxoj6O1LQPlFceDn-LKw==/18681802069355169.jpg","commentThreadId":"A_PL_0_71384707","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":1,"description":"云音乐用户一周内收听所有古典音乐官方TOP排行榜，每周四更新。","ordered":true,"adType":0,"name":"云音乐古典音乐榜","id":71384707,"coverImgId_str":"18681802069355169"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周五更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["电子"],"status":0,"coverImgId":18822539557941308,"updateTime":1562900538798,"playCount":116769152,"trackCount":50,"trackNumberUpdateTime":1562900538707,"trackUpdateTime":1563207289001,"cloudTrackCount":0,"highQuality":false,"createTime":1510825632233,"subscribedCount":659632,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/5tgOCD4jiPKBGt7znJl-2g==/18822539557941307.jpg","commentThreadId":"A_PL_0_1978921795","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":1,"description":"云音乐用户一周内收听电子音乐官方TOP排行榜，每周五更新","ordered":true,"adType":0,"name":"云音乐电音榜","id":1978921795,"coverImgId_str":"18822539557941307"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周三更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["榜单"],"status":0,"coverImgId":109951164174523460,"updateTime":1562812689154,"playCount":275680032,"trackCount":100,"trackNumberUpdateTime":1562812689086,"trackUpdateTime":1563247680858,"cloudTrackCount":0,"highQuality":false,"createTime":1527831892491,"subscribedCount":1419248,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/oUxnXXvM33OUHxxukYnUjQ==/109951164174523461.jpg","commentThreadId":"A_PL_0_2250011882","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":1473357007,"description":"抖音排行榜，每周三更新。","ordered":true,"adType":0,"name":"抖音排行榜","id":2250011882,"coverImgId_str":"109951164174523461"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周一更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["华语","流行"],"status":0,"coverImgId":109951163785427940,"updateTime":1561995354595,"playCount":41514472,"trackCount":40,"trackNumberUpdateTime":1561995353888,"trackUpdateTime":1563186379617,"cloudTrackCount":0,"highQuality":false,"createTime":1547092729345,"subscribedCount":71159,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/XbjRDARP1xv5a-40ZDOy6A==/109951163785427934.jpg","commentThreadId":"A_PL_0_2617766278","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":106733386,"description":"LOOK直播 - 「LOOK新声代2」活动官方榜单，旨在推介超人气单曲和小众优质原创～","ordered":true,"adType":0,"name":"新声榜","id":2617766278,"coverImgId_str":"109951163785427934"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周四更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":[],"status":0,"coverImgId":18752170813539664,"updateTime":1562814786380,"playCount":43606864,"trackCount":100,"trackNumberUpdateTime":1562814786279,"trackUpdateTime":1562996892919,"cloudTrackCount":0,"highQuality":false,"createTime":1430968935040,"subscribedCount":160844,"totalDuration":0,"coverImgUrl":"http: //p2.music.126.net/vttjtRjL75Q4DEnjRsO8-A==/18752170813539664.jpg","commentThreadId":"A_PL_0_71385702","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":1,"description":"云音乐用户一周内收听所有ACG音乐官方TOP排行榜，每周四更新。","ordered":true,"adType":0,"name":"云音乐ACG音乐榜","id":71385702,"coverImgId_str":"18752170813539664"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周四更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["韩语","榜单"],"status":0,"coverImgId":18737877162497500,"updateTime":1562823863908,"playCount":24760656,"trackCount":100,"trackNumberUpdateTime":1562823863841,"trackUpdateTime":1563260025994,"cloudTrackCount":0,"highQuality":false,"createTime":1496201691281,"subscribedCount":71651,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/vs-cMh49e6qPEorHuhU07A==/18737877162497499.jpg","commentThreadId":"A_PL_0_745956260","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":1,"description":"云音乐用户一周内收听所有韩语歌曲官方TOP排行榜，每周四更新。","ordered":true,"adType":0,"name":"云音乐韩语榜","id":745956260,"coverImgId_str":"18737877162497499"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周五更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["电子","榜单"],"status":0,"coverImgId":109951163424197390,"updateTime":1562911798934,"playCount":63725944,"trackCount":20,"trackNumberUpdateTime":1562911766637,"trackUpdateTime":1563247786769,"cloudTrackCount":0,"highQuality":false,"createTime":1395988377813,"subscribedCount":211436,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/8-GBrukQ3BHhs4CmK6qE4w==/109951163424197392.jpg","commentThreadId":"A_PL_0_10520166","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":48102,"description":"▲▲▲本榜排名按作品发行时间顺序▲▲▲网易云音乐联合网易放刺、Loopy、加菲众、DJ WENGWENG（灯笼Club）、3ASiC（同步计划）、DJ Senders（沉睡电台）、Eiasn电音厂牌、电悦台（EDM Station） \n打造云音乐\u201c国电榜\u201d ! 每周五为大家带来网易电子音乐人优质新作！","ordered":true,"adType":0,"name":"云音乐国电榜","id":10520166,"coverImgId_str":"109951163424197392"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周三更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["欧美","流行","英伦"],"status":0,"coverImgId":109951163089272200,"updateTime":1562772925966,"playCount":4357689,"trackCount":20,"trackNumberUpdateTime":1562772925921,"trackUpdateTime":1562917942897,"cloudTrackCount":0,"highQuality":false,"createTime":1513838619821,"subscribedCount":18373,"totalDuration":0,"coverImgUrl":"http: //p2.music.126.net/0_6_Efe9m0D0NtghOxinUg==/109951163089272193.jpg","commentThreadId":"A_PL_0_2023401535","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":270539485,"description":"英国权威音乐杂志《Q》中文版&网易云音乐联合呈现榜单TOP20，英国《Q》杂志权威推荐。每周三同步更新。","ordered":true,"adType":0,"name":"英国Q杂志中文版周榜","id":2023401535,"coverImgId_str":"109951163089272193"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周五更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["游戏"],"status":0,"coverImgId":109951163078922990,"updateTime":1562900457294,"playCount":14400545,"trackCount":50,"trackNumberUpdateTime":1562900457220,"trackUpdateTime":1562900457596,"cloudTrackCount":0,"highQuality":false,"createTime":1512703064327,"subscribedCount":63438,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/CUqQp33MZf_m0BwH4u0V6A==/109951163078922993.jpg","commentThreadId":"A_PL_0_2006508653","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":6790397,"description":"无音乐，不游戏。人气电竞主播联手推荐，最热最潮电竞歌曲榜单，电竞迷们的必备收藏！","ordered":true,"adType":0,"name":"电竞音乐榜","id":2006508653,"coverImgId_str":"109951163078922993"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周一更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["榜单","欧美"],"status":0,"coverImgId":18930291695438268,"updateTime":1563243684331,"playCount":93158176,"trackCount":97,"trackNumberUpdateTime":1563243684172,"trackUpdateTime":1563247285665,"cloudTrackCount":0,"highQuality":false,"createTime":1361239766844,"subscribedCount":233648,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/VQOMRRix9_omZbg4t-pVpw==/18930291695438269.jpg","commentThreadId":"A_PL_0_180106","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":48333,"description":"UK排行榜","ordered":true,"adType":0,"name":"UK排行榜周榜","id":180106,"coverImgId_str":"18930291695438269"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周三更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["流行","欧美","榜单"],"status":0,"coverImgId":18641120139148116,"updateTime":1562726082712,"playCount":341813792,"trackCount":100,"trackNumberUpdateTime":1562726082607,"trackUpdateTime":1563241075046,"cloudTrackCount":0,"highQuality":false,"createTime":1358823076818,"subscribedCount":1000323,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/EBRqPmY8k8qyVHyF8AyjdQ==/18641120139148117.jpg","commentThreadId":"A_PL_0_60198","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":48171,"description":"美国Billboard排行榜","ordered":true,"adType":0,"name":"美国Billboard周榜","id":60198,"coverImgId_str":"18641120139148117"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周四更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["欧美","电子","榜单"],"status":0,"coverImgId":18613632348448740,"updateTime":1562894971532,"playCount":68371296,"trackCount":57,"trackNumberUpdateTime":1562894971416,"trackUpdateTime":1563248388756,"cloudTrackCount":0,"highQuality":false,"createTime":1378886589466,"subscribedCount":219996,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/A61n94BjWAb-ql4xpwpYcg==/18613632348448741.jpg","commentThreadId":"A_PL_0_3812895","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":1589393,"description":"Beatport全球电子舞曲排行榜TOP100（本榜每周四更新）","ordered":true,"adType":0,"name":"Beatport全球电子舞曲榜","id":3812895,"coverImgId_str":"18613632348448741"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周五更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["华语","KTV","榜单"],"status":0,"coverImgId":19174383276805160,"updateTime":1562903915238,"playCount":43167192,"trackCount":20,"trackNumberUpdateTime":1562306334476,"trackUpdateTime":1563185453022,"cloudTrackCount":0,"highQuality":false,"createTime":1405653093230,"subscribedCount":177364,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/H4Y7jxd_zwygcAmPMfwJnQ==/19174383276805159.jpg","commentThreadId":"A_PL_0_21845217","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":30728956,"description":"KTV唛榜是目前国内首个以全国超过200家KTV点歌平台真实数据的当红歌曲榜单。所涉及的KTV店铺覆盖全国近100多个城市，囊括一、二、三线各级城市及地区。在综合全国各地KTV点唱数据的前提下进行汇总与统计。为了保证信息的及时性，唛榜每周五更新。提供给K迷们最新和最准确的数据。","ordered":true,"adType":0,"name":"KTV唛榜","id":21845217,"coverImgId_str":"19174383276805159"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周一更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["榜单"],"status":0,"coverImgId":109951163601178880,"updateTime":1563244902731,"playCount":82551592,"trackCount":97,"trackNumberUpdateTime":1563244902619,"trackUpdateTime":1563244909385,"cloudTrackCount":0,"highQuality":false,"createTime":1398047444743,"subscribedCount":267726,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/WTpbsVfxeB6qDs_3_rnQtg==/109951163601178881.jpg","commentThreadId":"A_PL_0_11641012","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":48308,"description":"iTunes榜Top100","ordered":true,"adType":0,"name":"iTunes榜","id":11641012,"coverImgId_str":"109951163601178881"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周三更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["榜单","日语"],"status":0,"coverImgId":19029247741938160,"updateTime":1562742393680,"playCount":36091304,"trackCount":32,"trackNumberUpdateTime":1562742393612,"trackUpdateTime":1562760410271,"cloudTrackCount":0,"highQuality":false,"createTime":1357635084874,"subscribedCount":105707,"totalDuration":0,"coverImgUrl":"http://p2.music.126.net/Rgqbqsf4b3gNOzZKxOMxuw==/19029247741938160.jpg","commentThreadId":"A_PL_0_60131","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":48160,"description":"ORICONSTYLE CD单曲周榜，每周三更新，欢迎关注。","ordered":true,"adType":0,"name":"日本Oricon周榜","id":60131,"coverImgId_str":"19029247741938160"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周一更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":[],"status":0,"coverImgId":19187577416338508,"updateTime":1563162713261,"playCount":24248682,"trackCount":20,"trackNumberUpdateTime":1563162713166,"trackUpdateTime":1563162720329,"cloudTrackCount":0,"highQuality":false,"createTime":1359703138872,"subscribedCount":94644,"totalDuration":0,"coverImgUrl":"http: //p1.music.126.net/54vZEZ-fCudWZm6GH7I55w==/19187577416338508.jpg","commentThreadId":"A_PL_0_120001","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":48337,"description":"Hit FM Top 20 Countdown 第36期","ordered":true,"adType":0,"name":"Hit FM Top榜","id":120001,"coverImgId_str":"19187577416338508"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周一更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["流行"],"status":0,"coverImgId":18646617697286900,"updateTime":1563162202474,"playCount":10177708,"trackCount":6,"trackNumberUpdateTime":1563162202414,"trackUpdateTime":1563231017170,"cloudTrackCount":0,"highQuality":false,"createTime":1359690215675,"subscribedCount":27851,"totalDuration":0,"coverImgUrl":"http: //p1.music.126.net/wqi4TF4ILiTUUL5T7zhwsQ==/18646617697286899.jpg","commentThreadId":"A_PL_0_112463","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":48174,"description":"資料來源統計： \u203b唱片行銷售量佔20%(含玫瑰大眾、g-music、五大、佳佳、博客來等各大唱片行) \u203b數位音樂下載佔30%(含 iTunes、KK box、myMusic、Omusic、各鈴聲下載榜) \u203bHit Fm聯播網AIR PLAY電台播出率佔50%","ordered":true,"adType":0,"name":"台湾Hito排行榜","id":112463,"coverImgId_str":"18646617697286899"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周五更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["榜单"],"status":0,"coverImgId":18976471183805916,"updateTime":1562306508938,"playCount":23512984,"trackCount":13,"trackNumberUpdateTime":1560507522619,"trackUpdateTime":1562306508935,"cloudTrackCount":0,"highQuality":false,"createTime":1395305562352,"subscribedCount":79160,"totalDuration":0,"coverImgUrl":"http: //p1.music.126.net/YQsr07nkdkOyZrlAkf0SHA==/18976471183805915.jpg","commentThreadId":"A_PL_0_10169002","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":48330,"description":"","ordered":true,"adType":0,"name":"香港电台中文歌曲龙虎榜","id":10169002,"coverImgId_str":"18976471183805915"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周四更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":[],"status":0,"coverImgId":109951164091703580,"updateTime":1562809467182,"playCount":3976187,"trackCount":200,"trackNumberUpdateTime":1562809467007,"trackUpdateTime":1563249194363,"cloudTrackCount":0,"highQuality":false,"createTime":1558493373769,"subscribedCount":32276,"totalDuration":0,"coverImgUrl":"http://p1.music.126.net/c0iThrYPpnFVgFvU6JCVXQ==/109951164091703579.jpg","commentThreadId":"A_PL_0_2809513713","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":1,"description":"云音乐用户一周内收听所有欧美歌曲官方TOP排行榜，每周四更新。\nWestern Hit Chart (updated every Thursday)","ordered":true,"adType":0,"name":"云音乐欧美热歌榜","id":2809513713,"coverImgId_str":"109951164091703579"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每天更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":[],"status":0,"coverImgId":109951164091690480,"updateTime":1563241109982,"playCount":7140326,"trackCount":100,"trackNumberUpdateTime":1563241109871,"trackUpdateTime":1563259121198,"cloudTrackCount":0,"highQuality":false,"createTime":1558493214795,"subscribedCount":23163,"totalDuration":0,"coverImgUrl":"http: //p1.music.126.net/Zb8AL5xdl9-_7WIyAhRLbw==/109951164091690485.jpg","commentThreadId":"A_PL_0_2809577409","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":1,"description":"云音乐用户一周内收听所有欧美新歌（一月内最新发行）官方TOP排行榜，每天更新。\nWestern New Release Chart (new songs released in last 30 days, updated daily)\n","ordered":true,"adType":0,"name":"云音乐欧美新歌榜","id":2809577409,"coverImgId_str":"109951164091690485"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周四更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":[],"status":0,"coverImgId":109951164156360930,"updateTime":1562810317580,"playCount":2917918,"trackCount":10,"trackNumberUpdateTime":1562810317507,"trackUpdateTime":1562810318382,"cloudTrackCount":0,"highQuality":false,"createTime":1560758132139,"subscribedCount":7689,"totalDuration":0,"coverImgUrl":"http: //p1.music.126.net/J02EN4c-CtKy8n6yxzmz3w==/109951164156360925.jpg","commentThreadId":"A_PL_0_2847251561","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":1447243891,"description":"我要的音乐放肆听！网易云音乐重磅推出《中国新说唱2019》歌曲官方榜单。云音乐用户一周内收听《中国新说唱2019》所有歌曲官方TOP排行榜，每周四更新。","ordered":true,"adType":0,"name":"说唱TOP榜","id":2847251561,"coverImgId_str":"109951164156360925"},{"subscribers":[],"subscribed":null,"creator":null,"artists":null,"tracks":null,"updateFrequency":"每周五更新","backgroundCoverId":0,"backgroundCoverUrl":null,"tags":["榜单"],"status":0,"coverImgId":109951162873641550,"updateTime":1562903742107,"playCount":16789376,"trackCount":20,"trackNumberUpdateTime":1562903731568,"trackUpdateTime":1563084878972,"cloudTrackCount":0,"highQuality":false,"createTime":1409825013948,"subscribedCount":47789,"totalDuration":0,"coverImgUrl":"http://p1.music.126.net/6O0ZEnO-I_RADBylVypprg==/109951162873641556.jpg","commentThreadId":"A_PL_0_27135204","privacy":0,"newImported":false,"specialType":10,"anonimous":false,"userId":5190793,"description":"法国NRJ电台（national Radio de Jeunes）成立于1981年，总部位于法国巴黎。是法国最受欢迎的音乐电台和听众最多的广播电台之一。NRJ音乐奖素有法国的\u201c格莱美\u201d之称。此榜单针对NRJ电台法国本土热门歌曲排行。【每周五更新】","ordered":true,"adType":0,"name":"法国 NRJ Vos Hits 周榜","id":27135204,"coverImgId_str":"109951162873641556"}]
     * artistToplist : {"coverUrl":"http: //p1.music.126.net/MJdmNzZwTCz0b4IpHJV6Wg==/109951162865487429.jpg","name":"云音乐歌手榜","upateFrequency":"每天更新","position":5,"updateFrequency":"每天更新"}
     */

    private int code;
    private ArtistToplistBean artistToplist;
    private List<ListBean> list;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public ArtistToplistBean getArtistToplist() {
        return artistToplist;
    }

    public void setArtistToplist(ArtistToplistBean artistToplist) {
        this.artistToplist = artistToplist;
    }

    public List<ListBean> getList() {
        return list;
    }

    public void setList(List<ListBean> list) {
        this.list = list;
    }

    public static class ArtistToplistBean {
        /**
         * coverUrl : http: //p1.music.126.net/MJdmNzZwTCz0b4IpHJV6Wg==/109951162865487429.jpg
         * name : 云音乐歌手榜
         * upateFrequency : 每天更新
         * position : 5
         * updateFrequency : 每天更新
         */

        private String coverUrl;
        private String name;
        private String upateFrequency;
        private int position;
        private String updateFrequency;

        public String getCoverUrl() {
            return coverUrl;
        }

        public void setCoverUrl(String coverUrl) {
            this.coverUrl = coverUrl;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getUpateFrequency() {
            return upateFrequency;
        }

        public void setUpateFrequency(String upateFrequency) {
            this.upateFrequency = upateFrequency;
        }

        public int getPosition() {
            return position;
        }

        public void setPosition(int position) {
            this.position = position;
        }

        public String getUpdateFrequency() {
            return updateFrequency;
        }

        public void setUpdateFrequency(String updateFrequency) {
            this.updateFrequency = updateFrequency;
        }
    }

    public static class ListBean {
        /**
         * subscribers : []
         * subscribed : null
         * creator : null
         * artists : null
         * tracks : null
         * updateFrequency : 每天更新
         * backgroundCoverId : 0
         * backgroundCoverUrl : null
         * tags : []
         * status : 0
         * coverImgId : 18696095720518496
         * updateTime : 1563236699492
         * playCount : 2127742592
         * trackCount : 100
         * trackNumberUpdateTime : 1563236698663
         * trackUpdateTime : 1563265499687
         * cloudTrackCount : 0
         * highQuality : false
         * createTime : 1404115136883
         * subscribedCount : 2075315
         * totalDuration : 0
         * coverImgUrl : http://p1.music.126.net/DrRIg6CrgDfVLEph9SNh7w==/18696095720518497.jpg
         * commentThreadId : A_PL_0_19723756
         * privacy : 0
         * newImported : false
         * specialType : 10
         * anonimous : false
         * userId : 1
         * description : 云音乐中每天热度上升最快的100首单曲，每日更新。
         * ordered : true
         * adType : 0
         * name : 云音乐飙升榜
         * id : 19723756
         * coverImgId_str : 18696095720518497
         * ToplistType : S
         */

        private Object subscribed;
        private Object creator;
        private Object artists;
        private Object tracks;
        private String updateFrequency;
        private int backgroundCoverId;
        private Object backgroundCoverUrl;
        private int status;
        private long coverImgId;
        private long updateTime;
        private long playCount;
        private int trackCount;
        private long trackNumberUpdateTime;
        private long trackUpdateTime;
        private int cloudTrackCount;
        private boolean highQuality;
        private long createTime;
        private int subscribedCount;
        private int totalDuration;
        private String coverImgUrl;
        private String commentThreadId;
        private int privacy;
        private boolean newImported;
        private int specialType;
        private boolean anonimous;
        private long userId;
        private String description;
        private boolean ordered;
        private int adType;
        private String name;
        private long id;
        private String coverImgId_str;
        private String ToplistType;
        private List<?> subscribers;
        private List<?> tags;

        public Object getSubscribed() {
            return subscribed;
        }

        public void setSubscribed(Object subscribed) {
            this.subscribed = subscribed;
        }

        public Object getCreator() {
            return creator;
        }

        public void setCreator(Object creator) {
            this.creator = creator;
        }

        public Object getArtists() {
            return artists;
        }

        public void setArtists(Object artists) {
            this.artists = artists;
        }

        public Object getTracks() {
            return tracks;
        }

        public void setTracks(Object tracks) {
            this.tracks = tracks;
        }

        public String getUpdateFrequency() {
            return updateFrequency;
        }

        public void setUpdateFrequency(String updateFrequency) {
            this.updateFrequency = updateFrequency;
        }

        public int getBackgroundCoverId() {
            return backgroundCoverId;
        }

        public void setBackgroundCoverId(int backgroundCoverId) {
            this.backgroundCoverId = backgroundCoverId;
        }

        public Object getBackgroundCoverUrl() {
            return backgroundCoverUrl;
        }

        public void setBackgroundCoverUrl(Object backgroundCoverUrl) {
            this.backgroundCoverUrl = backgroundCoverUrl;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public long getCoverImgId() {
            return coverImgId;
        }

        public void setCoverImgId(long coverImgId) {
            this.coverImgId = coverImgId;
        }

        public long getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(long updateTime) {
            this.updateTime = updateTime;
        }

        public long getPlayCount() {
            return playCount;
        }

        public void setPlayCount(long playCount) {
            this.playCount = playCount;
        }

        public int getTrackCount() {
            return trackCount;
        }

        public void setTrackCount(int trackCount) {
            this.trackCount = trackCount;
        }

        public long getTrackNumberUpdateTime() {
            return trackNumberUpdateTime;
        }

        public void setTrackNumberUpdateTime(long trackNumberUpdateTime) {
            this.trackNumberUpdateTime = trackNumberUpdateTime;
        }

        public long getTrackUpdateTime() {
            return trackUpdateTime;
        }

        public void setTrackUpdateTime(long trackUpdateTime) {
            this.trackUpdateTime = trackUpdateTime;
        }

        public int getCloudTrackCount() {
            return cloudTrackCount;
        }

        public void setCloudTrackCount(int cloudTrackCount) {
            this.cloudTrackCount = cloudTrackCount;
        }

        public boolean isHighQuality() {
            return highQuality;
        }

        public void setHighQuality(boolean highQuality) {
            this.highQuality = highQuality;
        }

        public long getCreateTime() {
            return createTime;
        }

        public void setCreateTime(long createTime) {
            this.createTime = createTime;
        }

        public int getSubscribedCount() {
            return subscribedCount;
        }

        public void setSubscribedCount(int subscribedCount) {
            this.subscribedCount = subscribedCount;
        }

        public int getTotalDuration() {
            return totalDuration;
        }

        public void setTotalDuration(int totalDuration) {
            this.totalDuration = totalDuration;
        }

        public String getCoverImgUrl() {
            return coverImgUrl;
        }

        public void setCoverImgUrl(String coverImgUrl) {
            this.coverImgUrl = coverImgUrl;
        }

        public String getCommentThreadId() {
            return commentThreadId;
        }

        public void setCommentThreadId(String commentThreadId) {
            this.commentThreadId = commentThreadId;
        }

        public int getPrivacy() {
            return privacy;
        }

        public void setPrivacy(int privacy) {
            this.privacy = privacy;
        }

        public boolean isNewImported() {
            return newImported;
        }

        public void setNewImported(boolean newImported) {
            this.newImported = newImported;
        }

        public int getSpecialType() {
            return specialType;
        }

        public void setSpecialType(int specialType) {
            this.specialType = specialType;
        }

        public boolean isAnonimous() {
            return anonimous;
        }

        public void setAnonimous(boolean anonimous) {
            this.anonimous = anonimous;
        }

        public long getUserId() {
            return userId;
        }

        public void setUserId(long userId) {
            this.userId = userId;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public boolean isOrdered() {
            return ordered;
        }

        public void setOrdered(boolean ordered) {
            this.ordered = ordered;
        }

        public int getAdType() {
            return adType;
        }

        public void setAdType(int adType) {
            this.adType = adType;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public String getCoverImgId_str() {
            return coverImgId_str;
        }

        public void setCoverImgId_str(String coverImgId_str) {
            this.coverImgId_str = coverImgId_str;
        }

        public String getToplistType() {
            return ToplistType;
        }

        public void setToplistType(String ToplistType) {
            this.ToplistType = ToplistType;
        }

        public List<?> getSubscribers() {
            return subscribers;
        }

        public void setSubscribers(List<?> subscribers) {
            this.subscribers = subscribers;
        }

        public List<?> getTags() {
            return tags;
        }

        public void setTags(List<?> tags) {
            this.tags = tags;
        }
    }

    @Override
    public String toString() {
        return "TopListBean{" +
                "code=" + code +
                ", artistToplist=" + artistToplist +
                ", list=" + list +
                '}';
    }
}
