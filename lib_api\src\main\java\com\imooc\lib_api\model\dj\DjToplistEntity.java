package com.imooc.lib_api.model.dj;


import com.chad.library.adapter.base.entity.SectionEntity;

public class DjToplistEntity implements SectionEntity {

    private boolean isHeader;
    private String header;
    private DjRankListBean.List data;

    public DjToplistEntity(DjRankListBean.List list) {
        this.isHeader = false;
        this.data = list;
    }

    public DjToplistEntity(boolean isHeader, String header) {
        this.isHeader = isHeader;
        this.header = header;
    }

    @Override
    public boolean isHeader() {
        return isHeader;
    }

    public String getHeader() {
        return header;
    }

    public DjRankListBean.List getData() {
        return data;
    }
}
