<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/white">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="10dp"
        android:text="地区" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_mv_area_all"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="全部"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_mv_area_inland"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="内地"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_mv_area_gang"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="港台"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_mv_area_europe"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="欧美"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_mv_area_korea"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="韩国"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_mv_area_japan"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="日本"
            android:textSize="14sp" />
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="20dp"
        android:text="类型" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_mv_type_all"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="全部"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_mv_type_guan"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="官方版"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_mv_type_yuansheng"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="原声"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_mv_type_xianchang"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="现场版"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_mv_type_netease"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="网易出品"
            android:textSize="14sp" />
    </LinearLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="20dp"
        android:text="排序" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="20dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_mv_order_fast"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="上升最快"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_mv_order_new"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="最新"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_mv_order_hot"
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="最热"
            android:textSize="14sp" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="15dp"
        android:background="@color/app_background" />

    <TextView
        android:id="@+id/tv_mv_finish"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:gravity="center_horizontal"
        android:text="完成"
        android:textColor="#FE3A3C" />
</LinearLayout>