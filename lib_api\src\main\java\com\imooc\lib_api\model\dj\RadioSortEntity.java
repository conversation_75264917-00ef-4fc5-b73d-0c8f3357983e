package com.imooc.lib_api.model.dj;

import com.chad.library.adapter.base.entity.SectionEntity;
import com.imooc.lib_api.model.dj.DjCatelistBean;

public class RadioSortEntity implements SectionEntity {

    private boolean isHeader;
    private String header;
    private DjCatelistBean.CategoriesBean data;

    public RadioSortEntity(boolean isHeader, String header) {
        this.isHeader = isHeader;
        this.header = header;
    }

    public RadioSortEntity(DjCatelistBean.CategoriesBean bean) {
        this.isHeader = false;
        this.data = bean;
    }

    @Override
    public boolean isHeader() {
        return isHeader;
    }

    public String getHeader() {
        return header;
    }

    public DjCatelistBean.CategoriesBean getData() {
        return data;
    }
}
