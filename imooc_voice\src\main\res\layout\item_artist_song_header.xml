<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_collect_artist_song"
        android:layout_width="40dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="10dp"
        android:src="@drawable/ic_collect_gray" />

    <TextView
        android:id="@+id/tv_artist_song_header_num"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:layout_marginLeft="5dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:gravity="center_vertical"
        android:text="收藏热门50"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold" />
</LinearLayout>