
apply from: this.rootProject.file('imooc.gradle')

buildscript {
    repositories {
        google()
        mavenCentral()
        maven{ url 'https://maven.aliyun.com/repository/public/' }
        maven{ url 'https://maven.aliyun.com/repository/google' }
        maven{ url 'https://maven.aliyun.com/repository/jcenter' }
        maven {
            url "https://jitpack.io"
        }
        // 移除不安全的HTTP仓库，如果需要可以配置为HTTPS或添加allowInsecureProtocol
        // maven{
        //     url 'http://localhost:8081/repository/imooc_release/'
        //     allowInsecureProtocol = true
        //     credentials{
        //         username 'admin'
        //         password 'admin123'
        //     }
        // }
        // maven{
        //     url 'http://localhost:8081/repository/imooc_snapshots/'
        //     allowInsecureProtocol = true
        //     credentials{
        //         username 'admin'
        //         password 'admin123'
        //     }
        // }

    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.7.3'
        //greendao插件依赖
        classpath 'org.greenrobot:greendao-gradle-plugin:3.3.1'
        classpath 'com.jakewharton:butterknife-gradle-plugin:10.2.3'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven{ url 'https://maven.aliyun.com/repository/public/' }
        maven{ url 'https://maven.aliyun.com/repository/google' }
        maven{ url 'https://maven.aliyun.com/repository/jcenter' }
        maven {
            url "https://jitpack.io"
        }
        // 移除不安全的HTTP仓库，如果需要可以配置为HTTPS或添加allowInsecureProtocol
        // maven{
        //     url 'http://localhost:8081/repository/imooc_release/'
        //     allowInsecureProtocol = true
        //     credentials{
        //         username 'admin'
        //         password 'admin'
        //     }
        // }
        // maven{
        //     url 'http://localhost:8081/repository/imooc_snapshots/'
        //     allowInsecureProtocol = true
        //     credentials{
        //         username 'admin'
        //         password 'admin'
        //     }
        // }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
