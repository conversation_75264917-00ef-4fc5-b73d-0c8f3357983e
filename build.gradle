
apply from: this.rootProject.file('imooc.gradle')

buildscript {
    repositories {
        maven{ url 'https://maven.aliyun.com/repository/public/' }
        maven{ url 'https://maven.aliyun.com/repository/google' }
        maven{ url 'https://maven.aliyun.com/repository/jcenter' }
        google()
        jcenter()
        mavenCentral()
        maven{
            url 'http://localhost:8081/repository/imooc_release/'
            credentials{
                username 'admin'
                password 'admin123'
            }
        }
        maven {
            url "https://jitpack.io"
        }
        maven{
            url 'http://localhost:8081/repository/imooc_snapshots/'
            credentials{
                username 'admin'
                password 'admin123'
            }
        }
        
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.2.0'
        //greendao插件依赖
        classpath 'org.greenrobot:greendao-gradle-plugin:3.2.2'
        classpath 'com.jakewharton:butterknife-gradle-plugin:9.0.0-rc1'
    }
}

allprojects {
    repositories {
        maven{ url 'https://maven.aliyun.com/repository/public/' }
        maven{ url 'https://maven.aliyun.com/repository/google' }
        maven{ url 'https://maven.aliyun.com/repository/jcenter' }
        google()
        jcenter()
        mavenCentral()
        maven{
            url 'http://localhost:8081/repository/imooc_release/'
            credentials{
                username 'admin'
                password 'admin'
            }
        }
        maven {
            url "https://jitpack.io"
        }
        maven{
            url 'http://localhost:8081/repository/imooc_snapshots/'
            credentials{
                username 'admin'
                password 'admin'
            }
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
