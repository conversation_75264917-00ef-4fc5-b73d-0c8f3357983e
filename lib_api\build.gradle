apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.android.compileSdkVersion
    namespace 'com.imooc.lib_api'

    defaultConfig {
        minSdkVersion rootProject.android.minSdkVersion
        targetSdkVersion rootProject.android.targetSdkVersion
        versionCode rootProject.android.versionCode
        versionName rootProject.android.versionName
        multiDexEnabled rootProject.android.multiDexEnabled

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation this.rootProject.depsLibs.BaseRecyclerViewAdapterHelper
    implementation this.rootProject.depsLibs.greendao
    implementation project(path: ':lib_network')
}
