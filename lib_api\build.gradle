apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.android.compileSdkVersion

    defaultConfig {
        applicationId rootProject.android.applicationId
        minSdkVersion rootProject.android.minSdkVersion
        targetSdkVersion rootProject.android.targetSdkVersion
        versionCode rootProject.android.versionCode
        versionName rootProject.android.versionName
        multiDexEnabled rootProject.android.multiDexEnabled
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation this.rootProject.depsLibs.BaseRecyclerViewAdapterHelper
    implementation this.rootProject.depsLibs.greendao
    implementation project(path: ':lib_network')
}
