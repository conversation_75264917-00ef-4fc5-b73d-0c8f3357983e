<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"

        android:orientation="horizontal">

        <ImageView
            android:id="@+id/img_tab_back"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_margin="10dp"
            android:src="@drawable/ic_left_arrow_black" />

        <TextView
            android:id="@+id/tv_webcontainer_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="7"
            android:layout_gravity="center"
            android:ellipsize="marquee"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center_vertical"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:textColor="@color/black"
            android:layout_marginRight="5dp"
            android:textSize="18sp" />
        <ImageView
            android:id="@+id/img_tab_search"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="15dp"
            android:src="@drawable/ic_share_black" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/frame_web"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>