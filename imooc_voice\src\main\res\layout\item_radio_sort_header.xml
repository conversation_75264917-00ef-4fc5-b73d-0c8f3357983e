<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:id="@+id/item_radio_header_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="15dp"
        android:gravity="center_horizontal"
        android:textSize="15sp"
        android:text="热门分类"
        android:textColor="@color/black" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/app_background" />
</LinearLayout>