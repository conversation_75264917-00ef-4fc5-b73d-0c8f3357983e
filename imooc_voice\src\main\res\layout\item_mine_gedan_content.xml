<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="15dp"
    android:layout_marginTop="10dp"
    android:layout_marginRight="10dp"
    android:layout_marginBottom="10dp">

    <ImageView
        android:id="@+id/iv_item_gedan_content_img"
        android:layout_width="55dp"
        android:layout_height="55dp"
        android:layout_centerVertical="true"
        android:src="@drawable/ic_test" />

    <ImageView
        android:id="@+id/iv_album_right_flag"
        android:layout_width="10dp"
        android:layout_height="60dp"
        android:layout_toRightOf="@+id/iv_item_gedan_content_img"
        android:src="@drawable/ic_album_attach"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_item_gedan_content_toptext"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/iv_item_gedan_content_img"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="10dp"
        android:layout_toRightOf="@+id/iv_album_right_flag"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="我喜欢的音乐"
        android:textColor="@color/black"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_item_gedan_content_bottomtext"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_item_gedan_content_toptext"
        android:layout_alignLeft="@+id/tv_item_gedan_content_toptext"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="5dp"
        android:layout_toRightOf="@+id/iv_item_gedan_content_img"
        android:text="10首"
        android:textSize="11sp" />

    <ImageView
        android:id="@+id/iv_item_gedan_more"
        android:layout_width="15dp"
        android:layout_height="25dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:src="@drawable/ic_more_black" />
</RelativeLayout>