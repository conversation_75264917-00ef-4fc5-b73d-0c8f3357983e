package com.imooc.lib_api.model.playlist;

import java.util.List;

/**
 * 首页推荐歌单的ban
 */
public class MainRecommendPlayListBean {

    /**
     * code : 200
     * featureFirst : true
     * haveRcmdSongs : false
     * recommend : [{"id":**********,"type":1,"name":"[来杯下午茶] 忙碌之余来点音乐犒劳自己","copywriter":"猜你喜欢","picUrl":"https://p2.music.126.net/xKyXkJ9ZIqov2Frl8C6qwg==/109951164170712411.jpg","playcount":370077,"createTime":*************,"creator":{"description":"网易云音乐官方账号","accountStatus":0,"userId":1,"vipType":11,"province":110000,"avatarUrl":"https://p1.music.126.net/QWMV-Ru_6149AKe0mCBXKg==/****************.jpg","authStatus":1,"userType":2,"nickname":"网易云音乐","gender":1,"birthday":-*************,"city":110101,"avatarImgId":****************,"backgroundImgId":****************,"detailDescription":"网易云音乐官方账号","defaultAvatar":false,"expertTags":null,"djStatus":10,"followed":false,"backgroundUrl":"http://p1.music.126.net/pmHS4fcQtcNEGewNb5HRhg==/****************.jpg","avatarImgIdStr":"****************","backgroundImgIdStr":"****************","remarkName":null,"mutual":false,"signature":"网易云音乐是6亿人都在使用的音乐平台，致力于帮助用户发现音乐惊喜，帮助音乐人实现梦想。客服@云音乐客服 在线时间：9：00 - 24：00，如您在使用过程中遇到任何问题，欢迎私信咨询，我们会尽快回复。如果仍然不能解决您的问题，与活动相关的疑问请私信@云音乐客服","authority":3},"trackCount":0,"userId":1,"alg":"official_playlist_sceneRank"},{"id":**********,"type":1,"name":"『那些评论破万的日文歌』更新中","copywriter":"根据你可能喜欢的歌单《✯Reol『れをる』全曲目收录✯》推荐","picUrl":"https://p2.music.126.net/wzoTxcviowVd1E252TicCw==/*****************.jpg","playcount":9194239,"createTime":*************,"creator":{"description":"","accountStatus":0,"userId":*********,"vipType":10,"province":370000,"avatarUrl":"https://p1.music.126.net/6JgDIjM06RqRIIthT9XVyQ==/109951163630658121.jpg","authStatus":0,"userType":0,"nickname":"-可乐小橘子-","gender":2,"birthday":************,"city":370100,"avatarImgId":109951163630658130,"backgroundImgId":109951164137736420,"detailDescription":"","defaultAvatar":false,"expertTags":null,"djStatus":0,"followed":false,"backgroundUrl":"http://p1.music.126.net/3Ene3kwCYt4SPDmpDSaY3g==/109951164137736411.jpg","avatarImgIdStr":"109951163630658121","backgroundImgIdStr":"109951164137736411","remarkName":null,"mutual":false,"signature":"谢谢你们喜欢我鸭","authority":0},"trackCount":442,"userId":*********,"alg":"itembased2_withPlay"},{"id":**********,"type":1,"name":"90后经典歌曲回忆杀","copywriter":"根据你可能喜欢的歌单《怀旧 | 闲暇细数90后的回忆杀》推荐","picUrl":"https://p2.music.126.net/I_cNN2jccqMQXHgX5cTGRQ==/109951163626195097.jpg","playcount":4425719,"createTime":*************,"creator":{"description":"","accountStatus":0,"userId":*********,"vipType":0,"province":320000,"avatarUrl":"https://p1.music.126.net/N9YyRJOLrZz16Dup1Mx3oA==/109951164194492612.jpg","authStatus":0,"userType":0,"nickname":"柒月深","gender":2,"birthday":************,"city":320500,"avatarImgId":109951164194492600,"backgroundImgId":109951164171628460,"detailDescription":"","defaultAvatar":false,"expertTags":null,"djStatus":0,"followed":false,"backgroundUrl":"http://p1.music.126.net/U9WCP5ur6hEDwwsK3n465A==/109951164171628460.jpg","avatarImgIdStr":"109951164194492612","backgroundImgIdStr":"109951164171628460","remarkName":null,"mutual":false,"signature":"","authority":0},"trackCount":301,"userId":*********,"alg":"itembased2_withPlay"},{"id":**********,"type":1,"name":"90后经典老歌经久不衰*朗朗上口*恍若现场","copywriter":"根据你可能喜欢的歌单《怀旧 | 闲暇细数90后的回忆杀》推荐","picUrl":"https://p2.music.126.net/toffsI11SUvb5c8Si8-70Q==/109951163931866039.jpg","playcount":2500597,"createTime":*************,"creator":{"description":"","accountStatus":0,"userId":*********,"vipType":0,"province":330000,"avatarUrl":"https://p1.music.126.net/6oOtl2ignOX6-nlqRXFJmw==/109951163779857430.jpg","authStatus":0,"userType":0,"nickname":"独占的偏执","gender":0,"birthday":************,"city":330400,"avatarImgId":109951163779857420,"backgroundImgId":109951163779858860,"detailDescription":"","defaultAvatar":false,"expertTags":null,"djStatus":0,"followed":false,"backgroundUrl":"http://p1.music.126.net/6WDWL3B4s6oWIF4GwiCQew==/109951163779858870.jpg","avatarImgIdStr":"109951163779857430","backgroundImgIdStr":"109951163779858870","remarkName":null,"mutual":false,"signature":"","authority":0},"trackCount":199,"userId":*********,"alg":"itembased2_withPlay"},{"id":*********,"type":1,"name":"【全类型燃向BGM】GOD's love核爆音乐","copywriter":"根据你可能喜欢的歌单《一边散步一边听会感觉自己帅炸》推荐","picUrl":"https://p2.music.126.net/DWozf75gecWgThiLbjYJ-A==/****************.jpg","playcount":2289062,"createTime":*************,"creator":{"description":"","accountStatus":0,"userId":********,"vipType":0,"province":520000,"avatarUrl":"https://p1.music.126.net/cb8MCogebHMnh7PE7zfEYg==/****************.jpg","authStatus":0,"userType":0,"nickname":"浚焰","gender":1,"birthday":************,"city":520200,"avatarImgId":****************,"backgroundImgId":*****************,"detailDescription":"","defaultAvatar":false,"expertTags":null,"djStatus":0,"followed":false,"backgroundUrl":"http://p1.music.126.net/yCh2TUtUVuEJ0XNMgFyEHw==/*****************.jpg","avatarImgIdStr":"****************","backgroundImgIdStr":"*****************","remarkName":null,"mutual":false,"signature":"带上耳机的那一刻，感觉我他娘的就是世界霸主!","authority":0},"trackCount":2007,"userId":********,"alg":"itembased2_withPlay"},{"id":**********,"type":1,"name":"白金处刑曲各种变奏","copywriter":"根据你可能喜欢的歌单《「JOJO的奇妙冒险」OP ED OST》推荐","picUrl":"https://p2.music.126.net/S3liZdT3hhoNjIyXdmYAxA==/****************.jpg","playcount":456765,"createTime":*************,"creator":{"description":"","accountStatus":0,"userId":********,"vipType":0,"province":350000,"avatarUrl":"https://p1.music.126.net/XxfBUelg0iFk-yKBmGkVzw==/****************.jpg","authStatus":0,"userType":0,"nickname":"叫我肉丸兄","gender":1,"birthday":************,"city":350100,"avatarImgId":****************,"backgroundImgId":****************,"detailDescription":"","defaultAvatar":false,"expertTags":null,"djStatus":0,"followed":false,"backgroundUrl":"http://p1.music.126.net/o3G7lWrGBQAvSRt3UuApTw==/****************.jpg","avatarImgIdStr":"****************","backgroundImgIdStr":"****************","remarkName":null,"mutual":false,"signature":"只会玩游戏的咸鱼，没有猫的猫奴","authority":0},"trackCount":9,"userId":********,"alg":"itembased2_withPlay"},{"id":**********,"type":1,"name":"百部动画摇滚，百首经典旋律","copywriter":"根据你收藏的歌单《♪平成动画歌曲大奖/赏♪（百首全收录歌单）》推荐","picUrl":"https://p2.music.126.net/t05bMUzPZrKCtbrU38weEA==/109951163244646682.jpg","playcount":3837498,"createTime":*************,"creator":{"description":"","accountStatus":0,"userId":********,"vipType":11,"province":1000000,"avatarUrl":"https://p1.music.126.net/c2ItK8erM6S4mlsiZUR_qQ==/109951164210631266.jpg","authStatus":0,"userType":200,"nickname":"花嫁赫萝","gender":0,"birthday":************,"city":1010000,"avatarImgId":109951164210631260,"backgroundImgId":109951164209397580,"detailDescription":"","defaultAvatar":false,"expertTags":["ACG","爵士","摇滚"],"djStatus":10,"followed":false,"backgroundUrl":"http://p1.music.126.net/0fNOdV8kI-VNtfOV29f5rQ==/109951164209397590.jpg","avatarImgIdStr":"109951164210631266","backgroundImgIdStr":"109951164209397590","remarkName":null,"mutual":false,"signature":"败犬也要谈恋爱","authority":0},"trackCount":170,"userId":********,"alg":"itembased2"}]
     */

    private int code;
    private boolean featureFirst;
    private boolean haveRcmdSongs;
    private List<RecommendBean> recommend;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public boolean isFeatureFirst() {
        return featureFirst;
    }

    public void setFeatureFirst(boolean featureFirst) {
        this.featureFirst = featureFirst;
    }

    public boolean isHaveRcmdSongs() {
        return haveRcmdSongs;
    }

    public void setHaveRcmdSongs(boolean haveRcmdSongs) {
        this.haveRcmdSongs = haveRcmdSongs;
    }

    public List<RecommendBean> getRecommend() {
        return recommend;
    }

    public void setRecommend(List<RecommendBean> recommend) {
        this.recommend = recommend;
    }

    public static class RecommendBean {
        /**
         * id : **********
         * type : 1
         * name : [来杯下午茶] 忙碌之余来点音乐犒劳自己
         * copywriter : 猜你喜欢
         * picUrl : https://p2.music.126.net/xKyXkJ9ZIqov2Frl8C6qwg==/109951164170712411.jpg
         * playcount : 370077
         * createTime : *************
         * creator : {"description":"网易云音乐官方账号","accountStatus":0,"userId":1,"vipType":11,"province":110000,"avatarUrl":"https://p1.music.126.net/QWMV-Ru_6149AKe0mCBXKg==/****************.jpg","authStatus":1,"userType":2,"nickname":"网易云音乐","gender":1,"birthday":-*************,"city":110101,"avatarImgId":****************,"backgroundImgId":****************,"detailDescription":"网易云音乐官方账号","defaultAvatar":false,"expertTags":null,"djStatus":10,"followed":false,"backgroundUrl":"http://p1.music.126.net/pmHS4fcQtcNEGewNb5HRhg==/****************.jpg","avatarImgIdStr":"****************","backgroundImgIdStr":"****************","remarkName":null,"mutual":false,"signature":"网易云音乐是6亿人都在使用的音乐平台，致力于帮助用户发现音乐惊喜，帮助音乐人实现梦想。客服@云音乐客服 在线时间：9：00 - 24：00，如您在使用过程中遇到任何问题，欢迎私信咨询，我们会尽快回复。如果仍然不能解决您的问题，与活动相关的疑问请私信@云音乐客服","authority":3}
         * trackCount : 0
         * userId : 1
         * alg : official_playlist_sceneRank
         */

        private long id;
        private int type;
        private String name;
        private String copywriter;
        private String picUrl;
        private long playcount;
        private long createTime;
        private CreatorBean creator;
        private int trackCount;
        private int userId;
        private String alg;

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCopywriter() {
            return copywriter;
        }

        public void setCopywriter(String copywriter) {
            this.copywriter = copywriter;
        }

        public String getPicUrl() {
            return picUrl;
        }

        public void setPicUrl(String picUrl) {
            this.picUrl = picUrl;
        }

        public long getPlaycount() {
            return playcount;
        }

        public void setPlaycount(long playcount) {
            this.playcount = playcount;
        }

        public long getCreateTime() {
            return createTime;
        }

        public void setCreateTime(long createTime) {
            this.createTime = createTime;
        }

        public CreatorBean getCreator() {
            return creator;
        }

        public void setCreator(CreatorBean creator) {
            this.creator = creator;
        }

        public int getTrackCount() {
            return trackCount;
        }

        public void setTrackCount(int trackCount) {
            this.trackCount = trackCount;
        }

        public int getUserId() {
            return userId;
        }

        public void setUserId(int userId) {
            this.userId = userId;
        }

        public String getAlg() {
            return alg;
        }

        public void setAlg(String alg) {
            this.alg = alg;
        }

        public static class CreatorBean {
            /**
             * description : 网易云音乐官方账号
             * accountStatus : 0
             * userId : 1
             * vipType : 11
             * province : 110000
             * avatarUrl : https://p1.music.126.net/QWMV-Ru_6149AKe0mCBXKg==/****************.jpg
             * authStatus : 1
             * userType : 2
             * nickname : 网易云音乐
             * gender : 1
             * birthday : -*************
             * city : 110101
             * avatarImgId : ****************
             * backgroundImgId : ****************
             * detailDescription : 网易云音乐官方账号
             * defaultAvatar : false
             * expertTags : null
             * djStatus : 10
             * followed : false
             * backgroundUrl : http://p1.music.126.net/pmHS4fcQtcNEGewNb5HRhg==/****************.jpg
             * avatarImgIdStr : ****************
             * backgroundImgIdStr : ****************
             * remarkName : null
             * mutual : false
             * signature : 网易云音乐是6亿人都在使用的音乐平台，致力于帮助用户发现音乐惊喜，帮助音乐人实现梦想。客服@云音乐客服 在线时间：9：00 - 24：00，如您在使用过程中遇到任何问题，欢迎私信咨询，我们会尽快回复。如果仍然不能解决您的问题，与活动相关的疑问请私信@云音乐客服
             * authority : 3
             */

            private String description;
            private int accountStatus;
            private int userId;
            private int vipType;
            private int province;
            private String avatarUrl;
            private int authStatus;
            private int userType;
            private String nickname;
            private int gender;
            private long birthday;
            private int city;
            private long avatarImgId;
            private long backgroundImgId;
            private String detailDescription;
            private boolean defaultAvatar;
            private Object expertTags;
            private int djStatus;
            private boolean followed;
            private String backgroundUrl;
            private String avatarImgIdStr;
            private String backgroundImgIdStr;
            private Object remarkName;
            private boolean mutual;
            private String signature;
            private int authority;

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public int getAccountStatus() {
                return accountStatus;
            }

            public void setAccountStatus(int accountStatus) {
                this.accountStatus = accountStatus;
            }

            public int getUserId() {
                return userId;
            }

            public void setUserId(int userId) {
                this.userId = userId;
            }

            public int getVipType() {
                return vipType;
            }

            public void setVipType(int vipType) {
                this.vipType = vipType;
            }

            public int getProvince() {
                return province;
            }

            public void setProvince(int province) {
                this.province = province;
            }

            public String getAvatarUrl() {
                return avatarUrl;
            }

            public void setAvatarUrl(String avatarUrl) {
                this.avatarUrl = avatarUrl;
            }

            public int getAuthStatus() {
                return authStatus;
            }

            public void setAuthStatus(int authStatus) {
                this.authStatus = authStatus;
            }

            public int getUserType() {
                return userType;
            }

            public void setUserType(int userType) {
                this.userType = userType;
            }

            public String getNickname() {
                return nickname;
            }

            public void setNickname(String nickname) {
                this.nickname = nickname;
            }

            public int getGender() {
                return gender;
            }

            public void setGender(int gender) {
                this.gender = gender;
            }

            public long getBirthday() {
                return birthday;
            }

            public void setBirthday(long birthday) {
                this.birthday = birthday;
            }

            public int getCity() {
                return city;
            }

            public void setCity(int city) {
                this.city = city;
            }

            public long getAvatarImgId() {
                return avatarImgId;
            }

            public void setAvatarImgId(long avatarImgId) {
                this.avatarImgId = avatarImgId;
            }

            public long getBackgroundImgId() {
                return backgroundImgId;
            }

            public void setBackgroundImgId(long backgroundImgId) {
                this.backgroundImgId = backgroundImgId;
            }

            public String getDetailDescription() {
                return detailDescription;
            }

            public void setDetailDescription(String detailDescription) {
                this.detailDescription = detailDescription;
            }

            public boolean isDefaultAvatar() {
                return defaultAvatar;
            }

            public void setDefaultAvatar(boolean defaultAvatar) {
                this.defaultAvatar = defaultAvatar;
            }

            public Object getExpertTags() {
                return expertTags;
            }

            public void setExpertTags(Object expertTags) {
                this.expertTags = expertTags;
            }

            public int getDjStatus() {
                return djStatus;
            }

            public void setDjStatus(int djStatus) {
                this.djStatus = djStatus;
            }

            public boolean isFollowed() {
                return followed;
            }

            public void setFollowed(boolean followed) {
                this.followed = followed;
            }

            public String getBackgroundUrl() {
                return backgroundUrl;
            }

            public void setBackgroundUrl(String backgroundUrl) {
                this.backgroundUrl = backgroundUrl;
            }

            public String getAvatarImgIdStr() {
                return avatarImgIdStr;
            }

            public void setAvatarImgIdStr(String avatarImgIdStr) {
                this.avatarImgIdStr = avatarImgIdStr;
            }

            public String getBackgroundImgIdStr() {
                return backgroundImgIdStr;
            }

            public void setBackgroundImgIdStr(String backgroundImgIdStr) {
                this.backgroundImgIdStr = backgroundImgIdStr;
            }

            public Object getRemarkName() {
                return remarkName;
            }

            public void setRemarkName(Object remarkName) {
                this.remarkName = remarkName;
            }

            public boolean isMutual() {
                return mutual;
            }

            public void setMutual(boolean mutual) {
                this.mutual = mutual;
            }

            public String getSignature() {
                return signature;
            }

            public void setSignature(String signature) {
                this.signature = signature;
            }

            public int getAuthority() {
                return authority;
            }

            public void setAuthority(int authority) {
                this.authority = authority;
            }

        }

        @Override
        public String toString() {
            return "RecommendBean{" +
                    "id=" + id +
                    ", type=" + type +
                    ", name='" + name + '\'' +
                    ", copywriter='" + copywriter + '\'' +
                    ", picUrl='" + picUrl + '\'' +
                    ", playcount=" + playcount +
                    ", createTime=" + createTime +
                    ", creator=" + creator +
                    ", trackCount=" + trackCount +
                    ", userId=" + userId +
                    ", alg='" + alg + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "MainRecommendPlayListBean{" +
                "code=" + code +
                ", featureFirst=" + featureFirst +
                ", haveRcmdSongs=" + haveRcmdSongs +
                ", recommend=" + recommend +
                '}';
    }
}
