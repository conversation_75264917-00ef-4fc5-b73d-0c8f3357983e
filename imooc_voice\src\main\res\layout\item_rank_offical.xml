<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="5dp"
    android:paddingBottom="5dp">


    <ImageView
        android:id="@+id/iv_item_rank_cover_image"
        android:layout_width="110dp"
        android:layout_height="110dp"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:scaleType="centerCrop"
        android:src="@mipmap/ranklist_first" />

    <TextView
        android:id="@+id/tv_item_rank_update_frequency"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/iv_item_rank_cover_image"
        android:layout_alignBottom="@+id/iv_item_rank_cover_image"
        android:layout_centerHorizontal="true"
        android:layout_marginLeft="5dp"
        android:layout_marginBottom="5dp"
        android:gravity="center_horizontal"
        android:textSize="10sp"
        android:text="每周一更新"
        android:textColor="@color/white" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="5dp"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        android:layout_toRightOf="@+id/iv_item_rank_cover_image"
        android:orientation="vertical"
        android:padding="5dp">

        <TextView
            android:id="@+id/tv_item_rank_first_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="2dp"
            android:paddingBottom="8dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:text="1.知足"
            android:textColor="#7D7D7D"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_item_rank_second_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="2dp"
            android:paddingBottom="8dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:text="2.温柔"
            android:textColor="#7D7D7D"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_item_rank_third_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="2dp"
            android:paddingBottom="8dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:text="3.干杯"
            android:textColor="#7D7D7D"
            android:textSize="12sp" />


    </LinearLayout>

</RelativeLayout>