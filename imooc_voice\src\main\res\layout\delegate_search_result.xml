<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/img_search_result_back"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="10dp"
            android:src="@drawable/ic_left_arrow_black" />

        <EditText
            android:id="@+id/tv_search_result_text"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="15dp"
            android:layout_weight="7"
            android:background="@null"
            android:gravity="bottom"
            android:imeOptions="actionSearch"
            android:inputType="text"
            android:maxLines="1"
            android:paddingBottom="10dp"
            android:textColor="@color/black"
            android:textSize="16sp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="15dp"
        android:background="@color/darkgray" />

    <net.lucode.hackware.magicindicator.MagicIndicator
        android:id="@+id/magic_indicator_search_result"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="10dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginBottom="5dp"
        android:background="@color/app_background" />

    <android.support.v4.view.ViewPager
        android:id="@+id/vp_search_result"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="10" />
</LinearLayout>