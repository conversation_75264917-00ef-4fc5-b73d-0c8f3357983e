package com.imooc.lib_api.model.dj;

import java.util.List;

public class DjProgramBean {

    /**
     * count : 36
     * code : 200
     * programs : [{"mainSong":{"name":"Visual Studio Code - 吕鹏","id":530692704,"position":0,"alias":[],"status":0,"fee":0,"copyrightId":0,"disc":"","no":0,"artists":[{"name":"代码时间","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"[DJ节目]代码时间的DJ节目 第37期","id":0,"type":null,"size":0,"picId":109951163108762080,"blurPicUrl":"http://p2.music.126.net/DNJDC75rAZZkXWcuHSN5zA==/109951163108762078.jpg","companyId":0,"pic":109951163108762080,"picUrl":"http://p2.music.126.net/DNJDC75rAZZkXWcuHSN5zA==/109951163108762078.jpg","publishTime":0,"description":"","tags":"","company":null,"briefDesc":"","artist":{"name":"代码时间","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_0","artists":[{"name":"代码时间","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"picId_str":"109951163108762078"},"starred":false,"popularity":5,"score":5,"starredNum":0,"duration":3626266,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_530692704","rtUrl":null,"ftype":0,"rtUrls":[],"lMusic":{"name":"--","id":**********,"size":********,"extension":"-dj-","sr":44100,"dfsId":0,"bitrate":71000,"playTime":3626266,"volumeDelta":0},"mMusic":null,"hMusic":null,"rtype":0,"rurl":null,"mvid":0,"bMusic":{"name":"--","id":**********,"size":********,"extension":"-dj-","sr":44100,"dfsId":0,"bitrate":71000,"playTime":3626266,"volumeDelta":0},"mp3Url":null},"songs":null,"dj":{"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/BBK9kY31geOOdnkaxiwCdw==/****************.jpg","accountStatus":0,"gender":1,"city":1002400,"birthday":-*************,"userId":*********,"userType":0,"nickname":"代码时间","signature":"代码时间是一个面向程序员的中文播客节目, 致力于通过语音的方式传播程序员的正能量. 节目的网站是: http://codetimecn.com | 新浪微博 ID: 代码时间 | 微信公众号 ID: 代码时间","description":"","detailDescription":"","avatarImgId":****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"****************","backgroundImgIdStr":"****************","brand":"代码时间"},"blurCoverUrl":"http://music.163.com/api/dj/img/blur/109951163108762078","radio":{"dj":null,"category":"科技科学","buyed":false,"price":0,"originalPrice":0,"discountPrice":null,"purchaseCount":0,"lastProgramName":null,"videos":null,"finished":false,"underShelf":false,"liveInfo":null,"feeScope":0,"programCount":36,"subCount":15111,"radioFeeType":0,"lastProgramCreateTime":1515962230660,"lastProgramId":1367665101,"picUrl":"https://p1.music.126.net/BBK9kY31geOOdnkaxiwCdw==/****************.jpg","desc":"","categoryId":453052,"picId":****************,"createTime":1465726085482,"name":"代码时间","id":336355127,"subed":true},"duration":3626266,"buyed":false,"programDesc":null,"h5Links":null,"canReward":false,"auditStatus":0,"videoInfo":null,"score":0,"liveInfo":null,"alg":null,"titbits":null,"serialNum":36,"feeScope":0,"channels":[],"listenerCount":116267,"subscribedCount":0,"pubStatus":1,"coverUrl":"https://p1.music.126.net/DNJDC75rAZZkXWcuHSN5zA==/109951163108762078.jpg","bdAuditStatus":2,"reward":false,"mainTrackId":530692704,"programFeeType":0,"commentThreadId":"A_DJ_1_1367665101","smallLanguageAuditStatus":0,"titbitImages":null,"isPublish":true,"trackCount":0,"description":"什么是Visual Studio Code(VSC), 它和Visual Studio的关系\n为什么VSC能成为微软拥抱开源的影响力最大的两大项目之一 （另一个是Typescript）\nVSC吸引新手的几个特质\n为什么VSC有如此庞大且优秀的第三方插件\nVSC优秀的文档\nVSC团队如何保证稳定的新版本发布和内部的工作流程\nVSC团队是如何做测试的\n嘉宾工作从MSDN换到VSC工作性质的不同以及自己最大的转变\n嘉宾介绍VSC如何写插件\nVSC自动更新版本的需求\n[VSC githubo repo](https://github.com/Microsoft/vscode)\n[VSC官方插件教程](https://code.visualstudio.com/docs/extensions/overview)\n[Microsoft/language-server-protocol](https://github.com/Microsoft/language-server-protocol)\n[节目中谈到的VSC的Salesforce插件](https://github.com/forcedotcom/salesforcedx-vscode) \n[嘉宾的个人主页](rebornix.com)\n[嘉宾的.NET FM播客节目](http://dotnet.fm/)","createTime":1515962230660,"name":"Visual Studio Code - 吕鹏","id":1367665101,"shareCount":5,"subscribed":false,"likedCount":166,"commentCount":57}]
     * more : true
     */

    private int count;
    private int code;
    private boolean more;
    private List<ProgramsBean> programs;

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public boolean isMore() {
        return more;
    }

    public void setMore(boolean more) {
        this.more = more;
    }

    public List<ProgramsBean> getPrograms() {
        return programs;
    }

    public void setPrograms(List<ProgramsBean> programs) {
        this.programs = programs;
    }

    public static class ProgramsBean {
        /**
         * mainSong : {"name":"Visual Studio Code - 吕鹏","id":530692704,"position":0,"alias":[],"status":0,"fee":0,"copyrightId":0,"disc":"","no":0,"artists":[{"name":"代码时间","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"album":{"name":"[DJ节目]代码时间的DJ节目 第37期","id":0,"type":null,"size":0,"picId":109951163108762080,"blurPicUrl":"http://p2.music.126.net/DNJDC75rAZZkXWcuHSN5zA==/109951163108762078.jpg","companyId":0,"pic":109951163108762080,"picUrl":"http://p2.music.126.net/DNJDC75rAZZkXWcuHSN5zA==/109951163108762078.jpg","publishTime":0,"description":"","tags":"","company":null,"briefDesc":"","artist":{"name":"代码时间","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_0","artists":[{"name":"代码时间","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"picId_str":"109951163108762078"},"starred":false,"popularity":5,"score":5,"starredNum":0,"duration":3626266,"playedNum":0,"dayPlays":0,"hearTime":0,"ringtone":null,"crbt":null,"audition":null,"copyFrom":"","commentThreadId":"R_SO_4_530692704","rtUrl":null,"ftype":0,"rtUrls":[],"lMusic":{"name":"--","id":**********,"size":********,"extension":"-dj-","sr":44100,"dfsId":0,"bitrate":71000,"playTime":3626266,"volumeDelta":0},"mMusic":null,"hMusic":null,"rtype":0,"rurl":null,"mvid":0,"bMusic":{"name":"--","id":**********,"size":********,"extension":"-dj-","sr":44100,"dfsId":0,"bitrate":71000,"playTime":3626266,"volumeDelta":0},"mp3Url":null}
         * songs : null
         * dj : {"defaultAvatar":false,"province":1000000,"authStatus":0,"followed":false,"avatarUrl":"http://p1.music.126.net/BBK9kY31geOOdnkaxiwCdw==/****************.jpg","accountStatus":0,"gender":1,"city":1002400,"birthday":-*************,"userId":*********,"userType":0,"nickname":"代码时间","signature":"代码时间是一个面向程序员的中文播客节目, 致力于通过语音的方式传播程序员的正能量. 节目的网站是: http://codetimecn.com | 新浪微博 ID: 代码时间 | 微信公众号 ID: 代码时间","description":"","detailDescription":"","avatarImgId":****************,"backgroundImgId":****************,"backgroundUrl":"http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg","authority":0,"mutual":false,"expertTags":null,"experts":null,"djStatus":10,"vipType":0,"remarkName":null,"avatarImgIdStr":"****************","backgroundImgIdStr":"****************","brand":"代码时间"}
         * blurCoverUrl : http://music.163.com/api/dj/img/blur/109951163108762078
         * radio : {"dj":null,"category":"科技科学","buyed":false,"price":0,"originalPrice":0,"discountPrice":null,"purchaseCount":0,"lastProgramName":null,"videos":null,"finished":false,"underShelf":false,"liveInfo":null,"feeScope":0,"programCount":36,"subCount":15111,"radioFeeType":0,"lastProgramCreateTime":1515962230660,"lastProgramId":1367665101,"picUrl":"https://p1.music.126.net/BBK9kY31geOOdnkaxiwCdw==/****************.jpg","desc":"","categoryId":453052,"picId":****************,"createTime":1465726085482,"name":"代码时间","id":336355127,"subed":true}
         * duration : 3626266
         * buyed : false
         * programDesc : null
         * h5Links : null
         * canReward : false
         * auditStatus : 0
         * videoInfo : null
         * score : 0
         * liveInfo : null
         * alg : null
         * titbits : null
         * serialNum : 36
         * feeScope : 0
         * channels : []
         * listenerCount : 116267
         * subscribedCount : 0
         * pubStatus : 1
         * coverUrl : https://p1.music.126.net/DNJDC75rAZZkXWcuHSN5zA==/109951163108762078.jpg
         * bdAuditStatus : 2
         * reward : false
         * mainTrackId : 530692704
         * programFeeType : 0
         * commentThreadId : A_DJ_1_1367665101
         * smallLanguageAuditStatus : 0
         * titbitImages : null
         * isPublish : true
         * trackCount : 0
         * description : 什么是Visual Studio Code(VSC), 它和Visual Studio的关系
         * 为什么VSC能成为微软拥抱开源的影响力最大的两大项目之一 （另一个是Typescript）
         * VSC吸引新手的几个特质
         * 为什么VSC有如此庞大且优秀的第三方插件
         * VSC优秀的文档
         * VSC团队如何保证稳定的新版本发布和内部的工作流程
         * VSC团队是如何做测试的
         * 嘉宾工作从MSDN换到VSC工作性质的不同以及自己最大的转变
         * 嘉宾介绍VSC如何写插件
         * VSC自动更新版本的需求
         * [VSC githubo repo](https://github.com/Microsoft/vscode)
         * [VSC官方插件教程](https://code.visualstudio.com/docs/extensions/overview)
         * [Microsoft/language-server-protocol](https://github.com/Microsoft/language-server-protocol)
         * [节目中谈到的VSC的Salesforce插件](https://github.com/forcedotcom/salesforcedx-vscode)
         * [嘉宾的个人主页](rebornix.com)
         * [嘉宾的.NET FM播客节目](http://dotnet.fm/)
         * createTime : 1515962230660
         * name : Visual Studio Code - 吕鹏
         * id : 1367665101
         * shareCount : 5
         * subscribed : false
         * likedCount : 166
         * commentCount : 57
         */

        private MainSongBean mainSong;
        private Object songs;
        private DjBean dj;
        private String blurCoverUrl;
        private RadioBean radio;
        private int duration;
        private boolean buyed;
        private Object programDesc;
        private Object h5Links;
        private boolean canReward;
        private int auditStatus;
        private Object videoInfo;
        private int score;
        private Object liveInfo;
        private Object alg;
        private Object titbits;
        private int serialNum;
        private int feeScope;
        private int listenerCount;
        private int subscribedCount;
        private int pubStatus;
        private String coverUrl;
        private int bdAuditStatus;
        private boolean reward;
        private int mainTrackId;
        private int programFeeType;
        private String commentThreadId;
        private int smallLanguageAuditStatus;
        private Object titbitImages;
        private boolean isPublish;
        private int trackCount;
        private String description;
        private long createTime;
        private String name;
        private long id;
        private int shareCount;
        private boolean subscribed;
        private int likedCount;
        private int commentCount;
        private List<?> channels;

        public MainSongBean getMainSong() {
            return mainSong;
        }

        public void setMainSong(MainSongBean mainSong) {
            this.mainSong = mainSong;
        }

        public Object getSongs() {
            return songs;
        }

        public void setSongs(Object songs) {
            this.songs = songs;
        }

        public DjBean getDj() {
            return dj;
        }

        public void setDj(DjBean dj) {
            this.dj = dj;
        }

        public String getBlurCoverUrl() {
            return blurCoverUrl;
        }

        public void setBlurCoverUrl(String blurCoverUrl) {
            this.blurCoverUrl = blurCoverUrl;
        }

        public RadioBean getRadio() {
            return radio;
        }

        public void setRadio(RadioBean radio) {
            this.radio = radio;
        }

        public int getDuration() {
            return duration;
        }

        public void setDuration(int duration) {
            this.duration = duration;
        }

        public boolean isBuyed() {
            return buyed;
        }

        public void setBuyed(boolean buyed) {
            this.buyed = buyed;
        }

        public Object getProgramDesc() {
            return programDesc;
        }

        public void setProgramDesc(Object programDesc) {
            this.programDesc = programDesc;
        }

        public Object getH5Links() {
            return h5Links;
        }

        public void setH5Links(Object h5Links) {
            this.h5Links = h5Links;
        }

        public boolean isCanReward() {
            return canReward;
        }

        public void setCanReward(boolean canReward) {
            this.canReward = canReward;
        }

        public int getAuditStatus() {
            return auditStatus;
        }

        public void setAuditStatus(int auditStatus) {
            this.auditStatus = auditStatus;
        }

        public Object getVideoInfo() {
            return videoInfo;
        }

        public void setVideoInfo(Object videoInfo) {
            this.videoInfo = videoInfo;
        }

        public int getScore() {
            return score;
        }

        public void setScore(int score) {
            this.score = score;
        }

        public Object getLiveInfo() {
            return liveInfo;
        }

        public void setLiveInfo(Object liveInfo) {
            this.liveInfo = liveInfo;
        }

        public Object getAlg() {
            return alg;
        }

        public void setAlg(Object alg) {
            this.alg = alg;
        }

        public Object getTitbits() {
            return titbits;
        }

        public void setTitbits(Object titbits) {
            this.titbits = titbits;
        }

        public int getSerialNum() {
            return serialNum;
        }

        public void setSerialNum(int serialNum) {
            this.serialNum = serialNum;
        }

        public int getFeeScope() {
            return feeScope;
        }

        public void setFeeScope(int feeScope) {
            this.feeScope = feeScope;
        }

        public int getListenerCount() {
            return listenerCount;
        }

        public void setListenerCount(int listenerCount) {
            this.listenerCount = listenerCount;
        }

        public int getSubscribedCount() {
            return subscribedCount;
        }

        public void setSubscribedCount(int subscribedCount) {
            this.subscribedCount = subscribedCount;
        }

        public int getPubStatus() {
            return pubStatus;
        }

        public void setPubStatus(int pubStatus) {
            this.pubStatus = pubStatus;
        }

        public String getCoverUrl() {
            return coverUrl;
        }

        public void setCoverUrl(String coverUrl) {
            this.coverUrl = coverUrl;
        }

        public int getBdAuditStatus() {
            return bdAuditStatus;
        }

        public void setBdAuditStatus(int bdAuditStatus) {
            this.bdAuditStatus = bdAuditStatus;
        }

        public boolean isReward() {
            return reward;
        }

        public void setReward(boolean reward) {
            this.reward = reward;
        }

        public int getMainTrackId() {
            return mainTrackId;
        }

        public void setMainTrackId(int mainTrackId) {
            this.mainTrackId = mainTrackId;
        }

        public int getProgramFeeType() {
            return programFeeType;
        }

        public void setProgramFeeType(int programFeeType) {
            this.programFeeType = programFeeType;
        }

        public String getCommentThreadId() {
            return commentThreadId;
        }

        public void setCommentThreadId(String commentThreadId) {
            this.commentThreadId = commentThreadId;
        }

        public int getSmallLanguageAuditStatus() {
            return smallLanguageAuditStatus;
        }

        public void setSmallLanguageAuditStatus(int smallLanguageAuditStatus) {
            this.smallLanguageAuditStatus = smallLanguageAuditStatus;
        }

        public Object getTitbitImages() {
            return titbitImages;
        }

        public void setTitbitImages(Object titbitImages) {
            this.titbitImages = titbitImages;
        }

        public boolean isIsPublish() {
            return isPublish;
        }

        public void setIsPublish(boolean isPublish) {
            this.isPublish = isPublish;
        }

        public int getTrackCount() {
            return trackCount;
        }

        public void setTrackCount(int trackCount) {
            this.trackCount = trackCount;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public long getCreateTime() {
            return createTime;
        }

        public void setCreateTime(long createTime) {
            this.createTime = createTime;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public int getShareCount() {
            return shareCount;
        }

        public void setShareCount(int shareCount) {
            this.shareCount = shareCount;
        }

        public boolean isSubscribed() {
            return subscribed;
        }

        public void setSubscribed(boolean subscribed) {
            this.subscribed = subscribed;
        }

        public int getLikedCount() {
            return likedCount;
        }

        public void setLikedCount(int likedCount) {
            this.likedCount = likedCount;
        }

        public int getCommentCount() {
            return commentCount;
        }

        public void setCommentCount(int commentCount) {
            this.commentCount = commentCount;
        }

        public List<?> getChannels() {
            return channels;
        }

        public void setChannels(List<?> channels) {
            this.channels = channels;
        }

        public static class MainSongBean {
            /**
             * name : Visual Studio Code - 吕鹏
             * id : 530692704
             * position : 0
             * alias : []
             * status : 0
             * fee : 0
             * copyrightId : 0
             * disc :
             * no : 0
             * artists : [{"name":"代码时间","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]
             * album : {"name":"[DJ节目]代码时间的DJ节目 第37期","id":0,"type":null,"size":0,"picId":109951163108762080,"blurPicUrl":"http://p2.music.126.net/DNJDC75rAZZkXWcuHSN5zA==/109951163108762078.jpg","companyId":0,"pic":109951163108762080,"picUrl":"http://p2.music.126.net/DNJDC75rAZZkXWcuHSN5zA==/109951163108762078.jpg","publishTime":0,"description":"","tags":"","company":null,"briefDesc":"","artist":{"name":"代码时间","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0},"songs":[],"alias":[],"status":0,"copyrightId":0,"commentThreadId":"R_AL_3_0","artists":[{"name":"代码时间","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}],"picId_str":"109951163108762078"}
             * starred : false
             * popularity : 5
             * score : 5
             * starredNum : 0
             * duration : 3626266
             * playedNum : 0
             * dayPlays : 0
             * hearTime : 0
             * ringtone : null
             * crbt : null
             * audition : null
             * copyFrom :
             * commentThreadId : R_SO_4_530692704
             * rtUrl : null
             * ftype : 0
             * rtUrls : []
             * lMusic : {"name":"--","id":**********,"size":********,"extension":"-dj-","sr":44100,"dfsId":0,"bitrate":71000,"playTime":3626266,"volumeDelta":0}
             * mMusic : null
             * hMusic : null
             * rtype : 0
             * rurl : null
             * mvid : 0
             * bMusic : {"name":"--","id":**********,"size":********,"extension":"-dj-","sr":44100,"dfsId":0,"bitrate":71000,"playTime":3626266,"volumeDelta":0}
             * mp3Url : null
             */

            private String name;
            private long id;
            private int position;
            private int status;
            private int fee;
            private int copyrightId;
            private String disc;
            private int no;
            private AlbumBean album;
            private boolean starred;
            private int popularity;
            private int score;
            private int starredNum;
            private int duration;
            private int playedNum;
            private int dayPlays;
            private int hearTime;
            private Object ringtone;
            private Object crbt;
            private Object audition;
            private String copyFrom;
            private String commentThreadId;
            private Object rtUrl;
            private int ftype;
            private LMusicBean lMusic;
            private Object mMusic;
            private Object hMusic;
            private int rtype;
            private Object rurl;
            private int mvid;
            private BMusicBean bMusic;
            private Object mp3Url;
            private List<?> alias;
            private List<ArtistsBeanX> artists;
            private List<?> rtUrls;

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public long getId() {
                return id;
            }

            public void setId(long id) {
                this.id = id;
            }

            public int getPosition() {
                return position;
            }

            public void setPosition(int position) {
                this.position = position;
            }

            public int getStatus() {
                return status;
            }

            public void setStatus(int status) {
                this.status = status;
            }

            public int getFee() {
                return fee;
            }

            public void setFee(int fee) {
                this.fee = fee;
            }

            public int getCopyrightId() {
                return copyrightId;
            }

            public void setCopyrightId(int copyrightId) {
                this.copyrightId = copyrightId;
            }

            public String getDisc() {
                return disc;
            }

            public void setDisc(String disc) {
                this.disc = disc;
            }

            public int getNo() {
                return no;
            }

            public void setNo(int no) {
                this.no = no;
            }

            public AlbumBean getAlbum() {
                return album;
            }

            public void setAlbum(AlbumBean album) {
                this.album = album;
            }

            public boolean isStarred() {
                return starred;
            }

            public void setStarred(boolean starred) {
                this.starred = starred;
            }

            public int getPopularity() {
                return popularity;
            }

            public void setPopularity(int popularity) {
                this.popularity = popularity;
            }

            public int getScore() {
                return score;
            }

            public void setScore(int score) {
                this.score = score;
            }

            public int getStarredNum() {
                return starredNum;
            }

            public void setStarredNum(int starredNum) {
                this.starredNum = starredNum;
            }

            public int getDuration() {
                return duration;
            }

            public void setDuration(int duration) {
                this.duration = duration;
            }

            public int getPlayedNum() {
                return playedNum;
            }

            public void setPlayedNum(int playedNum) {
                this.playedNum = playedNum;
            }

            public int getDayPlays() {
                return dayPlays;
            }

            public void setDayPlays(int dayPlays) {
                this.dayPlays = dayPlays;
            }

            public int getHearTime() {
                return hearTime;
            }

            public void setHearTime(int hearTime) {
                this.hearTime = hearTime;
            }

            public Object getRingtone() {
                return ringtone;
            }

            public void setRingtone(Object ringtone) {
                this.ringtone = ringtone;
            }

            public Object getCrbt() {
                return crbt;
            }

            public void setCrbt(Object crbt) {
                this.crbt = crbt;
            }

            public Object getAudition() {
                return audition;
            }

            public void setAudition(Object audition) {
                this.audition = audition;
            }

            public String getCopyFrom() {
                return copyFrom;
            }

            public void setCopyFrom(String copyFrom) {
                this.copyFrom = copyFrom;
            }

            public String getCommentThreadId() {
                return commentThreadId;
            }

            public void setCommentThreadId(String commentThreadId) {
                this.commentThreadId = commentThreadId;
            }

            public Object getRtUrl() {
                return rtUrl;
            }

            public void setRtUrl(Object rtUrl) {
                this.rtUrl = rtUrl;
            }

            public int getFtype() {
                return ftype;
            }

            public void setFtype(int ftype) {
                this.ftype = ftype;
            }

            public LMusicBean getLMusic() {
                return lMusic;
            }

            public void setLMusic(LMusicBean lMusic) {
                this.lMusic = lMusic;
            }

            public Object getMMusic() {
                return mMusic;
            }

            public void setMMusic(Object mMusic) {
                this.mMusic = mMusic;
            }

            public Object getHMusic() {
                return hMusic;
            }

            public void setHMusic(Object hMusic) {
                this.hMusic = hMusic;
            }

            public int getRtype() {
                return rtype;
            }

            public void setRtype(int rtype) {
                this.rtype = rtype;
            }

            public Object getRurl() {
                return rurl;
            }

            public void setRurl(Object rurl) {
                this.rurl = rurl;
            }

            public int getMvid() {
                return mvid;
            }

            public void setMvid(int mvid) {
                this.mvid = mvid;
            }

            public BMusicBean getBMusic() {
                return bMusic;
            }

            public void setBMusic(BMusicBean bMusic) {
                this.bMusic = bMusic;
            }

            public Object getMp3Url() {
                return mp3Url;
            }

            public void setMp3Url(Object mp3Url) {
                this.mp3Url = mp3Url;
            }

            public List<?> getAlias() {
                return alias;
            }

            public void setAlias(List<?> alias) {
                this.alias = alias;
            }

            public List<ArtistsBeanX> getArtists() {
                return artists;
            }

            public void setArtists(List<ArtistsBeanX> artists) {
                this.artists = artists;
            }

            public List<?> getRtUrls() {
                return rtUrls;
            }

            public void setRtUrls(List<?> rtUrls) {
                this.rtUrls = rtUrls;
            }

            public static class AlbumBean {
                /**
                 * name : [DJ节目]代码时间的DJ节目 第37期
                 * id : 0
                 * type : null
                 * size : 0
                 * picId : 109951163108762080
                 * blurPicUrl : http://p2.music.126.net/DNJDC75rAZZkXWcuHSN5zA==/109951163108762078.jpg
                 * companyId : 0
                 * pic : 109951163108762080
                 * picUrl : http://p2.music.126.net/DNJDC75rAZZkXWcuHSN5zA==/109951163108762078.jpg
                 * publishTime : 0
                 * description :
                 * tags :
                 * company : null
                 * briefDesc :
                 * artist : {"name":"代码时间","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}
                 * songs : []
                 * alias : []
                 * status : 0
                 * copyrightId : 0
                 * commentThreadId : R_AL_3_0
                 * artists : [{"name":"代码时间","id":0,"picId":0,"img1v1Id":0,"briefDesc":"","picUrl":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","img1v1Url":"http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg","albumSize":0,"alias":[],"trans":"","musicSize":0}]
                 * picId_str : 109951163108762078
                 */

                private String name;
                private int id;
                private Object type;
                private int size;
                private long picId;
                private String blurPicUrl;
                private int companyId;
                private long pic;
                private String picUrl;
                private int publishTime;
                private String description;
                private String tags;
                private Object company;
                private String briefDesc;
                private ArtistBean artist;
                private int status;
                private int copyrightId;
                private String commentThreadId;
                private String picId_str;
                private List<?> songs;
                private List<?> alias;
                private List<ArtistsBean> artists;

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public int getId() {
                    return id;
                }

                public void setId(int id) {
                    this.id = id;
                }

                public Object getType() {
                    return type;
                }

                public void setType(Object type) {
                    this.type = type;
                }

                public int getSize() {
                    return size;
                }

                public void setSize(int size) {
                    this.size = size;
                }

                public long getPicId() {
                    return picId;
                }

                public void setPicId(long picId) {
                    this.picId = picId;
                }

                public String getBlurPicUrl() {
                    return blurPicUrl;
                }

                public void setBlurPicUrl(String blurPicUrl) {
                    this.blurPicUrl = blurPicUrl;
                }

                public int getCompanyId() {
                    return companyId;
                }

                public void setCompanyId(int companyId) {
                    this.companyId = companyId;
                }

                public long getPic() {
                    return pic;
                }

                public void setPic(long pic) {
                    this.pic = pic;
                }

                public String getPicUrl() {
                    return picUrl;
                }

                public void setPicUrl(String picUrl) {
                    this.picUrl = picUrl;
                }

                public int getPublishTime() {
                    return publishTime;
                }

                public void setPublishTime(int publishTime) {
                    this.publishTime = publishTime;
                }

                public String getDescription() {
                    return description;
                }

                public void setDescription(String description) {
                    this.description = description;
                }

                public String getTags() {
                    return tags;
                }

                public void setTags(String tags) {
                    this.tags = tags;
                }

                public Object getCompany() {
                    return company;
                }

                public void setCompany(Object company) {
                    this.company = company;
                }

                public String getBriefDesc() {
                    return briefDesc;
                }

                public void setBriefDesc(String briefDesc) {
                    this.briefDesc = briefDesc;
                }

                public ArtistBean getArtist() {
                    return artist;
                }

                public void setArtist(ArtistBean artist) {
                    this.artist = artist;
                }

                public int getStatus() {
                    return status;
                }

                public void setStatus(int status) {
                    this.status = status;
                }

                public int getCopyrightId() {
                    return copyrightId;
                }

                public void setCopyrightId(int copyrightId) {
                    this.copyrightId = copyrightId;
                }

                public String getCommentThreadId() {
                    return commentThreadId;
                }

                public void setCommentThreadId(String commentThreadId) {
                    this.commentThreadId = commentThreadId;
                }

                public String getPicId_str() {
                    return picId_str;
                }

                public void setPicId_str(String picId_str) {
                    this.picId_str = picId_str;
                }

                public List<?> getSongs() {
                    return songs;
                }

                public void setSongs(List<?> songs) {
                    this.songs = songs;
                }

                public List<?> getAlias() {
                    return alias;
                }

                public void setAlias(List<?> alias) {
                    this.alias = alias;
                }

                public List<ArtistsBean> getArtists() {
                    return artists;
                }

                public void setArtists(List<ArtistsBean> artists) {
                    this.artists = artists;
                }

                public static class ArtistBean {
                    /**
                     * name : 代码时间
                     * id : 0
                     * picId : 0
                     * img1v1Id : 0
                     * briefDesc :
                     * picUrl : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                     * img1v1Url : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                     * albumSize : 0
                     * alias : []
                     * trans :
                     * musicSize : 0
                     */

                    private String name;
                    private int id;
                    private int picId;
                    private int img1v1Id;
                    private String briefDesc;
                    private String picUrl;
                    private String img1v1Url;
                    private int albumSize;
                    private String trans;
                    private int musicSize;
                    private List<?> alias;

                    public String getName() {
                        return name;
                    }

                    public void setName(String name) {
                        this.name = name;
                    }

                    public int getId() {
                        return id;
                    }

                    public void setId(int id) {
                        this.id = id;
                    }

                    public int getPicId() {
                        return picId;
                    }

                    public void setPicId(int picId) {
                        this.picId = picId;
                    }

                    public int getImg1v1Id() {
                        return img1v1Id;
                    }

                    public void setImg1v1Id(int img1v1Id) {
                        this.img1v1Id = img1v1Id;
                    }

                    public String getBriefDesc() {
                        return briefDesc;
                    }

                    public void setBriefDesc(String briefDesc) {
                        this.briefDesc = briefDesc;
                    }

                    public String getPicUrl() {
                        return picUrl;
                    }

                    public void setPicUrl(String picUrl) {
                        this.picUrl = picUrl;
                    }

                    public String getImg1v1Url() {
                        return img1v1Url;
                    }

                    public void setImg1v1Url(String img1v1Url) {
                        this.img1v1Url = img1v1Url;
                    }

                    public int getAlbumSize() {
                        return albumSize;
                    }

                    public void setAlbumSize(int albumSize) {
                        this.albumSize = albumSize;
                    }

                    public String getTrans() {
                        return trans;
                    }

                    public void setTrans(String trans) {
                        this.trans = trans;
                    }

                    public int getMusicSize() {
                        return musicSize;
                    }

                    public void setMusicSize(int musicSize) {
                        this.musicSize = musicSize;
                    }

                    public List<?> getAlias() {
                        return alias;
                    }

                    public void setAlias(List<?> alias) {
                        this.alias = alias;
                    }
                }

                public static class ArtistsBean {
                    /**
                     * name : 代码时间
                     * id : 0
                     * picId : 0
                     * img1v1Id : 0
                     * briefDesc :
                     * picUrl : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                     * img1v1Url : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                     * albumSize : 0
                     * alias : []
                     * trans :
                     * musicSize : 0
                     */

                    private String name;
                    private int id;
                    private int picId;
                    private int img1v1Id;
                    private String briefDesc;
                    private String picUrl;
                    private String img1v1Url;
                    private int albumSize;
                    private String trans;
                    private int musicSize;
                    private List<?> alias;

                    public String getName() {
                        return name;
                    }

                    public void setName(String name) {
                        this.name = name;
                    }

                    public int getId() {
                        return id;
                    }

                    public void setId(int id) {
                        this.id = id;
                    }

                    public int getPicId() {
                        return picId;
                    }

                    public void setPicId(int picId) {
                        this.picId = picId;
                    }

                    public int getImg1v1Id() {
                        return img1v1Id;
                    }

                    public void setImg1v1Id(int img1v1Id) {
                        this.img1v1Id = img1v1Id;
                    }

                    public String getBriefDesc() {
                        return briefDesc;
                    }

                    public void setBriefDesc(String briefDesc) {
                        this.briefDesc = briefDesc;
                    }

                    public String getPicUrl() {
                        return picUrl;
                    }

                    public void setPicUrl(String picUrl) {
                        this.picUrl = picUrl;
                    }

                    public String getImg1v1Url() {
                        return img1v1Url;
                    }

                    public void setImg1v1Url(String img1v1Url) {
                        this.img1v1Url = img1v1Url;
                    }

                    public int getAlbumSize() {
                        return albumSize;
                    }

                    public void setAlbumSize(int albumSize) {
                        this.albumSize = albumSize;
                    }

                    public String getTrans() {
                        return trans;
                    }

                    public void setTrans(String trans) {
                        this.trans = trans;
                    }

                    public int getMusicSize() {
                        return musicSize;
                    }

                    public void setMusicSize(int musicSize) {
                        this.musicSize = musicSize;
                    }

                    public List<?> getAlias() {
                        return alias;
                    }

                    public void setAlias(List<?> alias) {
                        this.alias = alias;
                    }
                }
            }

            public static class LMusicBean {
                /**
                 * name : --
                 * id : **********
                 * size : ********
                 * extension : -dj-
                 * sr : 44100
                 * dfsId : 0
                 * bitrate : 71000
                 * playTime : 3626266
                 * volumeDelta : 0
                 */

                private String name;
                private long id;
                private int size;
                private String extension;
                private int sr;
                private int dfsId;
                private int bitrate;
                private int playTime;
                private double volumeDelta;

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public long getId() {
                    return id;
                }

                public void setId(long id) {
                    this.id = id;
                }

                public int getSize() {
                    return size;
                }

                public void setSize(int size) {
                    this.size = size;
                }

                public String getExtension() {
                    return extension;
                }

                public void setExtension(String extension) {
                    this.extension = extension;
                }

                public int getSr() {
                    return sr;
                }

                public void setSr(int sr) {
                    this.sr = sr;
                }

                public int getDfsId() {
                    return dfsId;
                }

                public void setDfsId(int dfsId) {
                    this.dfsId = dfsId;
                }

                public int getBitrate() {
                    return bitrate;
                }

                public void setBitrate(int bitrate) {
                    this.bitrate = bitrate;
                }

                public int getPlayTime() {
                    return playTime;
                }

                public void setPlayTime(int playTime) {
                    this.playTime = playTime;
                }

                public double getVolumeDelta() {
                    return volumeDelta;
                }

                public void setVolumeDelta(double volumeDelta) {
                    this.volumeDelta = volumeDelta;
                }
            }

            public static class BMusicBean {
                /**
                 * name : --
                 * id : **********
                 * size : ********
                 * extension : -dj-
                 * sr : 44100
                 * dfsId : 0
                 * bitrate : 71000
                 * playTime : 3626266
                 * volumeDelta : 0
                 */

                private String name;
                private long id;
                private int size;
                private String extension;
                private int sr;
                private int dfsId;
                private int bitrate;
                private int playTime;
                private double volumeDelta;

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public long getId() {
                    return id;
                }

                public void setId(long id) {
                    this.id = id;
                }

                public int getSize() {
                    return size;
                }

                public void setSize(int size) {
                    this.size = size;
                }

                public String getExtension() {
                    return extension;
                }

                public void setExtension(String extension) {
                    this.extension = extension;
                }

                public int getSr() {
                    return sr;
                }

                public void setSr(int sr) {
                    this.sr = sr;
                }

                public int getDfsId() {
                    return dfsId;
                }

                public void setDfsId(int dfsId) {
                    this.dfsId = dfsId;
                }

                public int getBitrate() {
                    return bitrate;
                }

                public void setBitrate(int bitrate) {
                    this.bitrate = bitrate;
                }

                public int getPlayTime() {
                    return playTime;
                }

                public void setPlayTime(int playTime) {
                    this.playTime = playTime;
                }

                public double getVolumeDelta() {
                    return volumeDelta;
                }

                public void setVolumeDelta(double volumeDelta) {
                    this.volumeDelta = volumeDelta;
                }
            }

            public static class ArtistsBeanX {
                /**
                 * name : 代码时间
                 * id : 0
                 * picId : 0
                 * img1v1Id : 0
                 * briefDesc :
                 * picUrl : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                 * img1v1Url : http://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg
                 * albumSize : 0
                 * alias : []
                 * trans :
                 * musicSize : 0
                 */

                private String name;
                private long id;
                private int picId;
                private int img1v1Id;
                private String briefDesc;
                private String picUrl;
                private String img1v1Url;
                private int albumSize;
                private String trans;
                private int musicSize;
                private List<?> alias;

                public String getName() {
                    return name;
                }

                public void setName(String name) {
                    this.name = name;
                }

                public long getId() {
                    return id;
                }

                public void setId(long id) {
                    this.id = id;
                }

                public int getPicId() {
                    return picId;
                }

                public void setPicId(int picId) {
                    this.picId = picId;
                }

                public int getImg1v1Id() {
                    return img1v1Id;
                }

                public void setImg1v1Id(int img1v1Id) {
                    this.img1v1Id = img1v1Id;
                }

                public String getBriefDesc() {
                    return briefDesc;
                }

                public void setBriefDesc(String briefDesc) {
                    this.briefDesc = briefDesc;
                }

                public String getPicUrl() {
                    return picUrl;
                }

                public void setPicUrl(String picUrl) {
                    this.picUrl = picUrl;
                }

                public String getImg1v1Url() {
                    return img1v1Url;
                }

                public void setImg1v1Url(String img1v1Url) {
                    this.img1v1Url = img1v1Url;
                }

                public int getAlbumSize() {
                    return albumSize;
                }

                public void setAlbumSize(int albumSize) {
                    this.albumSize = albumSize;
                }

                public String getTrans() {
                    return trans;
                }

                public void setTrans(String trans) {
                    this.trans = trans;
                }

                public int getMusicSize() {
                    return musicSize;
                }

                public void setMusicSize(int musicSize) {
                    this.musicSize = musicSize;
                }

                public List<?> getAlias() {
                    return alias;
                }

                public void setAlias(List<?> alias) {
                    this.alias = alias;
                }
            }
        }

        public static class DjBean {
            /**
             * defaultAvatar : false
             * province : 1000000
             * authStatus : 0
             * followed : false
             * avatarUrl : http://p1.music.126.net/BBK9kY31geOOdnkaxiwCdw==/****************.jpg
             * accountStatus : 0
             * gender : 1
             * city : 1002400
             * birthday : -*************
             * userId : *********
             * userType : 0
             * nickname : 代码时间
             * signature : 代码时间是一个面向程序员的中文播客节目, 致力于通过语音的方式传播程序员的正能量. 节目的网站是: http://codetimecn.com | 新浪微博 ID: 代码时间 | 微信公众号 ID: 代码时间
             * description :
             * detailDescription :
             * avatarImgId : ****************
             * backgroundImgId : ****************
             * backgroundUrl : http://p1.music.126.net/VTW4vsN08vwL3uSQqPyHqg==/****************.jpg
             * authority : 0
             * mutual : false
             * expertTags : null
             * experts : null
             * djStatus : 10
             * vipType : 0
             * remarkName : null
             * avatarImgIdStr : ****************
             * backgroundImgIdStr : ****************
             * brand : 代码时间
             */

            private boolean defaultAvatar;
            private int province;
            private int authStatus;
            private boolean followed;
            private String avatarUrl;
            private int accountStatus;
            private int gender;
            private int city;
            private long birthday;
            private int userId;
            private int userType;
            private String nickname;
            private String signature;
            private String description;
            private String detailDescription;
            private long avatarImgId;
            private long backgroundImgId;
            private String backgroundUrl;
            private int authority;
            private boolean mutual;
            private Object expertTags;
            private Object experts;
            private int djStatus;
            private int vipType;
            private Object remarkName;
            private String avatarImgIdStr;
            private String backgroundImgIdStr;
            private String brand;

            public boolean isDefaultAvatar() {
                return defaultAvatar;
            }

            public void setDefaultAvatar(boolean defaultAvatar) {
                this.defaultAvatar = defaultAvatar;
            }

            public int getProvince() {
                return province;
            }

            public void setProvince(int province) {
                this.province = province;
            }

            public int getAuthStatus() {
                return authStatus;
            }

            public void setAuthStatus(int authStatus) {
                this.authStatus = authStatus;
            }

            public boolean isFollowed() {
                return followed;
            }

            public void setFollowed(boolean followed) {
                this.followed = followed;
            }

            public String getAvatarUrl() {
                return avatarUrl;
            }

            public void setAvatarUrl(String avatarUrl) {
                this.avatarUrl = avatarUrl;
            }

            public int getAccountStatus() {
                return accountStatus;
            }

            public void setAccountStatus(int accountStatus) {
                this.accountStatus = accountStatus;
            }

            public int getGender() {
                return gender;
            }

            public void setGender(int gender) {
                this.gender = gender;
            }

            public int getCity() {
                return city;
            }

            public void setCity(int city) {
                this.city = city;
            }

            public long getBirthday() {
                return birthday;
            }

            public void setBirthday(long birthday) {
                this.birthday = birthday;
            }

            public int getUserId() {
                return userId;
            }

            public void setUserId(int userId) {
                this.userId = userId;
            }

            public int getUserType() {
                return userType;
            }

            public void setUserType(int userType) {
                this.userType = userType;
            }

            public String getNickname() {
                return nickname;
            }

            public void setNickname(String nickname) {
                this.nickname = nickname;
            }

            public String getSignature() {
                return signature;
            }

            public void setSignature(String signature) {
                this.signature = signature;
            }

            public String getDescription() {
                return description;
            }

            public void setDescription(String description) {
                this.description = description;
            }

            public String getDetailDescription() {
                return detailDescription;
            }

            public void setDetailDescription(String detailDescription) {
                this.detailDescription = detailDescription;
            }

            public long getAvatarImgId() {
                return avatarImgId;
            }

            public void setAvatarImgId(long avatarImgId) {
                this.avatarImgId = avatarImgId;
            }

            public long getBackgroundImgId() {
                return backgroundImgId;
            }

            public void setBackgroundImgId(long backgroundImgId) {
                this.backgroundImgId = backgroundImgId;
            }

            public String getBackgroundUrl() {
                return backgroundUrl;
            }

            public void setBackgroundUrl(String backgroundUrl) {
                this.backgroundUrl = backgroundUrl;
            }

            public int getAuthority() {
                return authority;
            }

            public void setAuthority(int authority) {
                this.authority = authority;
            }

            public boolean isMutual() {
                return mutual;
            }

            public void setMutual(boolean mutual) {
                this.mutual = mutual;
            }

            public Object getExpertTags() {
                return expertTags;
            }

            public void setExpertTags(Object expertTags) {
                this.expertTags = expertTags;
            }

            public Object getExperts() {
                return experts;
            }

            public void setExperts(Object experts) {
                this.experts = experts;
            }

            public int getDjStatus() {
                return djStatus;
            }

            public void setDjStatus(int djStatus) {
                this.djStatus = djStatus;
            }

            public int getVipType() {
                return vipType;
            }

            public void setVipType(int vipType) {
                this.vipType = vipType;
            }

            public Object getRemarkName() {
                return remarkName;
            }

            public void setRemarkName(Object remarkName) {
                this.remarkName = remarkName;
            }

            public String getAvatarImgIdStr() {
                return avatarImgIdStr;
            }

            public void setAvatarImgIdStr(String avatarImgIdStr) {
                this.avatarImgIdStr = avatarImgIdStr;
            }

            public String getBackgroundImgIdStr() {
                return backgroundImgIdStr;
            }

            public void setBackgroundImgIdStr(String backgroundImgIdStr) {
                this.backgroundImgIdStr = backgroundImgIdStr;
            }

            public String getBrand() {
                return brand;
            }

            public void setBrand(String brand) {
                this.brand = brand;
            }
        }

        public static class RadioBean {
            /**
             * dj : null
             * category : 科技科学
             * buyed : false
             * price : 0
             * originalPrice : 0
             * discountPrice : null
             * purchaseCount : 0
             * lastProgramName : null
             * videos : null
             * finished : false
             * underShelf : false
             * liveInfo : null
             * feeScope : 0
             * programCount : 36
             * subCount : 15111
             * radioFeeType : 0
             * lastProgramCreateTime : 1515962230660
             * lastProgramId : 1367665101
             * picUrl : https://p1.music.126.net/BBK9kY31geOOdnkaxiwCdw==/****************.jpg
             * desc :
             * categoryId : 453052
             * picId : ****************
             * createTime : 1465726085482
             * name : 代码时间
             * id : 336355127
             * subed : true
             */

            private Object dj;
            private String category;
            private boolean buyed;
            private int price;
            private int originalPrice;
            private Object discountPrice;
            private int purchaseCount;
            private Object lastProgramName;
            private Object videos;
            private boolean finished;
            private boolean underShelf;
            private Object liveInfo;
            private int feeScope;
            private int programCount;
            private int subCount;
            private int radioFeeType;
            private long lastProgramCreateTime;
            private int lastProgramId;
            private String picUrl;
            private String desc;
            private int categoryId;
            private long picId;
            private long createTime;
            private String name;
            private long id;
            private boolean subed;

            public Object getDj() {
                return dj;
            }

            public void setDj(Object dj) {
                this.dj = dj;
            }

            public String getCategory() {
                return category;
            }

            public void setCategory(String category) {
                this.category = category;
            }

            public boolean isBuyed() {
                return buyed;
            }

            public void setBuyed(boolean buyed) {
                this.buyed = buyed;
            }

            public int getPrice() {
                return price;
            }

            public void setPrice(int price) {
                this.price = price;
            }

            public int getOriginalPrice() {
                return originalPrice;
            }

            public void setOriginalPrice(int originalPrice) {
                this.originalPrice = originalPrice;
            }

            public Object getDiscountPrice() {
                return discountPrice;
            }

            public void setDiscountPrice(Object discountPrice) {
                this.discountPrice = discountPrice;
            }

            public int getPurchaseCount() {
                return purchaseCount;
            }

            public void setPurchaseCount(int purchaseCount) {
                this.purchaseCount = purchaseCount;
            }

            public Object getLastProgramName() {
                return lastProgramName;
            }

            public void setLastProgramName(Object lastProgramName) {
                this.lastProgramName = lastProgramName;
            }

            public Object getVideos() {
                return videos;
            }

            public void setVideos(Object videos) {
                this.videos = videos;
            }

            public boolean isFinished() {
                return finished;
            }

            public void setFinished(boolean finished) {
                this.finished = finished;
            }

            public boolean isUnderShelf() {
                return underShelf;
            }

            public void setUnderShelf(boolean underShelf) {
                this.underShelf = underShelf;
            }

            public Object getLiveInfo() {
                return liveInfo;
            }

            public void setLiveInfo(Object liveInfo) {
                this.liveInfo = liveInfo;
            }

            public int getFeeScope() {
                return feeScope;
            }

            public void setFeeScope(int feeScope) {
                this.feeScope = feeScope;
            }

            public int getProgramCount() {
                return programCount;
            }

            public void setProgramCount(int programCount) {
                this.programCount = programCount;
            }

            public int getSubCount() {
                return subCount;
            }

            public void setSubCount(int subCount) {
                this.subCount = subCount;
            }

            public int getRadioFeeType() {
                return radioFeeType;
            }

            public void setRadioFeeType(int radioFeeType) {
                this.radioFeeType = radioFeeType;
            }

            public long getLastProgramCreateTime() {
                return lastProgramCreateTime;
            }

            public void setLastProgramCreateTime(long lastProgramCreateTime) {
                this.lastProgramCreateTime = lastProgramCreateTime;
            }

            public int getLastProgramId() {
                return lastProgramId;
            }

            public void setLastProgramId(int lastProgramId) {
                this.lastProgramId = lastProgramId;
            }

            public String getPicUrl() {
                return picUrl;
            }

            public void setPicUrl(String picUrl) {
                this.picUrl = picUrl;
            }

            public String getDesc() {
                return desc;
            }

            public void setDesc(String desc) {
                this.desc = desc;
            }

            public int getCategoryId() {
                return categoryId;
            }

            public void setCategoryId(int categoryId) {
                this.categoryId = categoryId;
            }

            public long getPicId() {
                return picId;
            }

            public void setPicId(long picId) {
                this.picId = picId;
            }

            public long getCreateTime() {
                return createTime;
            }

            public void setCreateTime(long createTime) {
                this.createTime = createTime;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public long getId() {
                return id;
            }

            public void setId(long id) {
                this.id = id;
            }

            public boolean isSubed() {
                return subed;
            }

            public void setSubed(boolean subed) {
                this.subed = subed;
            }
        }
    }
}
