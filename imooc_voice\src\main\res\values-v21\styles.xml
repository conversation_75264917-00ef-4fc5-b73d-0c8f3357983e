<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowTranslucentStatus">false</item>
        <!--<item name="android:windowTranslucentNavigation">true</item>-->
        <!--&lt;!&ndash;Android 5.x开始需要把颜色设置透明，否则导航栏会呈现系统默认的浅灰色&ndash;&gt;-->
        <!--&lt;!&ndash; 可以修改状态栏的颜色 -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/white</item>
        <item name="android:fitsSystemWindows">true</item>
        <!--允许activity的transition转场动画-->
        <item name="android:windowActivityTransitions">true</item>
    </style>
    <style name="anim_panel_up_from_bottom" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/push_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/push_bottom_out</item>
    </style>
</resources>


