<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:background="@color/white"
    android:paddingLeft="10dp">

    <ImageView
        android:id="@+id/play_state"
        android:layout_width="15dp"
        android:layout_height="25dp"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:src="@drawable/ic_status_play"
        android:scaleType="centerInside"
        android:visibility="gone" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_toStartOf="@+id/viewpager_list_button"
        android:layout_toLeftOf="@+id/viewpager_list_button"
        android:layout_toEndOf="@id/play_state"
        android:layout_toRightOf="@+id/play_state"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="6dp">

        <TextView
            android:id="@+id/viewpager_list_toptext"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:singleLine="true"
            android:text="被驯服的象"
            android:textColor="@color/listTextColor"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/viewpager_list_bottom_text"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:ellipsize="end"
            android:maxLength="20"
            android:maxLines="1"
            android:singleLine="true"
            android:text="蔡健雅"
            android:textColor="@color/listSubTextColor"
            android:textSize="11sp" />

    </LinearLayout>

    <ImageView
        android:id="@+id/viewpager_list_button"
        android:layout_width="35dp"
        android:layout_height="match_parent"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:focusable="true"
        android:src="@drawable/ic_song_more" />
</RelativeLayout>
