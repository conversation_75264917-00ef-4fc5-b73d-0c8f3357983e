apply plugin: 'com.android.application'
apply plugin: 'com.jakewharton.butterknife'
android {
    compileSdkVersion rootProject.android.compileSdkVersion
    buildToolsVersion rootProject.android.buildToolsVersion

    defaultConfig {
        applicationId rootProject.android.applicationId
        minSdkVersion rootProject.android.minSdkVersion
        targetSdkVersion rootProject.android.targetSdkVersion
        versionCode rootProject.android.versionCode
        versionName rootProject.android.versionName
        multiDexEnabled rootProject.android.multiDexEnabled
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    compile ('com.github.niorgai:StatusBarCompat:2.1.4', {
        exclude group: 'com.android.support'
    })
    implementation this.rootProject.depsLibs.appcompat
    implementation this.rootProject.depsLibs.design
    implementation this.rootProject.depsLibs.cardview
    implementation this.rootProject.depsLibs.recyclerview

    implementation this.rootProject.depsLibs.videoplayer
    implementation this.rootProject.depsLibs.multidex
    implementation this.rootProject.depsLibs.BaseRecyclerViewAdapterHelper
    implementation this.rootProject.depsLibs.tinypinyin
    implementation this.rootProject.depsLibs.xpopup
    implementation this.rootProject.depsLibs.easypermission
    //RecyclerView 多布局分组Adapter
    implementation this.rootProject.depsLibs.GroupedRecyclerViewAdapter
    implementation this.rootProject.depsLibs.magicindicator
    //eventbus类库依赖
    implementation this.rootProject.depsLibs.eventbus
    //greendao类库依赖
    implementation this.rootProject.depsLibs.greendao
    //rxjava库
    implementation this.rootProject.depsLibs.rxjava
    implementation this.rootProject.depsLibs.rxandroid

    annotationProcessor this.rootProject.depsLibs.butterknife_compiler

    implementation project(path: ':lib_common_ui')
    implementation project(path: ':lib_image_loader')
    implementation project(path: ':lib_qrcode')
    implementation project(path: ':lib_webview')
    implementation project(path: ':lib_audio')
    implementation project(path: ':lib_video')
    implementation project(path: ':lib_update')
    implementation project(path: ':lib_api')
    implementation project(path: ':lib_network')
    implementation project(path: ':lib_pullalive')
    implementation project(path: ':lib_share')
}
