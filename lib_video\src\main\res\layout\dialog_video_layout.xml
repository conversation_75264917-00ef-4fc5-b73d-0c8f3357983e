<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <RelativeLayout
        android:id="@+id/content_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center" />

    <Button
        android:id="@+id/xadsdk_big_play_btn"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_centerInParent="true"
        android:background="@drawable/xadsdk_ad_play"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/loading_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/xadsdk_ad_loading_anim"
        android:visibility="gone" />
</RelativeLayout>